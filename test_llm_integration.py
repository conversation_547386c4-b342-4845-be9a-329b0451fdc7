#!/usr/bin/env python3
"""
Test script to verify LLM integration is working.
"""

import asyncio
import sys
import os

# Add the bridge directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'bridge'))

async def test_llm_client():
    """Test the LLM client directly."""
    try:
        from bridge.integrations.llm_client import llm_client
        
        print("🧪 Testing LLM Client Integration...")
        print("=" * 50)
        
        # Test simple prompt
        test_prompt = "Write a simple 'Hello, World!' program in Python."
        
        print(f"📝 Prompt: {test_prompt}")
        print("🤖 LLM Response:")
        print("-" * 30)
        
        response = await llm_client.generate_response(test_prompt)
        print(response)
        
        print("\n" + "=" * 50)
        print("✅ LLM Integration Test Completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing LLM client: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_chat_manager():
    """Test the chat manager with LLM integration."""
    try:
        from bridge.integrations.chat_manager import chat_manager
        
        print("\n🧪 Testing Chat Manager with LLM...")
        print("=" * 50)
        
        # Create a test session
        session_id = chat_manager.create_session("LLM Test Session")
        print(f"📋 Created session: {session_id}")
        
        # Send a test message
        test_message = "Create a simple Java calculator class"
        print(f"📝 Message: {test_message}")
        print("🤖 Chat Response:")
        print("-" * 30)
        
        response = await chat_manager.send_message_sync(session_id, test_message)
        print(response)
        
        print("\n" + "=" * 50)
        print("✅ Chat Manager Test Completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing chat manager: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting LLM Integration Tests...")
    print("=" * 60)
    
    # Test 1: Direct LLM client
    llm_success = await test_llm_client()
    
    # Test 2: Chat manager
    chat_success = await test_chat_manager()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   LLM Client: {'✅ PASS' if llm_success else '❌ FAIL'}")
    print(f"   Chat Manager: {'✅ PASS' if chat_success else '❌ FAIL'}")
    
    if llm_success and chat_success:
        print("\n🎉 All tests passed! LLM integration is working correctly.")
        return 0
    else:
        print("\n💥 Some tests failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
