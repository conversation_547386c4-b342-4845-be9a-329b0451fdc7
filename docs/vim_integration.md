# Vim Integration Guide

This guide explains how to use the AI Coding Agent with the Vim plugin.

## Prerequisites

- Python 3.8 or higher
- Vim 9.1 or higher, or Neovim
- Node.js 19 or higher (required by the Vim plugin)

## Setup

1. Start the AI Coding Agent bridge:

   ```bash
   ./start_bridge.sh
   ```

   This will:
   - Create a Python virtual environment if it doesn't exist
   - Install the required dependencies
   - Start the bridge server

   The bridge server will start both the API server (port 8080) and the Vim integration server (port 8081).

2. Configure the Vim plugin:

   Add the following to your `.vimrc` or `init.vim`:

   ```vim
   " AI Coding Agent configuration
   let g:ai_coding_agent_host = 'localhost'
   let g:ai_coding_agent_port = 8081
   let g:ai_coding_agent_python = 'python3'  " Path to Python executable
   let g:ai_coding_agent_bridge_path = '/path/to/AI-Coding-Agent'  " Update this path
   ```

## Usage

The Vim plugin provides the following commands:

### Run AI Coding Agent

```vim
:AICodingAgentRun "Your task description here"
```

This will send your task to the AI Coding Agent, which will use the SWE-Agent to solve it.

Example:
```vim
:AICodingAgentRun "Create a function to calculate the Fibonacci sequence"
```

### Stop AI Coding Agent

```vim
:AICodingAgentStop
```

This will stop the current AI Coding Agent task.

### Check Bridge Status

```vim
:AICodingAgentPing
```

This will check if the bridge is running and responding.

## Troubleshooting

If you encounter issues:

1. Check if the bridge is running:
   ```vim
   :AICodingAgentPing
   ```

2. Look at the bridge logs:
   ```bash
   cat logs/bridge.log
   ```

3. Ensure the paths in your Vim configuration are correct:
   ```vim
   :echo g:ai_coding_agent_bridge_path
   ```

4. Make sure the required dependencies are installed:
   ```bash
   pip install -r requirements.txt
   ```

## Advanced Configuration

You can customize the bridge configuration by creating a YAML file:

```yaml
swe_agent:
  api_port: 8000
  api_host: localhost
bridge:
  api_port: 8080
  api_host: localhost
logging:
  level: INFO
  file: logs/bridge.log
```

Then start the bridge with:

```bash
./start_bridge.sh --config your_config.yaml
```
