#!/usr/bin/env python3
"""
Comprehensive test script for SWE-Agent tool integration.
Tests all aspects of Phase 1 implementation including security, API endpoints, and tool execution.
"""

import asyncio
import json
import logging
import requests
import sys
import time
from typing import Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
BRIDGE_HOST = "localhost"
BRIDGE_PORT = 8080
BASE_URL = f"http://{BRIDGE_HOST}:{BRIDGE_PORT}"

class SWEToolsIntegrationTester:
    """Comprehensive tester for SWE-Agent tools integration."""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.test_results = {}
        
    def run_all_tests(self):
        """Run all integration tests."""
        logger.info("🚀 Starting SWE-Agent Tools Integration Tests")
        logger.info("=" * 60)
        
        tests = [
            ("Bridge Health Check", self.test_bridge_health),
            ("SWE Tools Health Check", self.test_swe_tools_health),
            ("List Available Tools", self.test_list_tools),
            ("Tool Schema Validation", self.test_tool_schemas),
            ("Parameter Validation", self.test_parameter_validation),
            ("Security Filtering", self.test_security_filtering),
            ("Tool Execution", self.test_tool_execution),
            ("Agent Tools Integration", self.test_agent_tools_integration),
            ("Error Handling", self.test_error_handling),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n📋 Running: {test_name}")
            try:
                result = test_func()
                self.test_results[test_name] = {"status": "PASS", "result": result}
                logger.info(f"✅ {test_name}: PASSED")
            except Exception as e:
                self.test_results[test_name] = {"status": "FAIL", "error": str(e)}
                logger.error(f"❌ {test_name}: FAILED - {e}")
        
        self.print_summary()
    
    def test_bridge_health(self):
        """Test bridge health endpoint."""
        response = requests.get(f"{self.base_url}/health", timeout=5)
        assert response.status_code == 200, f"Health check failed: {response.status_code}"
        
        data = response.json()
        assert data.get("status") == "ok", f"Health status not ok: {data}"
        
        return {"status": "Bridge is healthy"}
    
    def test_swe_tools_health(self):
        """Test SWE tools health endpoint."""
        response = requests.get(f"{self.base_url}/api/swe-tools/health", timeout=5)
        assert response.status_code == 200, f"SWE tools health check failed: {response.status_code}"
        
        data = response.json()
        assert data.get("status") == "healthy", f"SWE tools not healthy: {data}"
        
        return {
            "tools_available": data.get("tools_available", 0),
            "swe_env_available": data.get("swe_env_available", False),
            "tools_loaded": data.get("tools_loaded", False)
        }
    
    def test_list_tools(self):
        """Test listing available SWE-Agent tools."""
        response = requests.get(f"{self.base_url}/api/swe-tools/list", timeout=10)
        assert response.status_code == 200, f"List tools failed: {response.status_code}"
        
        data = response.json()
        assert data.get("status") == "success", f"List tools status not success: {data}"
        
        tools = data.get("tools", {})
        assert len(tools) > 0, "No tools found"
        
        # Check for expected tools
        expected_tools = ["str_replace_editor", "goto", "open", "create", "find_file", "search_file"]
        found_tools = list(tools.keys())
        
        logger.info(f"Found {len(found_tools)} tools: {found_tools}")
        
        return {
            "total_tools": len(found_tools),
            "tools": found_tools,
            "expected_found": [tool for tool in expected_tools if tool in found_tools]
        }
    
    def test_tool_schemas(self):
        """Test tool schema retrieval."""
        # First get list of tools
        response = requests.get(f"{self.base_url}/api/swe-tools/list", timeout=10)
        assert response.status_code == 200
        
        tools = response.json().get("tools", {})
        assert len(tools) > 0, "No tools available for schema testing"
        
        # Test schema for first few tools
        tested_schemas = {}
        for tool_name in list(tools.keys())[:3]:  # Test first 3 tools
            schema_response = requests.get(f"{self.base_url}/api/swe-tools/{tool_name}/schema", timeout=5)
            assert schema_response.status_code == 200, f"Schema retrieval failed for {tool_name}"
            
            schema_data = schema_response.json()
            assert schema_data.get("status") == "success", f"Schema status not success for {tool_name}"
            
            schema = schema_data.get("schema", {})
            assert "function" in schema, f"Schema missing function definition for {tool_name}"
            assert "name" in schema["function"], f"Schema missing function name for {tool_name}"
            
            tested_schemas[tool_name] = schema["function"]["name"]
        
        return {"tested_schemas": tested_schemas}
    
    def test_parameter_validation(self):
        """Test parameter validation."""
        # Test valid parameters
        valid_test = {
            "tool_name": "str_replace_editor",
            "parameters": {
                "command": "view",
                "path": "/testbed/test.py"
            }
        }
        
        response = requests.post(f"{self.base_url}/api/swe-tools/validate", 
                               json=valid_test, timeout=5)
        
        # Note: This might return 400 if tool validation fails due to missing SWE environment
        # We'll check for proper error handling
        if response.status_code == 200:
            data = response.json()
            assert data.get("status") == "valid", f"Valid parameters rejected: {data}"
            valid_result = "Parameters validated successfully"
        else:
            # Check if it's a proper validation error
            data = response.json()
            valid_result = f"Validation returned {response.status_code}: {data.get('error', 'Unknown error')}"
        
        # Test invalid parameters
        invalid_test = {
            "tool_name": "str_replace_editor",
            "parameters": {
                "command": "invalid_command",
                "path": "../../../etc/passwd"  # Path traversal attempt
            }
        }
        
        response = requests.post(f"{self.base_url}/api/swe-tools/validate", 
                               json=invalid_test, timeout=5)
        assert response.status_code == 400, f"Invalid parameters not rejected: {response.status_code}"
        
        data = response.json()
        assert data.get("status") == "invalid", f"Invalid status not returned: {data}"
        
        return {
            "valid_test": valid_result,
            "invalid_test": "Invalid parameters properly rejected"
        }
    
    def test_security_filtering(self):
        """Test security filtering mechanisms."""
        security_tests = [
            {
                "name": "Path Traversal",
                "tool_name": "str_replace_editor",
                "parameters": {"command": "view", "path": "../../etc/passwd"}
            },
            {
                "name": "Dangerous Command",
                "tool_name": "bash",
                "parameters": {"command": "rm -rf /"}
            },
            {
                "name": "Long String",
                "tool_name": "str_replace_editor", 
                "parameters": {"command": "create", "file_text": "x" * 200000}
            }
        ]
        
        results = {}
        for test in security_tests:
            response = requests.post(f"{self.base_url}/api/swe-tools/validate",
                                   json=test, timeout=5)
            
            # All these should be rejected (400 status)
            if response.status_code == 400:
                results[test["name"]] = "BLOCKED (Good)"
            else:
                results[test["name"]] = f"NOT BLOCKED (Bad) - Status: {response.status_code}"
        
        return results
    
    def test_tool_execution(self):
        """Test actual tool execution."""
        # Test a simple tool execution
        execution_test = {
            "tool_name": "str_replace_editor",
            "parameters": {
                "command": "view",
                "path": "/tmp"  # Safe path
            },
            "timeout": 10
        }
        
        response = requests.post(f"{self.base_url}/api/swe-tools/execute",
                               json=execution_test, timeout=15)
        
        # Note: This might fail if SWE environment is not properly set up
        # We'll check for proper error handling
        if response.status_code == 200:
            data = response.json()
            return {
                "status": "Tool executed successfully",
                "success": data.get("success", False),
                "output_length": len(data.get("output", "")),
                "execution_time": data.get("execution_time", 0)
            }
        else:
            data = response.json() if response.headers.get('content-type', '').startswith('application/json') else {}
            return {
                "status": f"Tool execution returned {response.status_code}",
                "error": data.get("error", response.text),
                "note": "This may be expected if SWE environment is not fully configured"
            }
    
    def test_agent_tools_integration(self):
        """Test integration with agent_tools module."""
        try:
            from bridge.core.agent_tools import agent_tools
            
            # Test listing SWE tools through agent_tools
            result = agent_tools.list_swe_tools()
            
            return {
                "agent_tools_available": True,
                "list_swe_tools_success": result.success,
                "list_swe_tools_data": str(result.data)[:100] + "..." if result.data else None,
                "list_swe_tools_error": result.error if not result.success else None
            }
            
        except ImportError as e:
            return {
                "agent_tools_available": False,
                "import_error": str(e)
            }
    
    def test_error_handling(self):
        """Test error handling scenarios."""
        error_tests = [
            {
                "name": "Missing tool name",
                "endpoint": "/api/swe-tools/execute",
                "data": {"parameters": {}}
            },
            {
                "name": "Non-existent tool",
                "endpoint": "/api/swe-tools/nonexistent/schema",
                "data": None
            },
            {
                "name": "Invalid JSON",
                "endpoint": "/api/swe-tools/validate",
                "data": "invalid json"
            }
        ]
        
        results = {}
        for test in error_tests:
            try:
                if test["data"] is None:
                    response = requests.get(f"{self.base_url}{test['endpoint']}", timeout=5)
                elif isinstance(test["data"], str):
                    response = requests.post(f"{self.base_url}{test['endpoint']}", 
                                           data=test["data"], timeout=5)
                else:
                    response = requests.post(f"{self.base_url}{test['endpoint']}", 
                                           json=test["data"], timeout=5)
                
                results[test["name"]] = {
                    "status_code": response.status_code,
                    "has_error_message": "error" in response.text.lower()
                }
                
            except Exception as e:
                results[test["name"]] = {"exception": str(e)}
        
        return results
    
    def print_summary(self):
        """Print test summary."""
        logger.info("\n" + "=" * 60)
        logger.info("📊 TEST SUMMARY")
        logger.info("=" * 60)
        
        passed = sum(1 for result in self.test_results.values() if result["status"] == "PASS")
        total = len(self.test_results)
        
        logger.info(f"Total Tests: {total}")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {total - passed}")
        logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
        
        logger.info("\n📋 Detailed Results:")
        for test_name, result in self.test_results.items():
            status_emoji = "✅" if result["status"] == "PASS" else "❌"
            logger.info(f"{status_emoji} {test_name}: {result['status']}")
            
            if result["status"] == "FAIL":
                logger.info(f"   Error: {result['error']}")
            elif "result" in result:
                logger.info(f"   Result: {result['result']}")
        
        logger.info("\n" + "=" * 60)
        
        if passed == total:
            logger.info("🎉 ALL TESTS PASSED! SWE-Agent tool integration is working correctly.")
        else:
            logger.info("⚠️  Some tests failed. Check the errors above for details.")
        
        return passed == total


def main():
    """Main test execution."""
    logger.info("SWE-Agent Tools Integration Test Suite")
    logger.info("Testing Phase 1 implementation...")
    
    tester = SWEToolsIntegrationTester()
    
    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("\n⏹️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Test suite failed with exception: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
