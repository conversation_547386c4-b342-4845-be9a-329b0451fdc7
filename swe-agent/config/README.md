* Default config: `anthropic_filemap.yaml`
* `swebench_submissions`: Configs that were used for swebench submissions
* `sweagent_0_7`: Configs from SWE-agent 0.7, similar to the one used in the paper
* `exotic`: Various specific configurations that might be more of niche interest
* `human`: Demo/debug configs that have the human type commands and run without a LM
* Configs for running with <PERSON><PERSON><PERSON>-<PERSON>mith are at https://github.com/SWE-bench/SWE-smith/blob/main/agent/swesmith_infer.yaml

🔗 For more information on config files, visit [our documentation website][docs].

You can also find the corresponding markdown files in the [`docs/` folder][source].

[docs]: https://swe-agent.com/latest/config/config
[source]: https://github.com/SWE-agent/SWE-agent/tree/main/docs
