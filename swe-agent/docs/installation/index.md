# Setting up SWE-agent

<div class="grid cards" markdown>


-   :material-cog:{ .lg .middle } __Install from source__

    ---

    Install SWE-agent locally from source using `pip`.
    This is the default option.

    [:octicons-arrow-right-24: Get started](source.md)


-   :material-github:{ .lg .middle } __All in browser__

    ---

    Run SWE-agent using GitHub codespaces in an in-browser VSCode environment.
    Best for a quick first peek.

    [:octicons-arrow-right-24: Get started](codespaces.md)


-   :material-docker:{ .lg .middle } __Coming soon: Run in docker__

    ---

    Pull a docker container and directly run SWE-agent. This is our fallback solution if the local installation does not work for you.

    :octicons-arrow-right-24: Coming soon

-   :material-newspaper:{ .lg .middle } __Changelog__

    ---

    See what's new in SWE-agent!

    [:octicons-arrow-right-24: Read the changelog](changelog.md)
</div>
