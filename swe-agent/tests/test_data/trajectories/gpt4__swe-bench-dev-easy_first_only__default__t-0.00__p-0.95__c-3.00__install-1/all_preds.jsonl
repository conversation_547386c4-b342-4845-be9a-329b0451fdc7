{"model_name_or_path": "gpt4__swe-bench-dev-easy_first_only__default__t-0.00__p-0.95__c-3.00__install-1", "instance_id": "pydicom__pydicom-1458", "model_patch": "\ndiff --git a/pydicom/pixel_data_handlers/numpy_handler.py b/pydicom/pixel_data_handlers/numpy_handler.py\nindex 8e8d319ae..6a1221b72 100644\n--- a/pydicom/pixel_data_handlers/numpy_handler.py\n+++ b/pydicom/pixel_data_handlers/numpy_handler.py\n@@ -285,9 +285,10 @@ def get_pixeldata(ds: \"Dataset\", read_only: bool = False) -> \"np.ndarray\":\n         )\n \n     required_elements = [\n-        'BitsAllocated', 'Rows', 'Columns', 'PixelRepresentation',\n-        'SamplesPerPixel', 'PhotometricInterpretation'\n+        'BitsAllocated', 'Rows', 'Columns', 'SamplesPerPixel', 'PhotometricInterpretation'\n     ]\n+    if 'PixelData' in ds:\n+        required_elements.append('PixelRepresentation')\n     missing = [elem for elem in required_elements if elem not in ds]\n     if missing:\n         raise AttributeError(\n"}