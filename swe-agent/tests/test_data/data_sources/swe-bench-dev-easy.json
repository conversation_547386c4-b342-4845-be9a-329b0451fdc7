[{"repo": "pydicom/pydicom", "instance_id": "pydicom__pydicom-1458", "base_commit": "8da0b9b215ebfad5756051c891def88e426787e7", "patch": "diff --git a/pydicom/pixel_data_handlers/numpy_handler.py b/pydicom/pixel_data_handlers/numpy_handler.py\n--- a/pydicom/pixel_data_handlers/numpy_handler.py\n+++ b/pydicom/pixel_data_handlers/numpy_handler.py\n@@ -43,7 +43,9 @@\n | (0028,0100) | BitsAllocated             | 1    | 1, 8, 16, 32, | Required |\n |             |                           |      | 64            |          |\n +-------------+---------------------------+------+---------------+----------+\n-| (0028,0103) | PixelRepresentation       | 1    | 0, 1          | Required |\n+| (0028,0101) | BitsStored                | 1    | 1, 8, 12, 16  | Optional |\n++-------------+---------------------------+------+---------------+----------+\n+| (0028,0103) | PixelRepresentation       | 1C   | 0, 1          | Optional |\n +-------------+---------------------------+------+---------------+----------+\n \n \"\"\"\n@@ -284,16 +286,28 @@ def get_pixeldata(ds: \"Dataset\", read_only: bool = False) -> \"np.ndarray\":\n             \"the dataset\"\n         )\n \n+    # Attributes required by both Floating Point Image Pixel Module Attributes\n+    # and Image Pixel Description Macro Attributes\n     required_elements = [\n-        'BitsAllocated', 'Rows', 'Columns', 'PixelRepresentation',\n+        'BitsAllocated', 'Rows', 'Columns',\n         'SamplesPerPixel', 'PhotometricInterpretation'\n     ]\n+    if px_keyword[0] == 'PixelData':\n+        # Attributess required by Image Pixel Description Macro Attributes\n+        required_elements.extend(['PixelRepresentation', 'BitsStored'])\n     missing = [elem for elem in required_elements if elem not in ds]\n     if missing:\n         raise AttributeError(\n             \"Unable to convert the pixel data as the following required \"\n             \"elements are missing from the dataset: \" + \", \".join(missing)\n         )\n+    if ds.SamplesPerPixel > 1:\n+        if not hasattr(ds, 'PlanarConfiguration'):\n+            raise AttributeError(\n+                \"Unable to convert the pixel data as the following \"\n+                \"conditionally required element is missing from the dataset: \"\n+                \"PlanarConfiguration\"\n+            )\n \n     # May be Pixel Data, Float Pixel Data or Double Float Pixel Data\n     pixel_data = getattr(ds, px_keyword[0])\n", "test_patch": "diff --git a/pydicom/tests/test_numpy_pixel_data.py b/pydicom/tests/test_numpy_pixel_data.py\n--- a/pydicom/tests/test_numpy_pixel_data.py\n+++ b/pydicom/tests/test_numpy_pixel_data.py\n@@ -26,6 +26,8 @@\n * PlanarConfiguration\n \"\"\"\n \n+from copy import deepcopy\n+\n import pytest\n \n from pydicom import config\n@@ -1068,6 +1070,7 @@ def test_endianness_not_set(self):\n         ds.Rows = 10\n         ds.Columns = 10\n         ds.BitsAllocated = 16\n+        ds.BitsStored = 16\n         ds.PixelRepresentation = 0\n         ds.SamplesPerPixel = 1\n         ds.PhotometricInterpretation = 'MONOCHROME2'\n@@ -1105,16 +1108,60 @@ def test_no_pixel_data_raises(self):\n         with pytest.raises(AttributeError, match=msg):\n             get_pixeldata(ds)\n \n-    def test_missing_required_elem(self):\n+    def test_missing_required_elem_pixel_data_monochrome(self):\n         \"\"\"Tet get_pixeldata raises if dataset missing required element.\"\"\"\n-        ds = dcmread(EXPL_16_1_1F)\n-        del ds.BitsAllocated\n+        required_attrs = (\n+            'BitsAllocated',\n+            'BitsStored',\n+            'Rows',\n+            'Columns',\n+            'SamplesPerPixel',\n+            'PhotometricInterpretation',\n+            'PixelRepresentation',\n+        )\n+        for attr in required_attrs:\n+            ds = dcmread(EXPL_16_1_1F)\n+            delattr(ds, attr)\n+            msg = (\n+                r\"Unable to convert the pixel data as the following required \"\n+                r\"elements are missing from the dataset: {}\".format(attr)\n+            )\n+            with pytest.raises(AttributeError, match=msg):\n+                get_pixeldata(ds)\n+\n+    def test_missing_required_elem_pixel_data_color(self):\n+        \"\"\"Tet get_pixeldata raises if dataset missing required element.\"\"\"\n+        ds = dcmread(EXPL_8_3_1F)\n+        del ds.Rows\n+        del ds.Columns\n+        msg = (\n+            r\"Unable to convert the pixel data as the following required \"\n+            r\"elements are missing from the dataset: Rows, Columns\"\n+        )\n+        with pytest.raises(AttributeError, match=msg):\n+            get_pixeldata(ds)\n+\n+    def test_missing_conditionally_required_elem_pixel_data_color(self):\n+        \"\"\"Tet get_pixeldata raises if dataset missing required element.\"\"\"\n+        ds = dcmread(EXPL_8_3_1F)\n+        del ds.PlanarConfiguration\n+        msg = (\n+            r\"Unable to convert the pixel data as the following conditionally \"\n+            r\"required element is missing from the dataset: \"\n+            r\"PlanarConfiguration\"\n+        )\n+        with pytest.raises(AttributeError, match=msg):\n+            get_pixeldata(ds)\n+\n+    def test_missing_required_elem_float_pixel_data_monochrome(self):\n+        \"\"\"Tet get_pixeldata raises if dataset missing required element.\"\"\"\n+        ds = dcmread(IMPL_32_1_1F)\n+        ds.FloatPixelData = ds.PixelData\n+        del ds.PixelData\n         del ds.Rows\n-        del ds.SamplesPerPixel\n         msg = (\n             r\"Unable to convert the pixel data as the following required \"\n-            r\"elements are missing from the dataset: BitsAllocated, Rows, \"\n-            r\"SamplesPerPixel\"\n+            r\"elements are missing from the dataset: Rows\"\n         )\n         with pytest.raises(AttributeError, match=msg):\n             get_pixeldata(ds)\n", "problem_statement": "Pixel Representation attribute should be optional for pixel data handler\n**Describe the bug**\r\nThe NumPy pixel data handler currently [requires the Pixel Representation attribute](https://github.com/pydicom/pydicom/blob/8da0b9b215ebfad5756051c891def88e426787e7/pydicom/pixel_data_handlers/numpy_handler.py#L46). This is problematic, because in case of Float Pixel Data or Double Float Pixel Data the attribute shall be absent. Compare [Floating Point Image Pixel Module Attributes](http://dicom.nema.org/medical/dicom/current/output/chtml/part03/sect_C.7.6.24.html) versus [Image Pixel Description Macro Attributes](http://dicom.nema.org/medical/dicom/current/output/chtml/part03/sect_C.7.6.3.html#table_C.7-11c)\r\n\r\n**Expected behavior**\r\nI would expect the `Dataset.pixel_array` property to be able to decode a Float Pixel Data or Double Float Pixel Data element without presence of the Pixel Representation element in the metadata.\r\n\r\n**Steps To Reproduce**\r\n```python\r\nimport numpy as np\r\nfrom pydicom.dataset import Dataset, FileMetaDataset\r\n\r\n\r\nds = Dataset()\r\nds.file_meta = FileMetaDataset()\r\nds.file_meta.TransferSyntaxUID = '1.2.840.10008.1.2.1'\r\n\r\nds.BitsAllocated = 32\r\nds.SamplesPerPixel = 1\r\nds.Rows = 5\r\nds.Columns = 5\r\nds.PhotometricInterpretation = 'MONOCHROME2'\r\n\r\npixel_array = np.zeros((ds.Rows, ds.Columns), dtype=np.float32)\r\nds.FloatPixelData = pixel_array.flatten().tobytes()\r\n\r\nnp.array_equal(ds.pixel_array, pixel_array)\r\n```\n", "hints_text": "", "created_at": "2021-08-04T15:22:07Z", "version": "2.2", "FAIL_TO_PASS": ["pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_required_elem_pixel_data_monochrome", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_conditionally_required_elem_pixel_data_color"], "PASS_TO_PASS": ["pydicom/tests/test_numpy_pixel_data.py::test_unsupported_syntaxes", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_environment", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_supported_dataset", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/SC_rgb_jpeg_dcmtk.dcm-data0]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/JPEG-lossy.dcm-data1]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/SC_rgb_jpeg_gdcm.dcm-data2]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small_jpeg_ls_lossless.dcm-data3]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/guest/jy1682/.pydicom/data/emri_small_jpeg_2k_lossless.dcm-data4]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/JPEG2000.dcm-data5]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small_RLE.dcm-data6]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_pixel_array_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_environment", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_unsupported_syntax_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_dataset_pixel_array_handler_needs_convert", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_dataset_pixel_array_no_pixels", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/SC_rgb_jpeg_dcmtk.dcm-data0]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/JPEG-lossy.dcm-data1]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/SC_rgb_jpeg_gdcm.dcm-data2]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small_jpeg_ls_lossless.dcm-data3]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/guest/jy1682/.pydicom/data/emri_small_jpeg_2k_lossless.dcm-data4]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/JPEG2000.dcm-data5]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small_RLE.dcm-data6]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_pixel_array_8bit_un_signed", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_decompress_using_handler[numpy]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_decompress_using_handler[NumPy]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_decompress_using_handler[np]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_decompress_using_handler[np_handler]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_decompress_using_handler[numpy_handler]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_pixel_array_16bit_un_signed", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_pixel_array_32bit_un_signed", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_8bit_1sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_8bit_1sample_2frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_8bit_3sample_1frame_odd_size", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_8bit_3sample_1frame_ybr422", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_8bit_3sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_8bit_3sample_2frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/liver_1frame.dcm-data0]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/liver.dcm-data1]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/OBXXXX1A.dcm-data2]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/SC_rgb_small_odd.dcm-data3]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/SC_ybr_full_422_uncompressed.dcm-data4]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/OBXXXX1A_2frame.dcm-data5]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/SC_rgb.dcm-data6]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_2frame.dcm-data7]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small.dcm-data8]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/emri_small.dcm-data9]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_16bit.dcm-data10]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_16bit_2frame.dcm-data11]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/rtdose_1frame.dcm-data12]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/rtdose.dcm-data13]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_32bit.dcm-data14]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_32bit_2frame.dcm-data15]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_1bit_1sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_1bit_1sample_3frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_16bit_1sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_16bit_1sample_1frame_padded", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_16bit_1sample_10frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_16bit_3sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_16bit_3sample_2frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_32bit_1sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_32bit_1sample_15frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_32bit_3sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_32bit_3sample_2frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_32bit_float_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_32bit_float_15frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_64bit_float_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_64bit_float_15frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/liver_1frame.dcm-/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/liver_expb_1frame.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/liver.dcm-/n/fs/guest/jy1682/.pydicom/data/liver_expb.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/OBXXXX1A.dcm-/n/fs/guest/jy1682/.pydicom/data/OBXXXX1A_expb.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/OBXXXX1A_2frame.dcm-/n/fs/guest/jy1682/.pydicom/data/OBXXXX1A_expb_2frame.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/SC_rgb.dcm-/n/fs/guest/jy1682/.pydicom/data/SC_rgb_expb.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_2frame.dcm-/n/fs/guest/jy1682/.pydicom/data/SC_rgb_expb_2frame.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small.dcm-/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small_expb.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/emri_small.dcm-/n/fs/guest/jy1682/.pydicom/data/emri_small_big_endian.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_16bit.dcm-/n/fs/guest/jy1682/.pydicom/data/SC_rgb_expb_16bit.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_16bit_2frame.dcm-/n/fs/guest/jy1682/.pydicom/data/SC_rgb_expb_16bit_2frame.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/rtdose_1frame.dcm-/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/rtdose_expb_1frame.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/rtdose.dcm-/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/rtdose_expb.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_32bit.dcm-/n/fs/guest/jy1682/.pydicom/data/SC_rgb_expb_32bit.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_32bit_2frame.dcm-/n/fs/guest/jy1682/.pydicom/data/SC_rgb_expb_32bit_2frame.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_endianness_not_set", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_read_only", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_no_pixel_data_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_required_elem_pixel_data_color", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_required_elem_float_pixel_data_monochrome", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_unknown_pixel_representation_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_unsupported_syntaxes_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_bad_length_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_padding_warns", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_change_photometric_interpretation", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_array_read_only", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_array_read_only_bit_packed", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_ybr422_excess_padding", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_ybr422_wrong_interpretation", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_float_pixel_data", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_double_float_pixel_data", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[-output0]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x00-output1]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x01-output2]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x02-output3]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x04-output4]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x08-output5]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x10-output6]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[@-output8]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x80-output9]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\xaa-output10]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\xf0-output11]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x0f-output12]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\xff-output13]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x00\\x00-output14]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x00\\x01-output15]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x00\\x80-output16]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x00\\xff-output17]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x01\\x80-output18]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x80\\x80-output19]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\xff\\x80-output20]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[-input0]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x00-input1]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x01-input2]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x02-input3]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x04-input4]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x08-input5]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x10-input6]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[@-input8]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x80-input9]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\xaa-input10]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\xf0-input11]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x0f-input12]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\xff-input13]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x00\\x00-input14]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x00\\x01-input15]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x00\\x80-input16]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x00\\xff-input17]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x01\\x80-input18]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x80\\x80-input19]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\xff\\x80-input20]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_non_binary_input", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_ndarray_input", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_padding", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00@-input0]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00\\x10-input2]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00\\x08-input3]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00\\x04-input4]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00\\x02-input5]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00\\x01-input6]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x80-input7]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[@-input8]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x10-input10]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x08-input11]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x04-input12]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x02-input13]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x01-input14]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[-input15]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_functional"], "environment_setup_commit": "0fa18d2a2179c92efc22200ed6b3689e66cecf92", "query": "Pixel Representation attribute should be optional for pixel data handler\n**Describe the bug**\r\nThe NumPy pixel data handler currently [requires the Pixel Representation attribute](https://github.com/pydicom/pydicom/blob/8da0b9b215ebfad5756051c891def88e426787e7/pydicom/pixel_data_handlers/numpy_handler.py#L46). This is problematic, because in case of Float Pixel Data or Double Float Pixel Data the attribute shall be absent. Compare [Floating Point Image Pixel Module Attributes](http://dicom.nema.org/medical/dicom/current/output/chtml/part03/sect_C.7.6.24.html) versus [Image Pixel Description Macro Attributes](http://dicom.nema.org/medical/dicom/current/output/chtml/part03/sect_C.7.6.3.html#table_C.7-11c)\r\n\r\n**Expected behavior**\r\nI would expect the `Dataset.pixel_array` property to be able to decode a Float Pixel Data or Double Float Pixel Data element without presence of the Pixel Representation element in the metadata.\r\n\r\n**Steps To Reproduce**\r\n```python\r\nimport numpy as np\r\nfrom pydicom.dataset import Dataset, FileMetaDataset\r\n\r\n\r\nds = Dataset()\r\nds.file_meta = FileMetaDataset()\r\nds.file_meta.TransferSyntaxUID = '1.2.840.10008.1.2.1'\r\n\r\nds.BitsAllocated = 32\r\nds.SamplesPerPixel = 1\r\nds.Rows = 5\r\nds.Columns = 5\r\nds.PhotometricInterpretation = 'MONOCHROME2'\r\n\r\npixel_array = np.zeros((ds.Rows, ds.Columns), dtype=np.float32)\r\nds.FloatPixelData = pixel_array.flatten().tobytes()\r\n\r\nnp.array_equal(ds.pixel_array, pixel_array)\r\n```\n", "task_id": "pydicom__pydicom-1458"}, {"repo": "pylint-dev/astroid", "instance_id": "pylint-dev__astroid-1268", "base_commit": "ce5cbce5ba11cdc2f8139ade66feea1e181a7944", "patch": "diff --git a/astroid/nodes/as_string.py b/astroid/nodes/as_string.py\n--- a/astroid/nodes/as_string.py\n+++ b/astroid/nodes/as_string.py\n@@ -36,6 +36,7 @@\n         MatchSingleton,\n         MatchStar,\n         MatchValue,\n+        Unknown,\n     )\n \n # pylint: disable=unused-argument\n@@ -643,6 +644,9 @@ def visit_property(self, node):\n     def visit_evaluatedobject(self, node):\n         return node.original.accept(self)\n \n+    def visit_unknown(self, node: \"Unknown\") -> str:\n+        return str(node)\n+\n \n def _import_string(names):\n     \"\"\"return a list of (name, asname) formatted as a string\"\"\"\n", "test_patch": "diff --git a/tests/unittest_nodes.py b/tests/unittest_nodes.py\n--- a/tests/unittest_nodes.py\n+++ b/tests/unittest_nodes.py\n@@ -306,6 +306,11 @@ def test_f_strings(self):\n         ast = abuilder.string_build(code)\n         self.assertEqual(ast.as_string().strip(), code.strip())\n \n+    @staticmethod\n+    def test_as_string_unknown() -> None:\n+        assert nodes.Unknown().as_string() == \"Unknown.Unknown()\"\n+        assert nodes.Unknown(lineno=1, col_offset=0).as_string() == \"Unknown.Unknown()\"\n+\n \n class _NodeTest(unittest.TestCase):\n     \"\"\"test transformation of If Node\"\"\"\n", "problem_statement": "'AsStringVisitor' object has no attribute 'visit_unknown'\n```python\r\n>>> import astroid\r\n>>> astroid.nodes.Unknown().as_string()\r\nTraceback (most recent call last):\r\n  File \"<stdin>\", line 1, in <module>\r\n  File \"/Users/<USER>/code/marvin-python/venv/lib/python3.9/site-packages/astroid/nodes/node_ng.py\", line 609, in as_string\r\n    return AsStringVisitor()(self)\r\n  File \"/Users/<USER>/code/marvin-python/venv/lib/python3.9/site-packages/astroid/nodes/as_string.py\", line 56, in __call__\r\n    return node.accept(self).replace(DOC_NEWLINE, \"\\n\")\r\n  File \"/Users/<USER>/code/marvin-python/venv/lib/python3.9/site-packages/astroid/nodes/node_ng.py\", line 220, in accept\r\n    func = getattr(visitor, \"visit_\" + self.__class__.__name__.lower())\r\nAttributeError: 'AsStringVisitor' object has no attribute 'visit_unknown'\r\n>>> \r\n```\r\n### `python -c \"from astroid import __pkginfo__; print(__pkginfo__.version)\"` output\r\n\r\n2.8.6-dev0\n", "hints_text": "Thank you for opening the issue.\nI don't believe `Unknown().as_string()` is ever called regularly. AFAIK it's only used during inference. What should the string representation of an `Unknown` node be? So not sure this needs to be addressed.\nProbably just `'Unknown'`.\nIt's mostly only a problem when we do something like this:\n\n```python\ninferred = infer(node)\nif inferred is not Uninferable:\n    if inferred.as_string().contains(some_value):\n        ...\n```\nSo for the most part, as long as it doesn't crash we're good.", "created_at": "2021-11-21T16:15:23Z", "version": "2.9", "FAIL_TO_PASS": ["tests/unittest_nodes.py::AsStringTest::test_as_string_unknown"], "PASS_TO_PASS": ["tests/unittest_nodes.py::AsStringTest::test_3k_annotations_and_metaclass", "tests/unittest_nodes.py::AsStringTest::test_3k_as_string", "tests/unittest_nodes.py::AsStringTest::test_as_string", "tests/unittest_nodes.py::AsStringTest::test_as_string_for_list_containing_uninferable", "tests/unittest_nodes.py::AsStringTest::test_class_def", "tests/unittest_nodes.py::AsStringTest::test_ellipsis", "tests/unittest_nodes.py::AsStringTest::test_f_strings", "tests/unittest_nodes.py::AsStringTest::test_frozenset_as_string", "tests/unittest_nodes.py::AsStringTest::test_func_signature_issue_185", "tests/unittest_nodes.py::AsStringTest::test_int_attribute", "tests/unittest_nodes.py::AsStringTest::test_module2_as_string", "tests/unittest_nodes.py::AsStringTest::test_module_as_string", "tests/unittest_nodes.py::AsStringTest::test_operator_precedence", "tests/unittest_nodes.py::AsStringTest::test_slice_and_subscripts", "tests/unittest_nodes.py::AsStringTest::test_slices", "tests/unittest_nodes.py::AsStringTest::test_tuple_as_string", "tests/unittest_nodes.py::AsStringTest::test_varargs_kwargs_as_string", "tests/unittest_nodes.py::IfNodeTest::test_block_range", "tests/unittest_nodes.py::IfNodeTest::test_if_elif_else_node", "tests/unittest_nodes.py::IfNodeTest::test_if_sys_guard", "tests/unittest_nodes.py::IfNodeTest::test_if_typing_guard", "tests/unittest_nodes.py::TryExceptNodeTest::test_block_range", "tests/unittest_nodes.py::TryFinallyNodeTest::test_block_range", "tests/unittest_nodes.py::TryExceptFinallyNodeTest::test_block_range", "tests/unittest_nodes.py::ImportNodeTest::test_absolute_import", "tests/unittest_nodes.py::ImportNodeTest::test_as_string", "tests/unittest_nodes.py::ImportNodeTest::test_bad_import_inference", "tests/unittest_nodes.py::ImportNodeTest::test_conditional", "tests/unittest_nodes.py::ImportNodeTest::test_conditional_import", "tests/unittest_nodes.py::ImportNodeTest::test_from_self_resolve", "tests/unittest_nodes.py::ImportNodeTest::test_import_self_resolve", "tests/unittest_nodes.py::ImportNodeTest::test_more_absolute_import", "tests/unittest_nodes.py::ImportNodeTest::test_real_name", "tests/unittest_nodes.py::CmpNodeTest::test_as_string", "tests/unittest_nodes.py::ConstNodeTest::test_bool", "tests/unittest_nodes.py::ConstNodeTest::test_complex", "tests/unittest_nodes.py::ConstNodeTest::test_copy", "tests/unittest_nodes.py::ConstNodeTest::test_float", "tests/unittest_nodes.py::ConstNodeTest::test_int", "tests/unittest_nodes.py::ConstNodeTest::test_none", "tests/unittest_nodes.py::ConstNodeTest::test_str", "tests/unittest_nodes.py::ConstNodeTest::test_str_kind", "tests/unittest_nodes.py::ConstNodeTest::test_unicode", "tests/unittest_nodes.py::NameNodeTest::test_assign_to_true", "tests/unittest_nodes.py::TestNamedExprNode::test_frame", "tests/unittest_nodes.py::TestNamedExprNode::test_scope", "tests/unittest_nodes.py::AnnAssignNodeTest::test_as_string", "tests/unittest_nodes.py::AnnAssignNodeTest::test_complex", "tests/unittest_nodes.py::AnnAssignNodeTest::test_primitive", "tests/unittest_nodes.py::AnnAssignNodeTest::test_primitive_without_initial_value", "tests/unittest_nodes.py::ArgumentsNodeTC::test_kwoargs", "tests/unittest_nodes.py::ArgumentsNodeTC::test_positional_only", "tests/unittest_nodes.py::UnboundMethodNodeTest::test_no_super_getattr", "tests/unittest_nodes.py::BoundMethodNodeTest::test_is_property", "tests/unittest_nodes.py::AliasesTest::test_aliases", "tests/unittest_nodes.py::Python35AsyncTest::test_async_await_keywords", "tests/unittest_nodes.py::Python35AsyncTest::test_asyncfor_as_string", "tests/unittest_nodes.py::Python35AsyncTest::test_asyncwith_as_string", "tests/unittest_nodes.py::Python35AsyncTest::test_await_as_string", "tests/unittest_nodes.py::Python35AsyncTest::test_decorated_async_def_as_string", "tests/unittest_nodes.py::ContextTest::test_list_del", "tests/unittest_nodes.py::ContextTest::test_list_load", "tests/unittest_nodes.py::ContextTest::test_list_store", "tests/unittest_nodes.py::ContextTest::test_starred_load", "tests/unittest_nodes.py::ContextTest::test_starred_store", "tests/unittest_nodes.py::ContextTest::test_subscript_del", "tests/unittest_nodes.py::ContextTest::test_subscript_load", "tests/unittest_nodes.py::ContextTest::test_subscript_store", "tests/unittest_nodes.py::ContextTest::test_tuple_load", "tests/unittest_nodes.py::ContextTest::test_tuple_store", "tests/unittest_nodes.py::test_unknown", "tests/unittest_nodes.py::test_type_comments_with", "tests/unittest_nodes.py::test_type_comments_for", "tests/unittest_nodes.py::test_type_coments_assign", "tests/unittest_nodes.py::test_type_comments_invalid_expression", "tests/unittest_nodes.py::test_type_comments_invalid_function_comments", "tests/unittest_nodes.py::test_type_comments_function", "tests/unittest_nodes.py::test_type_comments_arguments", "tests/unittest_nodes.py::test_type_comments_posonly_arguments", "tests/unittest_nodes.py::test_correct_function_type_comment_parent", "tests/unittest_nodes.py::test_is_generator_for_yield_assignments", "tests/unittest_nodes.py::test_f_string_correct_line_numbering", "tests/unittest_nodes.py::test_assignment_expression", "tests/unittest_nodes.py::test_assignment_expression_in_functiondef", "tests/unittest_nodes.py::test_get_doc", "tests/unittest_nodes.py::test_parse_fstring_debug_mode", "tests/unittest_nodes.py::test_parse_type_comments_with_proper_parent", "tests/unittest_nodes.py::test_const_itered", "tests/unittest_nodes.py::test_is_generator_for_yield_in_while", "tests/unittest_nodes.py::test_is_generator_for_yield_in_if", "tests/unittest_nodes.py::test_is_generator_for_yield_in_aug_assign"], "environment_setup_commit": "0d1211558670cfefd95b39984b8d5f7f34837f32", "query": "'AsStringVisitor' object has no attribute 'visit_unknown'\n```python\r\n>>> import astroid\r\n>>> astroid.nodes.Unknown().as_string()\r\nTraceback (most recent call last):\r\n  File \"<stdin>\", line 1, in <module>\r\n  File \"/Users/<USER>/code/marvin-python/venv/lib/python3.9/site-packages/astroid/nodes/node_ng.py\", line 609, in as_string\r\n    return AsStringVisitor()(self)\r\n  File \"/Users/<USER>/code/marvin-python/venv/lib/python3.9/site-packages/astroid/nodes/as_string.py\", line 56, in __call__\r\n    return node.accept(self).replace(DOC_NEWLINE, \"\\n\")\r\n  File \"/Users/<USER>/code/marvin-python/venv/lib/python3.9/site-packages/astroid/nodes/node_ng.py\", line 220, in accept\r\n    func = getattr(visitor, \"visit_\" + self.__class__.__name__.lower())\r\nAttributeError: 'AsStringVisitor' object has no attribute 'visit_unknown'\r\n>>> \r\n```\r\n### `python -c \"from astroid import __pkginfo__; print(__pkginfo__.version)\"` output\r\n\r\n2.8.6-dev0\n", "task_id": "pylint-dev__astroid-1268"}, {"repo": "pydicom/pydicom", "instance_id": "pydicom__pydicom-1194", "base_commit": "5e70c1dfe09820023fec519dac4c51bebcb7f60d", "patch": "diff --git a/pydicom/filewriter.py b/pydicom/filewriter.py\n--- a/pydicom/filewriter.py\n+++ b/pydicom/filewriter.py\n@@ -87,6 +87,9 @@ def _correct_ambiguous_vr_element(elem, ds, is_little_endian):\n             elem.VR = 'SS'\n             byte_type = 'h'\n \n+        if elem.VM == 0:\n+            return elem\n+\n         # Need to handle type check for elements with VM > 1\n         elem_value = elem.value if elem.VM == 1 else elem.value[0]\n         if not isinstance(elem_value, int):\n@@ -115,6 +118,9 @@ def _correct_ambiguous_vr_element(elem, ds, is_little_endian):\n         # As per PS3.3 C.********\n         if ds.LUTDescriptor[0] == 1:\n             elem.VR = 'US'\n+            if elem.VM == 0:\n+                return elem\n+\n             elem_value = elem.value if elem.VM == 1 else elem.value[0]\n             if not isinstance(elem_value, int):\n                 elem.value = convert_numbers(elem.value, is_little_endian, 'H')\n", "test_patch": "diff --git a/pydicom/tests/test_filewriter.py b/pydicom/tests/test_filewriter.py\n--- a/pydicom/tests/test_filewriter.py\n+++ b/pydicom/tests/test_filewriter.py\n@@ -1054,7 +1054,7 @@ def test_not_ambiguous_raw_data_element(self):\n         elem = RawDataElement(0x60003000, 'OB', 1, b'\\x00', 0, True, True)\n         out = correct_ambiguous_vr_element(elem, Dataset(), True)\n         assert out == elem\n-        assert type(out) == RawDataElement\n+        assert isinstance(out, RawDataElement)\n \n     def test_correct_ambiguous_data_element(self):\n         \"\"\"Test correct ambiguous US/SS element\"\"\"\n@@ -1086,10 +1086,28 @@ def test_correct_ambiguous_raw_data_element(self):\n         ds[0x00280120] = elem\n         ds.PixelRepresentation = 0\n         out = correct_ambiguous_vr_element(elem, ds, True)\n-        assert type(out) == DataElement\n+        assert isinstance(out, DataElement)\n         assert out.VR == 'US'\n         assert out.value == 0xfffe\n \n+    def test_empty_value(self):\n+        \"\"\"Regression test for #1193: empty value raises exception.\"\"\"\n+        ds = Dataset()\n+        elem = RawDataElement(0x00280106, 'US or SS', 0, None, 0, True, True)\n+        ds[0x00280106] = elem\n+        out = correct_ambiguous_vr_element(elem, ds, True)\n+        assert isinstance(out, DataElement)\n+        assert out.VR == 'US'\n+\n+        ds.LUTDescriptor = [1, 1, 1]\n+        elem = RawDataElement(0x00283006, 'US or SS', 0, None, 0, True, True)\n+        assert out.value is None\n+        ds[0x00283006] = elem\n+        out = correct_ambiguous_vr_element(elem, ds, True)\n+        assert isinstance(out, DataElement)\n+        assert out.VR == 'US'\n+        assert out.value is None\n+\n \n class TestWriteAmbiguousVR:\n     \"\"\"Attempt to write data elements with ambiguous VR.\"\"\"\n", "problem_statement": "Error decoding dataset with ambiguous VR element when the value is None\nHi all,\r\n    I used the storescu in pynetdicom 1.5.3 to send the dicom ct files(both on mac and ubuntu): \r\n**python storescu.py ************* 9002 ~/Downloads/test/**\r\n(I also tried https://pydicom.github.io/pynetdicom/stable/examples/storage.html#storage-scu)\r\nbut it throwed errors: \r\n\r\n_E: Failed to encode the supplied Dataset\r\nE: Store failed: /Users/<USER>/Downloads/test/CT_S1_118.dcm\r\nE: Failed to encode the supplied Dataset\r\nTraceback (most recent call last):\r\n  File \"storescu.py\", line 283, in main\r\n    status = assoc.send_c_store(ds, ii)\r\n  File \"/Users/<USER>/.pyenv/versions/3.8.2/lib/python3.8/site-packages/pynetdicom/association.py\", line 1736, in send_c_store\r\n    raise ValueError('Failed to encode the supplied Dataset')\r\nValueError: Failed to encode the supplied Dataset_\r\n\r\nBut I used to send same files with storescu in dcm4che successfully.\r\nFile attached.\r\n\r\n[test.zip](https://github.com/pydicom/pynetdicom/files/5258867/test.zip)\r\n\n", "hints_text": "```python\r\n>>> from pydicom import dcmread\r\n>>> dcmread(\"CT_S1_001.dcm\")\r\nTraceback (most recent call last):\r\n  File \".../pydicom/tag.py\", line 30, in tag_in_exception\r\n    yield\r\n  File \".../pydicom/filewriter.py\", line 555, in write_dataset\r\n    write_data_element(fp, dataset.get_item(tag), dataset_encoding)\r\n  File \".../pydicom/dataset.py\", line 1060, in get_item\r\n    return self[key]\r\n  File \".../pydicom/dataset.py\", line 878, in __getitem__\r\n    self[tag] = correct_ambiguous_vr_element(\r\n  File \".../pydicom/filewriter.py\", line 160, in correct_ambiguous_vr_element\r\n    _correct_ambiguous_vr_element(elem, ds, is_little_endian)\r\n  File \".../pydicom/filewriter.py\", line 86, in _correct_ambiguous_vr_element\r\n    elem_value = elem.value if elem.VM == 1 else elem.value[0]\r\nTypeError: 'NoneType' object is not subscriptable\r\n```\r\nIssue occurs because the dataset is Implicit VR and the *Smallest Image Pixel Value* is ambiguous but empty,", "created_at": "2020-09-22T03:16:19Z", "version": "2.0", "FAIL_TO_PASS": ["pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVRElement::test_empty_value"], "PASS_TO_PASS": ["pydicom/tests/test_filewriter.py::TestWriteFile::testRTPlan", "pydicom/tests/test_filewriter.py::TestWriteFile::testRTDose", "pydicom/tests/test_filewriter.py::TestWriteFile::testCT", "pydicom/tests/test_filewriter.py::TestWriteFile::testMR", "pydicom/tests/test_filewriter.py::TestWriteFile::testUnicode", "pydicom/tests/test_filewriter.py::TestWriteFile::testMultiPN", "pydicom/tests/test_filewriter.py::TestWriteFile::testJPEG2000", "pydicom/tests/test_filewriter.py::TestWriteFile::test_pathlib_path_filename", "pydicom/tests/test_filewriter.py::TestWriteFile::testListItemWriteBack", "pydicom/tests/test_filewriter.py::TestWriteFile::testwrite_short_uid", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_no_ts", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_double_filemeta", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_ffff_ffff", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_removes_grouplength", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_empty_sequence", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_deflated_retains_elements", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_deflated_deflates_post_file_meta", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_dataset_without_encoding", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testRTPlan", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testRTDose", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testCT", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testMR", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testUnicode", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testMultiPN", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testJPEG2000", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_pathlib_path_filename", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testListItemWriteBack", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testwrite_short_uid", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_no_ts", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_double_filemeta", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_ffff_ffff", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_removes_grouplength", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_empty_sequence", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_deflated_retains_elements", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_deflated_deflates_post_file_meta", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_dataset_without_encoding", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_multivalue_DA", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_empty_AT", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_empty_LO", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_DA", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_multi_DA", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_TM", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_multi_TM", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_DT", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_multi_DT", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_ascii_vr_with_padding", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_OD_implicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_OD_explicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_OL_implicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_OL_explicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_UC_implicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_UC_explicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_UR_implicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_UR_explicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_UN_implicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_unknown_vr_raises", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_pixel_representation_vm_one", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_pixel_representation_vm_three", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_pixel_data", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_waveform_bits_allocated", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_lut_descriptor", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_overlay", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_sequence", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_write_new_ambiguous", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_ambiguous_element_in_sequence_explicit_using_attribute", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_ambiguous_element_in_sequence_explicit_using_index", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_ambiguous_element_in_sequence_implicit_using_attribute", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_ambiguous_element_in_sequence_implicit_using_index", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVRElement::test_not_ambiguous", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVRElement::test_not_ambiguous_raw_data_element", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVRElement::test_correct_ambiguous_data_element", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVRElement::test_correct_ambiguous_raw_data_element", "pydicom/tests/test_filewriter.py::TestWriteAmbiguousVR::test_write_explicit_vr_raises", "pydicom/tests/test_filewriter.py::TestWriteAmbiguousVR::test_write_explicit_vr_little_endian", "pydicom/tests/test_filewriter.py::TestWriteAmbiguousVR::test_write_explicit_vr_big_endian", "pydicom/tests/test_filewriter.py::TestScratchWrite::testImpl_LE_deflen_write", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_preamble_default", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_preamble_custom", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_no_preamble", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_none_preamble", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_bad_preamble", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_bad_filename", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_prefix", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_prefix_none", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_ds_changed", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_raw_elements_preserved_implicit_vr", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_raw_elements_preserved_explicit_vr", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_convert_implicit_to_explicit_vr", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_write_dataset", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_write_dataset_with_explicit_vr", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_convert_implicit_to_explicit_vr_using_destination", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_convert_explicit_to_implicit_vr", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_convert_big_to_little_endian", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_convert_little_to_big_endian", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_changed_character_set", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_transfer_syntax_added", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_private_tag_vr_from_implicit_data", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_convert_rgb_from_implicit_to_explicit_vr", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_transfer_syntax_not_added", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_transfer_syntax_raises", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_media_storage_sop_class_uid_added", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_write_no_file_meta", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_raise_no_file_meta", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_add_file_meta", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_standard", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_commandset_no_written", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_bad_elements", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_missing_elements", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_group_length", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_group_length_updated", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_version", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_implementation_version_name_length", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_implementation_class_uid_length", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_filelike_position", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_default", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_custom", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_no_preamble", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_ds_unchanged", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_file_meta_unchanged", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_filemeta_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_filemeta_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_commandset_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_commandset_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_commandset_filemeta_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_commandset_filemeta_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_commandset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_commandset_filemeta", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_commandset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_commandset_filemeta", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_read_write_identical", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoNonStandard::test_transfer_syntax_not_added", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoNonStandard::test_bad_elements", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoNonStandard::test_missing_elements", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoNonStandard::test_group_length_updated", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoNonStandard::test_filelike_position", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoNonStandard::test_meta_unchanged", "pydicom/tests/test_filewriter.py::TestWriteNumbers::test_write_empty_value", "pydicom/tests/test_filewriter.py::TestWriteNumbers::test_write_list", "pydicom/tests/test_filewriter.py::TestWriteNumbers::test_write_singleton", "pydicom/tests/test_filewriter.py::TestWriteNumbers::test_exception", "pydicom/tests/test_filewriter.py::TestWriteNumbers::test_write_big_endian", "pydicom/tests/test_filewriter.py::TestWriteOtherVRs::test_write_of", "pydicom/tests/test_filewriter.py::TestWriteOtherVRs::test_write_of_dataset", "pydicom/tests/test_filewriter.py::TestWritePN::test_no_encoding", "pydicom/tests/test_filewriter.py::TestWritePN::test_single_byte_multi_charset_groups", "pydicom/tests/test_filewriter.py::TestWritePN::test_single_byte_multi_charset_values", "pydicom/tests/test_filewriter.py::TestWriteText::test_no_encoding", "pydicom/tests/test_filewriter.py::TestWriteText::test_single_byte_multi_charset_text", "pydicom/tests/test_filewriter.py::TestWriteText::test_encode_mixed_charsets_text", "pydicom/tests/test_filewriter.py::TestWriteText::test_single_byte_multi_charset_text_multivalue", "pydicom/tests/test_filewriter.py::TestWriteText::test_invalid_encoding", "pydicom/tests/test_filewriter.py::TestWriteText::test_invalid_encoding_enforce_standard", "pydicom/tests/test_filewriter.py::TestWriteText::test_single_value_with_delimiters", "pydicom/tests/test_filewriter.py::TestWriteDT::test_format_dt", "pydicom/tests/test_filewriter.py::TestWriteUndefinedLengthPixelData::test_little_endian_correct_data", "pydicom/tests/test_filewriter.py::TestWriteUndefinedLengthPixelData::test_big_endian_correct_data", "pydicom/tests/test_filewriter.py::TestWriteUndefinedLengthPixelData::test_little_endian_incorrect_data", "pydicom/tests/test_filewriter.py::TestWriteUndefinedLengthPixelData::test_big_endian_incorrect_data", "pydicom/tests/test_filewriter.py::TestWriteUndefinedLengthPixelData::test_writing_to_gzip", "pydicom/tests/test_filewriter.py::TestWriteUndefinedLengthPixelData::test_writing_too_big_data_in_explicit_encoding"], "environment_setup_commit": "9d69811e539774f296c2f289839147e741251716", "query": "Error decoding dataset with ambiguous VR element when the value is None\nHi all,\r\n    I used the storescu in pynetdicom 1.5.3 to send the dicom ct files(both on mac and ubuntu): \r\n**python storescu.py ************* 9002 ~/Downloads/test/**\r\n(I also tried https://pydicom.github.io/pynetdicom/stable/examples/storage.html#storage-scu)\r\nbut it throwed errors: \r\n\r\n_E: Failed to encode the supplied Dataset\r\nE: Store failed: /Users/<USER>/Downloads/test/CT_S1_118.dcm\r\nE: Failed to encode the supplied Dataset\r\nTraceback (most recent call last):\r\n  File \"storescu.py\", line 283, in main\r\n    status = assoc.send_c_store(ds, ii)\r\n  File \"/Users/<USER>/.pyenv/versions/3.8.2/lib/python3.8/site-packages/pynetdicom/association.py\", line 1736, in send_c_store\r\n    raise ValueError('Failed to encode the supplied Dataset')\r\nValueError: Failed to encode the supplied Dataset_\r\n\r\nBut I used to send same files with storescu in dcm4che successfully.\r\nFile attached.\r\n\r\n[test.zip](https://github.com/pydicom/pynetdicom/files/5258867/test.zip)\r\n\n", "task_id": "pydicom__pydicom-1194"}, {"repo": "pylint-dev/astroid", "instance_id": "pylint-dev__astroid-1268", "base_commit": "ce5cbce5ba11cdc2f8139ade66feea1e181a7944", "patch": "diff --git a/astroid/nodes/as_string.py b/astroid/nodes/as_string.py\n--- a/astroid/nodes/as_string.py\n+++ b/astroid/nodes/as_string.py\n@@ -36,6 +36,7 @@\n         MatchSingleton,\n         MatchStar,\n         MatchValue,\n+        Unknown,\n     )\n \n # pylint: disable=unused-argument\n@@ -643,6 +644,9 @@ def visit_property(self, node):\n     def visit_evaluatedobject(self, node):\n         return node.original.accept(self)\n \n+    def visit_unknown(self, node: \"Unknown\") -> str:\n+        return str(node)\n+\n \n def _import_string(names):\n     \"\"\"return a list of (name, asname) formatted as a string\"\"\"\n", "test_patch": "diff --git a/tests/unittest_nodes.py b/tests/unittest_nodes.py\n--- a/tests/unittest_nodes.py\n+++ b/tests/unittest_nodes.py\n@@ -306,6 +306,11 @@ def test_f_strings(self):\n         ast = abuilder.string_build(code)\n         self.assertEqual(ast.as_string().strip(), code.strip())\n \n+    @staticmethod\n+    def test_as_string_unknown() -> None:\n+        assert nodes.Unknown().as_string() == \"Unknown.Unknown()\"\n+        assert nodes.Unknown(lineno=1, col_offset=0).as_string() == \"Unknown.Unknown()\"\n+\n \n class _NodeTest(unittest.TestCase):\n     \"\"\"test transformation of If Node\"\"\"\n", "problem_statement": "'AsStringVisitor' object has no attribute 'visit_unknown'\n```python\r\n>>> import astroid\r\n>>> astroid.nodes.Unknown().as_string()\r\nTraceback (most recent call last):\r\n  File \"<stdin>\", line 1, in <module>\r\n  File \"/Users/<USER>/code/marvin-python/venv/lib/python3.9/site-packages/astroid/nodes/node_ng.py\", line 609, in as_string\r\n    return AsStringVisitor()(self)\r\n  File \"/Users/<USER>/code/marvin-python/venv/lib/python3.9/site-packages/astroid/nodes/as_string.py\", line 56, in __call__\r\n    return node.accept(self).replace(DOC_NEWLINE, \"\\n\")\r\n  File \"/Users/<USER>/code/marvin-python/venv/lib/python3.9/site-packages/astroid/nodes/node_ng.py\", line 220, in accept\r\n    func = getattr(visitor, \"visit_\" + self.__class__.__name__.lower())\r\nAttributeError: 'AsStringVisitor' object has no attribute 'visit_unknown'\r\n>>> \r\n```\r\n### `python -c \"from astroid import __pkginfo__; print(__pkginfo__.version)\"` output\r\n\r\n2.8.6-dev0\n", "hints_text": "Thank you for opening the issue.\nI don't believe `Unknown().as_string()` is ever called regularly. AFAIK it's only used during inference. What should the string representation of an `Unknown` node be? So not sure this needs to be addressed.\nProbably just `'Unknown'`.\nIt's mostly only a problem when we do something like this:\n\n```python\ninferred = infer(node)\nif inferred is not Uninferable:\n    if inferred.as_string().contains(some_value):\n        ...\n```\nSo for the most part, as long as it doesn't crash we're good.", "created_at": "2021-11-21T16:15:23Z", "version": "2.9", "FAIL_TO_PASS": ["tests/unittest_nodes.py::AsStringTest::test_as_string_unknown"], "PASS_TO_PASS": ["tests/unittest_nodes.py::AsStringTest::test_3k_annotations_and_metaclass", "tests/unittest_nodes.py::AsStringTest::test_3k_as_string", "tests/unittest_nodes.py::AsStringTest::test_as_string", "tests/unittest_nodes.py::AsStringTest::test_as_string_for_list_containing_uninferable", "tests/unittest_nodes.py::AsStringTest::test_class_def", "tests/unittest_nodes.py::AsStringTest::test_ellipsis", "tests/unittest_nodes.py::AsStringTest::test_f_strings", "tests/unittest_nodes.py::AsStringTest::test_frozenset_as_string", "tests/unittest_nodes.py::AsStringTest::test_func_signature_issue_185", "tests/unittest_nodes.py::AsStringTest::test_int_attribute", "tests/unittest_nodes.py::AsStringTest::test_module2_as_string", "tests/unittest_nodes.py::AsStringTest::test_module_as_string", "tests/unittest_nodes.py::AsStringTest::test_operator_precedence", "tests/unittest_nodes.py::AsStringTest::test_slice_and_subscripts", "tests/unittest_nodes.py::AsStringTest::test_slices", "tests/unittest_nodes.py::AsStringTest::test_tuple_as_string", "tests/unittest_nodes.py::AsStringTest::test_varargs_kwargs_as_string", "tests/unittest_nodes.py::IfNodeTest::test_block_range", "tests/unittest_nodes.py::IfNodeTest::test_if_elif_else_node", "tests/unittest_nodes.py::IfNodeTest::test_if_sys_guard", "tests/unittest_nodes.py::IfNodeTest::test_if_typing_guard", "tests/unittest_nodes.py::TryExceptNodeTest::test_block_range", "tests/unittest_nodes.py::TryFinallyNodeTest::test_block_range", "tests/unittest_nodes.py::TryExceptFinallyNodeTest::test_block_range", "tests/unittest_nodes.py::ImportNodeTest::test_absolute_import", "tests/unittest_nodes.py::ImportNodeTest::test_as_string", "tests/unittest_nodes.py::ImportNodeTest::test_bad_import_inference", "tests/unittest_nodes.py::ImportNodeTest::test_conditional", "tests/unittest_nodes.py::ImportNodeTest::test_conditional_import", "tests/unittest_nodes.py::ImportNodeTest::test_from_self_resolve", "tests/unittest_nodes.py::ImportNodeTest::test_import_self_resolve", "tests/unittest_nodes.py::ImportNodeTest::test_more_absolute_import", "tests/unittest_nodes.py::ImportNodeTest::test_real_name", "tests/unittest_nodes.py::CmpNodeTest::test_as_string", "tests/unittest_nodes.py::ConstNodeTest::test_bool", "tests/unittest_nodes.py::ConstNodeTest::test_complex", "tests/unittest_nodes.py::ConstNodeTest::test_copy", "tests/unittest_nodes.py::ConstNodeTest::test_float", "tests/unittest_nodes.py::ConstNodeTest::test_int", "tests/unittest_nodes.py::ConstNodeTest::test_none", "tests/unittest_nodes.py::ConstNodeTest::test_str", "tests/unittest_nodes.py::ConstNodeTest::test_str_kind", "tests/unittest_nodes.py::ConstNodeTest::test_unicode", "tests/unittest_nodes.py::NameNodeTest::test_assign_to_true", "tests/unittest_nodes.py::TestNamedExprNode::test_frame", "tests/unittest_nodes.py::TestNamedExprNode::test_scope", "tests/unittest_nodes.py::AnnAssignNodeTest::test_as_string", "tests/unittest_nodes.py::AnnAssignNodeTest::test_complex", "tests/unittest_nodes.py::AnnAssignNodeTest::test_primitive", "tests/unittest_nodes.py::AnnAssignNodeTest::test_primitive_without_initial_value", "tests/unittest_nodes.py::ArgumentsNodeTC::test_kwoargs", "tests/unittest_nodes.py::ArgumentsNodeTC::test_positional_only", "tests/unittest_nodes.py::UnboundMethodNodeTest::test_no_super_getattr", "tests/unittest_nodes.py::BoundMethodNodeTest::test_is_property", "tests/unittest_nodes.py::AliasesTest::test_aliases", "tests/unittest_nodes.py::Python35AsyncTest::test_async_await_keywords", "tests/unittest_nodes.py::Python35AsyncTest::test_asyncfor_as_string", "tests/unittest_nodes.py::Python35AsyncTest::test_asyncwith_as_string", "tests/unittest_nodes.py::Python35AsyncTest::test_await_as_string", "tests/unittest_nodes.py::Python35AsyncTest::test_decorated_async_def_as_string", "tests/unittest_nodes.py::ContextTest::test_list_del", "tests/unittest_nodes.py::ContextTest::test_list_load", "tests/unittest_nodes.py::ContextTest::test_list_store", "tests/unittest_nodes.py::ContextTest::test_starred_load", "tests/unittest_nodes.py::ContextTest::test_starred_store", "tests/unittest_nodes.py::ContextTest::test_subscript_del", "tests/unittest_nodes.py::ContextTest::test_subscript_load", "tests/unittest_nodes.py::ContextTest::test_subscript_store", "tests/unittest_nodes.py::ContextTest::test_tuple_load", "tests/unittest_nodes.py::ContextTest::test_tuple_store", "tests/unittest_nodes.py::test_unknown", "tests/unittest_nodes.py::test_type_comments_with", "tests/unittest_nodes.py::test_type_comments_for", "tests/unittest_nodes.py::test_type_coments_assign", "tests/unittest_nodes.py::test_type_comments_invalid_expression", "tests/unittest_nodes.py::test_type_comments_invalid_function_comments", "tests/unittest_nodes.py::test_type_comments_function", "tests/unittest_nodes.py::test_type_comments_arguments", "tests/unittest_nodes.py::test_type_comments_posonly_arguments", "tests/unittest_nodes.py::test_correct_function_type_comment_parent", "tests/unittest_nodes.py::test_is_generator_for_yield_assignments", "tests/unittest_nodes.py::test_f_string_correct_line_numbering", "tests/unittest_nodes.py::test_assignment_expression", "tests/unittest_nodes.py::test_assignment_expression_in_functiondef", "tests/unittest_nodes.py::test_get_doc", "tests/unittest_nodes.py::test_parse_fstring_debug_mode", "tests/unittest_nodes.py::test_parse_type_comments_with_proper_parent", "tests/unittest_nodes.py::test_const_itered", "tests/unittest_nodes.py::test_is_generator_for_yield_in_while", "tests/unittest_nodes.py::test_is_generator_for_yield_in_if", "tests/unittest_nodes.py::test_is_generator_for_yield_in_aug_assign"], "environment_setup_commit": "0d1211558670cfefd95b39984b8d5f7f34837f32", "query": "'AsStringVisitor' object has no attribute 'visit_unknown'\n```python\r\n>>> import astroid\r\n>>> astroid.nodes.Unknown().as_string()\r\nTraceback (most recent call last):\r\n  File \"<stdin>\", line 1, in <module>\r\n  File \"/Users/<USER>/code/marvin-python/venv/lib/python3.9/site-packages/astroid/nodes/node_ng.py\", line 609, in as_string\r\n    return AsStringVisitor()(self)\r\n  File \"/Users/<USER>/code/marvin-python/venv/lib/python3.9/site-packages/astroid/nodes/as_string.py\", line 56, in __call__\r\n    return node.accept(self).replace(DOC_NEWLINE, \"\\n\")\r\n  File \"/Users/<USER>/code/marvin-python/venv/lib/python3.9/site-packages/astroid/nodes/node_ng.py\", line 220, in accept\r\n    func = getattr(visitor, \"visit_\" + self.__class__.__name__.lower())\r\nAttributeError: 'AsStringVisitor' object has no attribute 'visit_unknown'\r\n>>> \r\n```\r\n### `python -c \"from astroid import __pkginfo__; print(__pkginfo__.version)\"` output\r\n\r\n2.8.6-dev0\n", "task_id": "pylint-dev__astroid-1268"}, {"repo": "sqlfluff/sqlfluff", "instance_id": "sqlfluff__sqlfluff-2386", "base_commit": "23d698607b45b8469c766b521d27e9a6e92e8739", "patch": "diff --git a/src/sqlfluff/core/rules/base.py b/src/sqlfluff/core/rules/base.py\n--- a/src/sqlfluff/core/rules/base.py\n+++ b/src/sqlfluff/core/rules/base.py\n@@ -875,7 +875,7 @@ def eval(self, **kwargs):\n \n         plugin_name, code = rule_name_match.groups()\n         # If the docstring is multiline, then we extract just summary.\n-        description = cls.__doc__.split(\"\\n\")[0]\n+        description = cls.__doc__.replace(\"``\", \"'\").split(\"\\n\")[0]\n \n         if plugin_name:\n             code = f\"{plugin_name}_{code}\"\n", "test_patch": "diff --git a/test/core/rules/docstring_test.py b/test/core/rules/docstring_test.py\n--- a/test/core/rules/docstring_test.py\n+++ b/test/core/rules/docstring_test.py\n@@ -1,6 +1,7 @@\n \"\"\"Test rules docstring.\"\"\"\n import pytest\n \n+from sqlfluff import lint\n from sqlfluff.core.plugin.host import get_plugin_manager\n \n KEYWORD_ANTI = \"\\n    | **Anti-pattern**\"\n@@ -34,3 +35,19 @@ def test_keyword_anti_before_best():\n                 assert rule.__doc__.index(KEYWORD_ANTI) < rule.__doc__.index(\n                     KEYWORD_BEST\n                 ), f\"{rule.__name__} keyword {KEYWORD_BEST} appears before {KEYWORD_ANTI}\"\n+\n+\n+def test_backtick_replace():\n+    \"\"\"Test replacing docstring double backticks for lint results.\"\"\"\n+    sql = \"\"\"\n+    SELECT\n+        foo.a,\n+        bar.b\n+    FROM foo\n+    JOIN bar;\n+    \"\"\"\n+    result = lint(sql, rules=[\"L051\"])\n+    # L051 docstring looks like:\n+    # ``INNER JOIN`` must be fully qualified.\n+    # Check the double bacticks (``) get replaced by a single quote (').\n+    assert result[0][\"description\"] == \"'INNER JOIN' must be fully qualified.\"\ndiff --git a/test/rules/std_L054_test.py b/test/rules/std_L054_test.py\n--- a/test/rules/std_L054_test.py\n+++ b/test/rules/std_L054_test.py\n@@ -29,7 +29,7 @@ def test__rules__std_L054_raised() -> None:\n     assert len(results_l054) == 2\n     assert (\n         results_l054[0][\"description\"]\n-        == \"Inconsistent column references in ``GROUP BY/ORDER BY`` clauses.\"\n+        == \"Inconsistent column references in 'GROUP BY/ORDER BY' clauses.\"\n     )\n \n \n", "problem_statement": "Double backticks in Lint description\n![image](https://user-images.githubusercontent.com/80432516/150420352-57452c80-ad25-423b-8251-645e541579ad.png)\r\n(n.b. this affects a lot more rules than L051)\r\n\r\nThis was introduced in #2234 in which docstrings such as\r\n```\r\n`INNER JOIN` must be fully qualified.\r\n```\r\nwere replaced with \r\n```\r\n``INNER JOIN`` must be fully qualified.\r\n```\r\nso that they appear as code blocks in Sphinx for docs.\r\n![image](https://user-images.githubusercontent.com/80432516/150420294-eb9d3127-db1d-457c-a637-d614e0267277.png)\r\n\r\nHowever, our rules will use the first line of these docstrings in the event that no `description` is provided to the lint results.\r\n\r\nThis doesn't look great on the CLI so we should fix this. As far as I'm aware there are two approaches for this:\r\n1. Pass a `description` to all the `LintResult`s.\r\n2. Update the code that gets the default description from the docstring to do something like, replace the double backticks with a single one, or remove them, or do something clever like make them bold for the CLI and remove them for non-CLI.\r\n\r\nMy strong preference is number 2, but I'm open to discussion as to how exactly we do this 😄 \r\n\r\n@barrywhart @tunetheweb \n", "hints_text": "Number 2 sounds good to me!\n@barrywhart which variation?\nI would replace with single \"normal\" quotes: ' rather than \\`.\r\n\r\nThe clever approach could be cool for later, but I wouldn't try it now. I can't remember if we already handle detecting whether we're running in a terminal or not, because the techniques for doing bold or colored text don't work well when redirecting output to a file, etc.\n> The clever approach could be cool for later, but I wouldn't try it now. I can't remember if we already handle detecting whether we're running in a terminal or not, because the techniques for doing bold or colored text don't work well when redirecting output to a file, etc.\r\n\r\nYeah I think there's some `isatty` function we use in the formatter, but agree on the simple replace method for now 😄 ", "created_at": "2022-01-21T00:03:48Z", "version": "0.8", "FAIL_TO_PASS": ["test/core/rules/docstring_test.py::test_backtick_replace", "test/rules/std_L054_test.py::test__rules__std_L054_raised"], "PASS_TO_PASS": ["test/core/rules/docstring_test.py::test_content_count[\\n", "test/core/rules/docstring_test.py::test_keyword_anti_before_best", "test/rules/std_L054_test.py::test__rules__std_L054_unparsable", "test/rules/std_L054_test.py::test__rules__std_L054_noqa"], "environment_setup_commit": "a5c4eae4e3e419fe95460c9afd9cf39a35a470c4", "query": "Double backticks in Lint description\n![image](https://user-images.githubusercontent.com/80432516/150420352-57452c80-ad25-423b-8251-645e541579ad.png)\r\n(n.b. this affects a lot more rules than L051)\r\n\r\nThis was introduced in #2234 in which docstrings such as\r\n```\r\n`INNER JOIN` must be fully qualified.\r\n```\r\nwere replaced with \r\n```\r\n``INNER JOIN`` must be fully qualified.\r\n```\r\nso that they appear as code blocks in Sphinx for docs.\r\n![image](https://user-images.githubusercontent.com/80432516/150420294-eb9d3127-db1d-457c-a637-d614e0267277.png)\r\n\r\nHowever, our rules will use the first line of these docstrings in the event that no `description` is provided to the lint results.\r\n\r\nThis doesn't look great on the CLI so we should fix this. As far as I'm aware there are two approaches for this:\r\n1. Pass a `description` to all the `LintResult`s.\r\n2. Update the code that gets the default description from the docstring to do something like, replace the double backticks with a single one, or remove them, or do something clever like make them bold for the CLI and remove them for non-CLI.\r\n\r\nMy strong preference is number 2, but I'm open to discussion as to how exactly we do this 😄 \r\n\r\n@barrywhart @tunetheweb \n", "task_id": "sqlfluff__sqlfluff-2386"}]