#!/usr/bin/env python3
import sys
from typing import Optional

from windowed_file import FileNotOpened, WindowedFile  # type: ignore


def main(path: Optional[str] = None, line_number: Optional[str] = None) -> None:
    if path is None:
        try:
            WindowedFile(exit_on_exception=False).print_window()
            # If this passes, then there was already a file open and we just show it again
            sys.exit(0)
        except FileNotOpened:
            print('Usage: open "<file>"')
            sys.exit(1)

    assert path is not None

    wf = WindowedFile(path=path)

    if line_number is not None:
        try:
            line_num = int(line_number)
        except ValueError:
            print('Usage: open "<file>" [<line_number>]')
            print("Error: <line_number> must be a number")
            sys.exit(1)
        if line_num > wf.n_lines:
            print(f"Warning: <line_number> ({line_num}) is greater than the number of lines in the file ({wf.n_lines})")
            print(f"Warning: Setting <line_number> to {wf.n_lines}")
            line_num = wf.n_lines
        elif line_num < 1:
            print(f"Warning: <line_number> ({line_num}) is less than 1")
            print("Warning: Setting <line_number> to 1")
            line_num = 1
    else:
        # Default to middle of window if no line number provided
        line_num = wf.first_line

    wf.goto(line_num - 1, mode="top")
    wf.print_window()


if __name__ == "__main__":
    args = sys.argv[1:]
    file_path = args[0] if args else None
    line_number = args[1] if len(args) > 1 else None
    main(file_path, line_number)
