#!/usr/bin/env python3

import argparse
from pathlib import Path
import subprocess
import sys
import os
import io

from registry import registry


def main() -> None:
    parser = argparse.ArgumentParser(description="Submit changes for review")
    parser.add_argument("-f", "--force", action="store_true", help="Force submit without review")
    args = parser.parse_args()

    repo_root = registry.get("ROOT", os.getenv("ROOT"))
    assert repo_root

    patch_path = Path("/root/model.patch")

    subprocess.run(
        f"git add -A && git diff --cached > {patch_path}",
        shell=True,
        stdout=subprocess.DEVNULL,
        stderr=subprocess.DEVNULL,
        cwd=repo_root,
    )

    patch = patch_path.read_text(errors="backslashreplace")

    submit_review_messages = registry.get("SUBMIT_REVIEW_MESSAGES", [])
    n_stages = len(submit_review_messages)
    current_stage = registry.get("SUBMIT_STAGE", 0)
    if not args.force and current_stage != n_stages:
        message = submit_review_messages[current_stage]
        message = message.replace("{{diff}}", patch)
        message = message.replace("{{problem_statement}}", registry.get("PROBLEM_STATEMENT", ""))
        registry["SUBMIT_STAGE"] = current_stage + 1
        print(message)
        sys.exit(0)

    print("<<SWE_AGENT_SUBMISSION>>")
    print(patch)
    print("<<SWE_AGENT_SUBMISSION>>")


if __name__ == "__main__":
    # There are some super strange "ascii can't decode x" errors when printing to the terminal
    # that can be solved with setting the default encoding for stdout
    # (note that python3.6 doesn't have the reconfigure method)
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")
    main()
