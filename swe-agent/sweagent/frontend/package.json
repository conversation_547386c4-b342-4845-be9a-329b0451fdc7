{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.8", "bootstrap": "^5.3.3", "pm2": "^5.3.1", "react": "^18.2.0", "react-bootstrap": "^2.10.2", "react-bootstrap-icons": "^1.11.4", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.5.0", "socket.io-client": "^4.7.5", "web-vitals": "^2.1.4", "use-immer": "^0.9.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://127.0.0.1:8000/"}