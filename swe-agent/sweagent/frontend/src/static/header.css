header {
  margin-bottom: 1em;
  margin-top: 1.5em;
  margin-left: auto;
  margin-right: auto;
  align-items: left;
  justify-content: left;
  display: flex;
  width: 54%;
}

@media only screen and (max-width: 2400px) {
  header {
    width: 80%;
  }
}

header button {
  background: none;
  border: none;
  padding: 0;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  text-decoration: none;
  cursor: pointer;
  position: relative;
}

header button::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #000; /* Change color as needed */
  transition: width 0.3s;
}

header button:hover::before {
  width: 100%;
}

/* to revert Link colors to default */
header a {
  text-decoration: none;
  color: inherit;
  margin: 0 0.75em;
}
