.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* .message {
  border: 1px solid #ccc;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
}

#container {
  display: flex;
  justify-content: space-between;
  padding: 10px;
}

#agentfeed, #environmentfeed {
  flex: 1;
  margin: 10px;
  max-width: 50%;
  overflow-y: auto;
  height: 500px;
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 5px;
} */

#demo hr {
  border-top: 1px dotted #bbb;
  margin: 0.5em 0;
  width: 100%;
}

#demo .panels {
  display: grid;
  height: 65vh;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr 2fr 1fr 1fr;

  .agentFeed,
  .envFeed,
  .logPanel {
    background-color: transparent;
    display: flex;
    flex-direction: column;
    height: 100%;
    margin: 0.2em;
    overflow: auto;
  }

  .envFeed {
    grid-column: 3 / 6;
    grid-row: 1 / 3;
    height: 95%;
  }

  .innerDiv {
    flex-grow: 1;
  }

  .scrollableDiv {
    height: 100%;
    overflow-y: hidden;
    background-color: white;
    border-radius: 0 0 0.5em 0.5em;
  }

  .scrollableDiv:hover {
    overflow: auto;
  }

  pre {
    white-space: pre-wrap;
    overflow-x: auto;
  }
}
