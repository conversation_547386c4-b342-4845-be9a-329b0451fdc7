keys.yaml
.swe-agent-human-history
.vscode/**
cov.xml
trajectories/**
!trajectories/demonstrations/**
data/**
*.env
!tools/*

# -------- automatically generated files --------

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
/lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Mac files
*.DS_Store

# Custom
keys*.cfg

# iPython Notebooks
*.ipynb

# Evaluation folders
results/
testbed/
temp/

# Ignore all YAML files in data/
data/*/ic-*
data/*/single-issues

# Fine tuning data
fine_tune/*.ipynb
fine_tune/subtasks/*.jsonl
temp*.jsonl

# Inspector
inspector/*.json

# Ignore all files in the private folder
private/

### Website

# dependencies
website/frontend/node_modules
website/frontend/package-lock.json
website/frontend/.pnp
*.pnp.js

# testing
website/frontend/coverage

# production
website/frontend/build

# misc
*.env.local
*.env.development.local
*.env.test.local
*.env.production.local
.api_key
*npm-debug.log*
*yarn-debug.log*
*yarn-error.log*


# demo yamls (for editing)
*.demo.yaml

# PyCharm
.idea/

# Disasm / Decomp output
*.decomp.json
*.disas.json

# SWE-smith
agent_conf
sb-cli-reports/
trajectories_sft/