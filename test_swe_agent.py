#!/usr/bin/env python3
"""
Test script for the SWE-Agent integration with the AI Coding Agent bridge.
"""

import os
import sys
import json
import time
import argparse
from pathlib import Path

# Add the project root to the Python path
project_root = Path(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, str(project_root))

try:
    from bridge.utils.env_loader import load_env_file
    from bridge.core.config import config
    from bridge.integrations.swe_agent_interface import SWEAgentInterface
    from bridge.integrations.server_manager import start_swe_agent_server, stop_swe_agent_server
except ImportError as e:
    print(f"Error importing bridge modules: {e}")
    print("Make sure you have installed the required dependencies:")
    print("  pip install -r requirements.txt")
    print("  pip install -e .")
    sys.exit(1)

def check_api_keys():
    """Check if the necessary API keys are set."""
    print("Checking API keys...")
    
    anthropic_key = config.get("api_keys", "anthropic")
    openai_key = config.get("api_keys", "openai")
    
    if not anthropic_key and not openai_key:
        print("❌ No API keys found. Please add your API keys to the .env file.")
        print("   You need at least one of: ANTHROPIC_API_KEY or OPENAI_API_KEY")
        return False
    
    if anthropic_key:
        print("✅ Anthropic API key found")
    
    if openai_key:
        print("✅ OpenAI API key found")
    
    return True

def test_swe_agent_server():
    """Test starting and stopping the SWE-Agent server."""
    print("\nTesting SWE-Agent server management...")
    
    # Start the server
    print("Starting SWE-Agent server...")
    server_process = start_swe_agent_server()
    
    if server_process is None:
        print("❌ Failed to start SWE-Agent server")
        return False
    
    print("✅ SWE-Agent server started successfully")
    
    # Wait a bit to let the server initialize
    time.sleep(2)
    
    # Stop the server
    print("Stopping SWE-Agent server...")
    result = stop_swe_agent_server(server_process)
    
    if result:
        print("✅ SWE-Agent server stopped successfully")
    else:
        print("❌ Failed to stop SWE-Agent server")
    
    return result

def test_swe_agent_interface():
    """Test the SWE-Agent interface."""
    print("\nTesting SWE-Agent interface...")
    
    try:
        # Initialize the interface
        print("Initializing SWE-Agent interface...")
        swe_agent = SWEAgentInterface()
        print("✅ SWE-Agent interface initialized successfully")
        
        # Test a simple run (this won't actually execute the agent)
        print("\nTesting SWE-Agent run configuration...")
        model_name = config.get("swe_agent", "model")
        print(f"Using model: {model_name}")
        
        # Get the current directory as a test repo path
        repo_path = os.path.abspath(os.getcwd())
        
        # Create a simple test problem statement
        problem_statement = "Write a function to calculate the factorial of a number."
        
        print(f"Problem statement: {problem_statement}")
        print(f"Repository path: {repo_path}")
        
        # Note: We're not actually running the agent here, just checking if the configuration works
        print("\nNOTE: This is just a configuration test. The agent will not be run.")
        print("To run the actual agent, use the bridge API or Vim plugin.")
        
        return True
    
    except Exception as e:
        print(f"❌ Error testing SWE-Agent interface: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Test the SWE-Agent integration")
    parser.add_argument("--env-file", help="Path to the .env file")
    parser.add_argument("--skip-api-keys", action="store_true", help="Skip API key check")
    parser.add_argument("--skip-server", action="store_true", help="Skip server test")
    parser.add_argument("--skip-interface", action="store_true", help="Skip interface test")
    
    args = parser.parse_args()
    
    # Load environment variables
    if args.env_file:
        env_vars = load_env_file(args.env_file)
    else:
        env_vars = load_env_file()
    
    success = True
    
    # Check API keys
    if not args.skip_api_keys:
        api_keys_success = check_api_keys()
        success = success and api_keys_success
        print()
    
    # Test SWE-Agent server
    if not args.skip_server:
        server_success = test_swe_agent_server()
        success = success and server_success
    
    # Test SWE-Agent interface
    if not args.skip_interface:
        interface_success = test_swe_agent_interface()
        success = success and interface_success
    
    if success:
        print("\n✅ All tests passed!")
        return 0
    else:
        print("\n❌ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
