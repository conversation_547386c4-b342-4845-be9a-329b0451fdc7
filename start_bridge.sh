#!/bin/bash
# Start the AI Coding Agent bridge

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is required but not found. Please install Python 3."
    exit 1
fi

# Check if the virtual environment exists
if [ ! -d "$SCRIPT_DIR/venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv "$SCRIPT_DIR/venv"
    
    # Activate the virtual environment
    source "$SCRIPT_DIR/venv/bin/activate"
    
    # Install dependencies
    echo "Installing dependencies..."
    pip install -r "$SCRIPT_DIR/requirements.txt"
    pip install -e "$SCRIPT_DIR"
else
    # Activate the virtual environment
    source "$SCRIPT_DIR/venv/bin/activate"
fi

# Start the bridge
echo "Starting AI Coding Agent bridge..."
python "$SCRIPT_DIR/start_bridge.py" "$@"
