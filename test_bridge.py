#!/usr/bin/env python3
"""
Test script for the AI Coding Agent bridge.
"""

import argparse
import json
import sys
import requests
from pathlib import Path

def test_api_server(host="localhost", port=8080):
    """Test the API server connection."""
    print(f"Testing API server connection at {host}:{port}...")
    
    try:
        # Test health endpoint
        response = requests.get(f"http://{host}:{port}/health", timeout=5)
        if response.status_code == 200:
            print("✅ API server health check successful")
        else:
            print(f"❌ API server health check failed: {response.status_code} {response.text}")
            return False
        
        # Test config endpoint
        response = requests.get(f"http://{host}:{port}/api/config", timeout=5)
        if response.status_code == 200:
            print("✅ API server config check successful")
            print(f"Current configuration: {json.dumps(response.json(), indent=2)}")
        else:
            print(f"❌ API server config check failed: {response.status_code} {response.text}")
            return False
            
        return True
    
    except requests.exceptions.ConnectionError:
        print(f"❌ Failed to connect to API server at {host}:{port}")
        print("   Make sure the bridge is running with: ./start_bridge.sh")
        return False
    except Exception as e:
        print(f"❌ Error testing API server: {e}")
        return False

def test_vim_integration(host="localhost", port=8081):
    """Test the Vim integration server connection using a socket."""
    print(f"Testing Vim integration server connection at {host}:{port}...")
    
    try:
        import socket
        
        # Create a socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        # Connect to the server
        sock.connect((host, port))
        
        # Send a ping request
        request = json.dumps({"command": "ping"}).encode("utf-8") + b"\n"
        sock.sendall(request)
        
        # Receive the response
        data = b""
        while True:
            chunk = sock.recv(4096)
            if not chunk:
                break
            data += chunk
            if b"\n" in data:
                break
        
        # Parse the response
        if data:
            response = json.loads(data.decode("utf-8"))
            if response.get("status") == "success":
                print("✅ Vim integration server ping successful")
                return True
            else:
                print(f"❌ Vim integration server ping failed: {response}")
                return False
        else:
            print("❌ No response from Vim integration server")
            return False
    
    except socket.timeout:
        print(f"❌ Connection to Vim integration server at {host}:{port} timed out")
        print("   Make sure the bridge is running with: ./start_bridge.sh")
        return False
    except ConnectionRefusedError:
        print(f"❌ Connection to Vim integration server at {host}:{port} refused")
        print("   Make sure the bridge is running with: ./start_bridge.sh")
        return False
    except Exception as e:
        print(f"❌ Error testing Vim integration server: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

def main():
    parser = argparse.ArgumentParser(description="Test the AI Coding Agent bridge")
    parser.add_argument("--api-host", default="localhost", help="API server host")
    parser.add_argument("--api-port", type=int, default=8080, help="API server port")
    parser.add_argument("--vim-host", default="localhost", help="Vim integration server host")
    parser.add_argument("--vim-port", type=int, default=8081, help="Vim integration server port")
    parser.add_argument("--skip-api", action="store_true", help="Skip API server test")
    parser.add_argument("--skip-vim", action="store_true", help="Skip Vim integration server test")
    
    args = parser.parse_args()
    
    success = True
    
    if not args.skip_api:
        api_success = test_api_server(args.api_host, args.api_port)
        success = success and api_success
        print()
    
    if not args.skip_vim:
        vim_success = test_vim_integration(args.vim_host, args.vim_port)
        success = success and vim_success
    
    if success:
        print("\n✅ All tests passed!")
        return 0
    else:
        print("\n❌ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
