#!/usr/bin/env python3
"""
Test script for Phase 3.4 enhanced workspace features.
Tests project analysis, file navigation, refactoring, and code quality features.
"""

import json
import logging
import os
import sys
import tempfile
import time
from pathlib import Path

# Add the bridge to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from bridge.workspace.project_analyzer import project_analyzer
from bridge.workspace.file_navigator import file_navigator
from bridge.workspace.refactoring_assistant import refactoring_assistant
from bridge.workspace.code_quality import code_quality_analyzer
from bridge.integrations.vim_client import send_command

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_test_project():
    """Create a temporary test project."""
    temp_dir = tempfile.mkdtemp(prefix="test_project_")
    
    # Create project structure
    os.makedirs(os.path.join(temp_dir, "src"))
    os.makedirs(os.path.join(temp_dir, "tests"))
    
    # Create main.py
    main_py = """
def calculate_area(radius):
    \"\"\"Calculate the area of a circle.\"\"\"
    import math
    return math.pi * radius * radius

def calculate_perimeter(radius):
    import math
    return 2 * math.pi * radius

class Calculator:
    \"\"\"A simple calculator class.\"\"\"
    
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def multiply(self, a, b):
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result
    
    def get_history(self):
        return self.history

if __name__ == "__main__":
    calc = Calculator()
    print(calc.add(5, 3))
    print(calc.multiply(4, 7))
    print("History:", calc.get_history())
"""
    
    with open(os.path.join(temp_dir, "src", "main.py"), "w") as f:
        f.write(main_py)
    
    # Create utils.py
    utils_py = """
def format_number(num):
    return f"{num:.2f}"

def validate_input(value):
    if not isinstance(value, (int, float)):
        raise ValueError("Input must be a number")
    return True

# This is a very long line that exceeds the recommended line length limit and should be flagged by quality analysis
def very_long_function_name_that_demonstrates_poor_naming_conventions_and_should_be_refactored(parameter_one, parameter_two, parameter_three, parameter_four, parameter_five, parameter_six):
    return parameter_one + parameter_two + parameter_three + parameter_four + parameter_five + parameter_six
"""
    
    with open(os.path.join(temp_dir, "src", "utils.py"), "w") as f:
        f.write(utils_py)
    
    # Create test file
    test_py = """
import unittest
from src.main import Calculator, calculate_area

class TestCalculator(unittest.TestCase):
    
    def setUp(self):
        self.calc = Calculator()
    
    def test_add(self):
        result = self.calc.add(2, 3)
        self.assertEqual(result, 5)
    
    def test_multiply(self):
        result = self.calc.multiply(4, 5)
        self.assertEqual(result, 20)

if __name__ == "__main__":
    unittest.main()
"""
    
    with open(os.path.join(temp_dir, "tests", "test_main.py"), "w") as f:
        f.write(test_py)
    
    # Create README.md
    readme = """# Test Project

This is a test project for workspace features.

## Features
- Calculator functionality
- Utility functions
- Unit tests
"""
    
    with open(os.path.join(temp_dir, "README.md"), "w") as f:
        f.write(readme)
    
    return temp_dir


def test_project_analyzer():
    """Test the project analyzer functionality."""
    print("\n=== Testing Project Analyzer ===")
    
    try:
        # Create test project
        project_path = create_test_project()
        print(f"✓ Created test project: {project_path}")
        
        # Analyze project
        analysis = project_analyzer.analyze_project(project_path)
        print(f"✓ Project analyzed: {analysis.project_name}")
        print(f"  - Main language: {analysis.main_language}")
        print(f"  - Total files: {analysis.metrics.total_files}")
        print(f"  - Total lines: {analysis.metrics.total_lines}")
        print(f"  - Total functions: {analysis.metrics.total_functions}")
        print(f"  - Total classes: {analysis.metrics.total_classes}")
        print(f"  - Dependencies: {len(analysis.dependencies)}")
        print(f"  - Issues: {len(analysis.issues)}")
        print(f"  - Recommendations: {len(analysis.recommendations)}")
        
        # Test cached analysis
        cached_analysis = project_analyzer.get_project_analysis(project_path)
        if cached_analysis:
            print("✓ Cached analysis retrieved successfully")
        
        # Test project stats
        stats = project_analyzer.get_project_stats()
        print(f"✓ Project stats: {stats['total_projects']} projects analyzed")
        
        # Cleanup
        import shutil
        shutil.rmtree(project_path)
        
        return True
        
    except Exception as e:
        print(f"✗ Project analyzer test failed: {e}")
        return False


def test_file_navigator():
    """Test the file navigator functionality."""
    print("\n=== Testing File Navigator ===")
    
    try:
        # Create test project
        project_path = create_test_project()
        print(f"✓ Created test project: {project_path}")
        
        # Index project
        success = file_navigator.index_project(project_path)
        print(f"✓ Project indexed: {success}")
        
        # Test file search
        file_matches = file_navigator.search_files(project_path, "main", max_results=10)
        print(f"✓ File search results: {len(file_matches)} matches for 'main'")
        for match in file_matches[:3]:
            print(f"  - {match.file_path} (score: {match.score:.2f})")
        
        # Test symbol search
        symbol_matches = file_navigator.search_symbols(project_path, "Calculator", max_results=10)
        print(f"✓ Symbol search results: {len(symbol_matches)} matches for 'Calculator'")
        for match in symbol_matches[:3]:
            print(f"  - {match.symbol.name} in {match.file_path} (score: {match.score:.2f})")
        
        # Test navigation suggestions
        suggestions = file_navigator.get_navigation_suggestions(project_path, "src/main.py")
        print(f"✓ Navigation suggestions: {len(suggestions)} suggestions")
        for suggestion in suggestions[:3]:
            print(f"  - {suggestion.type}: {suggestion.description}")
        
        # Test file outline
        outline = file_navigator.get_file_outline(project_path, "src/main.py")
        print(f"✓ File outline: {len(outline)} symbols")
        for symbol in outline[:5]:
            print(f"  - {symbol['type']}: {symbol['name']} (line {symbol['line']})")
        
        # Test navigation stats
        stats = file_navigator.get_navigation_stats()
        print(f"✓ Navigation stats: {stats['total_files']} files, {stats['total_symbols']} symbols")
        
        # Cleanup
        import shutil
        shutil.rmtree(project_path)
        
        return True
        
    except Exception as e:
        print(f"✗ File navigator test failed: {e}")
        return False


def test_refactoring_assistant():
    """Test the refactoring assistant functionality."""
    print("\n=== Testing Refactoring Assistant ===")
    
    try:
        # Test Python code analysis
        python_code = """
def very_long_function_with_too_many_parameters(param1, param2, param3, param4, param5, param6, param7):
    # This function is too long and has too many parameters
    result = param1 + param2
    result += param3 + param4
    result += param5 + param6
    result += param7
    
    # More code to make it longer
    for i in range(10):
        result += i
        if result > 100:
            result = result / 2
        else:
            result = result * 2
    
    # Even more code
    if result > 50:
        for j in range(5):
            result += j
            if result > 200:
                break
    
    return result

class MyClass:
    def method_without_docstring(self):
        pass
"""
        
        report = refactoring_assistant.analyze_file("test.py", python_code, "python")
        print(f"✓ Refactoring analysis completed")
        print(f"  - File: {report.file_path}")
        print(f"  - Suggestions: {len(report.suggestions)}")
        print(f"  - Summary: {report.summary}")
        
        for suggestion in report.suggestions[:5]:
            print(f"  - {suggestion.rule.name}: {suggestion.explanation}")
        
        # Test refactoring rules
        rules = refactoring_assistant.get_refactoring_rules("python")
        print(f"✓ Refactoring rules: {len(rules)} Python rules available")
        
        # Test JavaScript code
        js_code = """
var oldVariable = 10;
console.log("Debug message");

function oldFunction() {
    return oldVariable + 5;
}
"""
        
        js_report = refactoring_assistant.analyze_file("test.js", js_code, "javascript")
        print(f"✓ JavaScript analysis: {len(js_report.suggestions)} suggestions")
        
        return True
        
    except Exception as e:
        print(f"✗ Refactoring assistant test failed: {e}")
        return False


def test_code_quality_analyzer():
    """Test the code quality analyzer functionality."""
    print("\n=== Testing Code Quality Analyzer ===")
    
    try:
        # Test Python code quality
        python_code = """
import math

def calculate_circle_area(radius):
    \"\"\"Calculate the area of a circle.\"\"\"
    return math.pi * radius * radius

def complex_function(a, b, c, d, e, f):
    # This function has high complexity
    result = 0
    
    if a > 0:
        if b > 0:
            if c > 0:
                if d > 0:
                    if e > 0:
                        result = a + b + c + d + e + f
                    else:
                        result = a + b + c + d + f
                else:
                    result = a + b + c + f
            else:
                result = a + b + f
        else:
            result = a + f
    else:
        result = f
    
    return result

class WellDocumentedClass:
    \"\"\"A well-documented class.\"\"\"
    
    def __init__(self):
        self.value = 0
    
    def get_value(self):
        \"\"\"Get the current value.\"\"\"
        return self.value

class PoorlyDocumentedClass:
    def __init__(self):
        self.data = []
    
    def process_data(self):
        pass
"""
        
        report = code_quality_analyzer.analyze_file("test.py", python_code, "python")
        print(f"✓ Quality analysis completed")
        print(f"  - File: {report.file_path}")
        print(f"  - Overall score: {report.overall_score:.2f}")
        print(f"  - Metrics: {len(report.metrics)}")
        print(f"  - Issues: {len(report.issues)}")
        print(f"  - Recommendations: {len(report.recommendations)}")
        
        # Show metrics
        for metric in report.metrics:
            print(f"  - {metric.name}: {metric.value:.2f} (score: {metric.score:.2f})")
        
        # Show issues
        for issue in report.issues[:3]:
            print(f"  - {issue.type}: {issue.message}")
        
        # Show recommendations
        for rec in report.recommendations:
            print(f"  - {rec}")
        
        return True
        
    except Exception as e:
        print(f"✗ Code quality analyzer test failed: {e}")
        return False


def test_vim_workspace_integration():
    """Test the Vim workspace integration."""
    print("\n=== Testing Vim Workspace Integration ===")
    
    try:
        # Test ping first
        response = send_command("ping")
        if response.get("status") != "success":
            print("✗ Vim integration server not responding")
            return False
        print("✓ Vim integration server is running")
        
        # Create test project
        project_path = create_test_project()
        print(f"✓ Created test project: {project_path}")
        
        # Test project analysis
        response = send_command("project_analyze", project_path=project_path)
        if response.get("status") == "success":
            analysis = response.get("analysis", {})
            print(f"✓ Project analysis via Vim: {analysis.get('project_name', 'Unknown')}")
        else:
            print(f"✗ Project analysis failed: {response.get('message', 'Unknown error')}")
        
        # Test file search
        response = send_command("file_search", project_path=project_path, query="main")
        if response.get("status") == "success":
            matches = response.get("matches", [])
            print(f"✓ File search via Vim: {len(matches)} matches")
        else:
            print(f"✗ File search failed: {response.get('message', 'Unknown error')}")
        
        # Test symbol search
        response = send_command("symbol_search", project_path=project_path, query="Calculator")
        if response.get("status") == "success":
            matches = response.get("matches", [])
            print(f"✓ Symbol search via Vim: {len(matches)} matches")
        else:
            print(f"✗ Symbol search failed: {response.get('message', 'Unknown error')}")
        
        # Test file outline
        response = send_command("file_outline", project_path=project_path, file_path="src/main.py")
        if response.get("status") == "success":
            outline = response.get("outline", [])
            print(f"✓ File outline via Vim: {len(outline)} symbols")
        else:
            print(f"✗ File outline failed: {response.get('message', 'Unknown error')}")
        
        # Test refactoring analysis
        with open(os.path.join(project_path, "src", "main.py"), "r") as f:
            content = f.read()
        
        response = send_command("refactor_analyze", file_path="src/main.py", content=content, language="python")
        if response.get("status") == "success":
            report = response.get("report", {})
            suggestions = report.get("suggestions", [])
            print(f"✓ Refactoring analysis via Vim: {len(suggestions)} suggestions")
        else:
            print(f"✗ Refactoring analysis failed: {response.get('message', 'Unknown error')}")
        
        # Test quality analysis
        response = send_command("quality_analyze", file_path="src/main.py", content=content, language="python")
        if response.get("status") == "success":
            report = response.get("report", {})
            score = report.get("overall_score", 0)
            print(f"✓ Quality analysis via Vim: score {score:.2f}")
        else:
            print(f"✗ Quality analysis failed: {response.get('message', 'Unknown error')}")
        
        # Cleanup
        import shutil
        shutil.rmtree(project_path)
        
        return True
        
    except Exception as e:
        print(f"✗ Vim workspace integration test failed: {e}")
        return False


def main():
    """Run all Phase 3.4 workspace tests."""
    print("Starting Phase 3.4 Enhanced Workspace Features Tests")
    print("=" * 60)
    
    tests = [
        ("Project Analyzer", test_project_analyzer),
        ("File Navigator", test_file_navigator),
        ("Refactoring Assistant", test_refactoring_assistant),
        ("Code Quality Analyzer", test_code_quality_analyzer),
        ("Vim Workspace Integration", test_vim_workspace_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} test PASSED")
            else:
                print(f"✗ {test_name} test FAILED")
        except Exception as e:
            print(f"✗ {test_name} test ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:30} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Phase 3.4 workspace tests PASSED!")
        return 0
    else:
        print("❌ Some Phase 3.4 workspace tests FAILED!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
