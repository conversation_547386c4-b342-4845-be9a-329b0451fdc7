#!/bin/bash
# <PERSON><PERSON>t to activate the virtual environment and run the AI Coding Agent bridge

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Check if the virtual environment exists
if [ ! -d "$SCRIPT_DIR/venv" ]; then
    echo "Virtual environment not found. Creating one..."
    python3 -m venv "$SCRIPT_DIR/venv"
    
    # Activate the virtual environment
    source "$SCRIPT_DIR/venv/bin/activate"
    
    # Install dependencies
    echo "Installing dependencies..."
    pip install -r "$SCRIPT_DIR/requirements.txt"
    pip install -e "$SCRIPT_DIR"
else
    # Activate the virtual environment
    source "$SCRIPT_DIR/venv/bin/activate"
fi

# Print activation message
echo "Virtual environment activated. You can now run the AI Coding Agent bridge with:"
echo "python -m bridge"
echo ""
echo "Or test the bridge with:"
echo "python test_bridge.py"
echo ""
echo "To deactivate the virtual environment when done, run:"
echo "deactivate"

# Keep the shell open with the activated environment
exec "${SHELL:-bash}"
