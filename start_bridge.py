#!/usr/bin/env python3
"""
Launcher script for the AI Coding Agent bridge.
"""

import os
import sys
import argparse
from pathlib import Path

# Add the project root to the Python path
project_root = Path(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, str(project_root))

# Import the bridge module
try:
    from bridge.__main__ import main
except ImportError as e:
    print(f"Error importing bridge module: {e}")
    print("Make sure you have installed the required dependencies:")
    print("  pip install -r requirements.txt")
    sys.exit(1)

if __name__ == "__main__":
    # Call the main function from bridge.__main__
    main()
