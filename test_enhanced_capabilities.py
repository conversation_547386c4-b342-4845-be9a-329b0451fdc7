#!/usr/bin/env python3
"""
Test script for enhanced AI Coding Agent capabilities.
Tests file operations, terminal execution, and workspace navigation.
"""

import requests
import json
import time
import os

# Configuration
BASE_URL = "http://localhost:8080"
TEST_PROJECT_PATH = "/u/Arun Dev/Python Projects/AI-Coding-Agent"

def test_file_operations():
    """Test file operation endpoints."""
    print("🔧 Testing File Operations...")
    
    # Test file reading
    print("  📖 Testing file read...")
    response = requests.post(f"{BASE_URL}/api/files/read", json={
        "file_path": "README.md"
    })
    
    if response.status_code == 200:
        data = response.json()
        print(f"    ✅ Successfully read file: {data['file_path']} ({data['size']} bytes)")
    else:
        print(f"    ❌ Failed to read file: {response.status_code} - {response.text}")
    
    # Test file creation
    print("  📝 Testing file creation...")
    test_content = """# Test File
This is a test file created by the AI Coding Agent.

def hello_world():
    print("Hello from AI Coding Agent!")

if __name__ == "__main__":
    hello_world()
"""
    
    response = requests.post(f"{BASE_URL}/api/files/create", json={
        "file_path": "test_ai_agent.py",
        "content": test_content,
        "create_dirs": True
    })
    
    if response.status_code == 200:
        data = response.json()
        print(f"    ✅ Successfully created file: {data['file_path']}")
    else:
        print(f"    ❌ Failed to create file: {response.status_code} - {response.text}")
    
    # Test directory listing
    print("  📁 Testing directory listing...")
    response = requests.post(f"{BASE_URL}/api/files/list", json={
        "directory_path": ".",
        "max_depth": 1
    })
    
    if response.status_code == 200:
        data = response.json()
        print(f"    ✅ Successfully listed directory: {data['count']} items found")
    else:
        print(f"    ❌ Failed to list directory: {response.status_code} - {response.text}")

def test_terminal_operations():
    """Test terminal operation endpoints."""
    print("\n💻 Testing Terminal Operations...")
    
    # Test command execution
    print("  ⚡ Testing command execution...")
    response = requests.post(f"{BASE_URL}/api/terminal/execute", json={
        "command": "echo 'Hello from AI Coding Agent Terminal!'",
        "capture_output": True
    })
    
    if response.status_code == 200:
        data = response.json()
        print(f"    ✅ Command executed successfully:")
        print(f"        Return code: {data['return_code']}")
        print(f"        Output: {data['stdout'].strip()}")
    else:
        print(f"    ❌ Failed to execute command: {response.status_code} - {response.text}")
    
    # Test terminal session creation
    print("  🖥️ Testing terminal session creation...")
    response = requests.post(f"{BASE_URL}/api/terminal/sessions", json={
        "working_directory": ".",
        "session_name": "test_session"
    })
    
    if response.status_code == 200:
        data = response.json()
        session_id = data['session_id']
        print(f"    ✅ Terminal session created: {session_id}")
        
        # Test sending input to session
        print("  📤 Testing terminal input...")
        response = requests.post(f"{BASE_URL}/api/terminal/sessions/{session_id}/input", json={
            "input": "pwd"
        })
        
        if response.status_code == 200:
            print("    ✅ Input sent to terminal session")
            
            # Wait a moment for command to execute
            time.sleep(1)
            
            # Get output
            print("  📥 Testing terminal output...")
            response = requests.get(f"{BASE_URL}/api/terminal/sessions/{session_id}/output")
            
            if response.status_code == 200:
                data = response.json()
                print(f"    ✅ Retrieved terminal output: {len(data['output'])} entries")
            else:
                print(f"    ❌ Failed to get terminal output: {response.status_code}")
        else:
            print(f"    ❌ Failed to send input: {response.status_code}")
        
        # Clean up session
        requests.delete(f"{BASE_URL}/api/terminal/sessions/{session_id}")
        print("    🧹 Terminal session cleaned up")
    else:
        print(f"    ❌ Failed to create terminal session: {response.status_code} - {response.text}")

def test_workspace_operations():
    """Test workspace operation endpoints."""
    print("\n🗂️ Testing Workspace Operations...")
    
    # Test project analysis
    print("  🔍 Testing project analysis...")
    response = requests.post(f"{BASE_URL}/api/workspace/analyze", json={
        "project_path": TEST_PROJECT_PATH
    })
    
    if response.status_code == 200:
        data = response.json()
        analysis = data['analysis']
        print(f"    ✅ Project analyzed successfully:")
        print(f"        Project type: {analysis.get('project_type', 'Unknown')}")
        print(f"        Total files: {analysis.get('total_files', 0)}")
        print(f"        Languages: {', '.join(analysis.get('languages', []))}")
    else:
        print(f"    ❌ Failed to analyze project: {response.status_code} - {response.text}")
    
    # Test file search
    print("  🔎 Testing file search...")
    response = requests.post(f"{BASE_URL}/api/workspace/navigation/search/files", json={
        "project_path": TEST_PROJECT_PATH,
        "query": "py",
        "max_results": 5
    })
    
    if response.status_code == 200:
        data = response.json()
        matches = data['matches']
        print(f"    ✅ File search completed: {len(matches)} matches found")
        for match in matches[:3]:
            print(f"        📄 {match['file_path']} (score: {match['score']:.2f})")
    else:
        print(f"    ❌ Failed to search files: {response.status_code} - {response.text}")

def test_chat_integration():
    """Test chat integration with enhanced capabilities."""
    print("\n💬 Testing Chat Integration...")
    
    # Create chat session
    print("  🆕 Creating chat session...")
    response = requests.post(f"{BASE_URL}/api/chat/sessions", json={
        "title": "Test Enhanced Capabilities",
        "context": {
            "project_root": TEST_PROJECT_PATH,
            "file_path": "README.md",
            "language": "markdown"
        }
    })
    
    if response.status_code == 200:
        data = response.json()
        session_id = data['session_id']
        print(f"    ✅ Chat session created: {session_id}")
        
        # Test sending a message that should trigger tool usage
        print("  📨 Testing tool-enabled chat message...")
        response = requests.post(f"{BASE_URL}/api/chat/sessions/{session_id}/messages", json={
            "message": "Can you list the files in the current directory?",
            "context": {
                "project_root": TEST_PROJECT_PATH
            }
        })
        
        if response.status_code == 200:
            data = response.json()
            print(f"    ✅ Chat message sent successfully")
            print(f"        Response: {data.get('response', 'No response')[:100]}...")
        else:
            print(f"    ❌ Failed to send chat message: {response.status_code} - {response.text}")
    else:
        print(f"    ❌ Failed to create chat session: {response.status_code} - {response.text}")

def cleanup():
    """Clean up test files."""
    print("\n🧹 Cleaning up...")
    
    # Remove test file if it exists
    if os.path.exists("test_ai_agent.py"):
        os.remove("test_ai_agent.py")
        print("  ✅ Removed test file")

def main():
    """Run all tests."""
    print("🚀 Testing Enhanced AI Coding Agent Capabilities")
    print("=" * 50)
    
    try:
        # Check if server is running
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print("❌ Bridge server is not running. Please start it first.")
            return
        
        print("✅ Bridge server is running")
        
        # Run tests
        test_file_operations()
        test_terminal_operations()
        test_workspace_operations()
        test_chat_integration()
        
        print("\n🎉 All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to bridge server. Please ensure it's running on localhost:8080")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    finally:
        cleanup()

if __name__ == "__main__":
    main()
