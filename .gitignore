# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
swe_venv/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
logs/
*.log

# Local configuration
.env
config.local.yaml

# SWE-Agent specific
swe-agent/trajectories/

# Vim plugin specific
vim-extension/dist/

# Bridge specific
.bridge_cache/

# Temporary files
*.tmp
*.bak
*.backup

# Test files
.coverage
htmlcov/
.pytest_cache/
.tox/

# Documentation build
docs/_build/

# OS specific
Thumbs.db
.directory

# Jupyter Notebooks
.ipynb_checkpoints
