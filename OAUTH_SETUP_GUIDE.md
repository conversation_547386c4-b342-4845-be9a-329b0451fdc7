# OAuth Provider Setup Guide

## 🔧 Quick Setup for Development

### 1. GitHub OAuth Application

1. Go to GitHub Settings → Developer settings → OAuth Apps
2. Click "New OAuth App"
3. Fill in the details:
   ```
   Application name: AI Coding Agent
   Homepage URL: http://localhost:8080
   Authorization callback URL: http://localhost:8080/api/auth/oauth/github/callback
   ```
4. Copy the Client ID and Client Secret
5. Set environment variables:
   ```bash
   export GITHUB_CLIENT_ID="your_client_id_here"
   export GITHUB_CLIENT_SECRET="your_client_secret_here"
   ```

### 2. Google OAuth Application

1. Go to Google Cloud Console → APIs & Services → Credentials
2. Click "Create Credentials" → OAuth 2.0 Client IDs
3. Configure OAuth consent screen if needed
4. Set application type to "Web application"
5. Add authorized redirect URIs:
   ```
   http://localhost:8080/api/auth/oauth/google/callback
   ```
6. Set environment variables:
   ```bash
   export GOOGLE_CLIENT_ID="your_client_id_here"
   export GOOGLE_CLIENT_SECRET="your_client_secret_here"
   ```

### 3. Microsoft OAuth Application

1. Go to Azure Portal → App registrations
2. Click "New registration"
3. Fill in the details:
   ```
   Name: AI Coding Agent
   Supported account types: Accounts in any organizational directory and personal Microsoft accounts
   Redirect URI: http://localhost:8080/api/auth/oauth/microsoft/callback
   ```
4. Go to Certificates & secrets → New client secret
5. Set environment variables:
   ```bash
   export MICROSOFT_CLIENT_ID="your_client_id_here"
   export MICROSOFT_CLIENT_SECRET="your_client_secret_here"
   export MICROSOFT_TENANT_ID="common"  # or your specific tenant ID
   ```

## 🚀 Test OAuth Setup

### Start the Server
```bash
# Install dependencies
pip install PyJWT cryptography requests

# Start the OAuth test server
python simple_oauth_test.py
```

### Test OAuth Endpoints
```bash
# Check available providers
curl http://localhost:8080/api/auth/providers

# Check system health
curl http://localhost:8080/api/auth/health

# Initiate GitHub OAuth (will redirect to GitHub)
curl http://localhost:8080/api/auth/oauth/github/login
```

## 🔐 Production Configuration

### Environment Variables Template
Create a `.env` file:
```bash
# OAuth Providers
GITHUB_CLIENT_ID=your_production_github_client_id
GITHUB_CLIENT_SECRET=your_production_github_client_secret
GITHUB_REDIRECT_URI=https://yourdomain.com/api/auth/oauth/github/callback

GOOGLE_CLIENT_ID=your_production_google_client_id
GOOGLE_CLIENT_SECRET=your_production_google_client_secret
GOOGLE_REDIRECT_URI=https://yourdomain.com/api/auth/oauth/google/callback

MICROSOFT_CLIENT_ID=your_production_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_production_microsoft_client_secret
MICROSOFT_TENANT_ID=your_tenant_id_or_common
MICROSOFT_REDIRECT_URI=https://yourdomain.com/api/auth/oauth/microsoft/callback

# JWT Configuration
JWT_ISSUER=your-company-name
JWT_AUDIENCE=your-api-audience
JWT_ACCESS_TOKEN_TTL=3600
JWT_REFRESH_TOKEN_TTL=2592000

# Generate RSA keys for production:
# openssl genrsa -out private_key.pem 2048
# openssl rsa -in private_key.pem -pubout -out public_key.pem
JWT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
JWT_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----"

# Security Settings
ADMIN_EMAIL=<EMAIL>
SESSION_TTL=86400
RATE_LIMIT_PER_MINUTE=1000
MAX_FILE_SIZE_MB=10

# Server Configuration
BRIDGE_HOST=0.0.0.0
BRIDGE_PORT=8080
LOG_LEVEL=INFO
```

### Production Checklist
- [ ] Update OAuth redirect URIs to production domains
- [ ] Generate secure RSA key pairs
- [ ] Configure HTTPS with valid SSL certificates
- [ ] Set up database for token storage (Redis recommended)
- [ ] Configure rate limiting and monitoring
- [ ] Set up backup and recovery procedures
- [ ] Configure logging and alerting

## 🧪 Testing OAuth Flow

### Manual Testing
1. Start the server: `python -m bridge.api.api_server`
2. Open browser: `http://localhost:8080/api/auth/providers`
3. Click on a provider login URL
4. Complete OAuth flow with provider
5. Verify token creation and user profile

### Automated Testing
```bash
# Run component tests
python simple_oauth_test.py

# Run integration tests
python test_oauth_system.py
```

## 🔍 Troubleshooting

### Common Issues

**"No OAuth providers configured"**
- Check environment variables are set correctly
- Verify OAuth applications are created in provider consoles
- Ensure redirect URIs match exactly

**"Invalid client credentials"**
- Double-check Client ID and Client Secret
- Verify OAuth application is active
- Check for typos in environment variables

**"Redirect URI mismatch"**
- Ensure redirect URI in OAuth app matches the one in your config
- Check for trailing slashes or protocol mismatches
- Verify the callback endpoint is accessible

**"Token validation failed"**
- Check JWT keys are properly configured
- Verify token hasn't expired
- Ensure clock synchronization between services

### Debug Mode
Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
python -m bridge.api.api_server
```

## 📚 Additional Resources

- [OAuth 2.0 RFC](https://tools.ietf.org/html/rfc6749)
- [JWT RFC](https://tools.ietf.org/html/rfc7519)
- [GitHub OAuth Documentation](https://docs.github.com/en/developers/apps/building-oauth-apps)
- [Google OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Microsoft OAuth Documentation](https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-auth-code-flow)

## 🎯 Next Steps

After OAuth setup is complete:
1. Test all provider flows
2. Integrate with VS Code extension
3. Set up production deployment
4. Configure monitoring and analytics
5. Implement additional security features
