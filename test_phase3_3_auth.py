#!/usr/bin/env python3
"""
Test script for Phase 3.3 OAuth authentication functionality.
Tests OAuth provider, JWT token manager, and authentication endpoints.
"""

import json
import logging
import requests
import sys
import time
from pathlib import Path
from datetime import datetime, <PERSON><PERSON><PERSON>

# Add the bridge to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from bridge.auth.oauth_provider import oauth_provider, GrantType
from bridge.auth.token_manager import token_manager, TokenType
from bridge.auth.session_auth import session_auth

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_oauth_provider():
    """Test the OAuth provider functionality."""
    print("\n=== Testing OAuth Provider ===")
    
    try:
        # Test client registration
        client = oauth_provider.register_client(
            client_name="Test Client",
            redirect_uris=["http://localhost:8080/callback"],
            grant_types=[GrantType.AUTHORIZATION_CODE, GrantType.CLIENT_CREDENTIALS],
            scope=["read", "write"]
        )
        print(f"✓ OAuth client registered: {client.client_id}")
        
        # Test client validation
        is_valid = oauth_provider.validate_client(client.client_id, client.client_secret)
        print(f"✓ Client validation: {is_valid}")
        
        # Test authorization code creation
        auth_code = oauth_provider.create_authorization_code(
            client_id=client.client_id,
            user_id="test_user",
            redirect_uri="http://localhost:8080/callback",
            scope=["read", "write"]
        )
        print(f"✓ Authorization code created: {auth_code[:8]}...")
        
        # Test authorization code exchange
        access_token, refresh_token, expires_in = oauth_provider.exchange_authorization_code(
            code=auth_code,
            client_id=client.client_id,
            client_secret=client.client_secret,
            redirect_uri="http://localhost:8080/callback"
        )
        print(f"✓ Tokens exchanged: access_token={access_token[:8]}..., expires_in={expires_in}")
        
        # Test device code flow
        device_code_obj = oauth_provider.create_device_code(client.client_id, ["read"])
        print(f"✓ Device code created: {device_code_obj.user_code}")
        
        # Authorize device code
        success = oauth_provider.authorize_device_code(device_code_obj.user_code, "test_user")
        print(f"✓ Device code authorized: {success}")
        
        # Poll device code
        device_access_token, device_refresh_token, device_expires_in, error = oauth_provider.poll_device_code(
            device_code_obj.device_code, client.client_id
        )
        if not error:
            print(f"✓ Device code flow completed: {device_access_token[:8]}...")
        else:
            print(f"✗ Device code flow error: {error}")
        
        return True
        
    except Exception as e:
        print(f"✗ OAuth provider test failed: {e}")
        return False


def test_token_manager():
    """Test the JWT token manager functionality."""
    print("\n=== Testing Token Manager ===")
    
    try:
        # Test access token creation
        access_token = token_manager.create_access_token(
            user_id="test_user",
            client_id="test_client",
            scope=["read", "write"]
        )
        print(f"✓ Access token created: {access_token[:20]}...")
        
        # Test token validation
        claims = token_manager.validate_token(access_token)
        if claims:
            print(f"✓ Token validated: user={claims.sub}, client={claims.aud}")
        else:
            print("✗ Token validation failed")
            return False
        
        # Test refresh token creation
        refresh_token = token_manager.create_refresh_token(
            user_id="test_user",
            client_id="test_client",
            scope=["read", "write"]
        )
        print(f"✓ Refresh token created: {refresh_token[:20]}...")
        
        # Test token refresh
        new_tokens = token_manager.refresh_access_token(refresh_token)
        if new_tokens:
            new_access_token, new_refresh_token = new_tokens
            print(f"✓ Tokens refreshed: {new_access_token[:20]}...")
        else:
            print("✗ Token refresh failed")
            return False
        
        # Test ID token creation
        id_token = token_manager.create_id_token(
            user_id="test_user",
            client_id="test_client",
            user_info={"name": "Test User", "email": "<EMAIL>"}
        )
        print(f"✓ ID token created: {id_token[:20]}...")
        
        # Test token revocation
        token_revoked = token_manager.revoke_token(claims.jti)
        print(f"✓ Token revoked: {token_revoked}")
        
        # Test token stats
        stats = token_manager.get_token_stats()
        print(f"✓ Token stats: {stats['total_tokens']} total, {stats['active_tokens']} active")
        
        # Test cleanup
        cleaned = token_manager.cleanup_expired_tokens()
        print(f"✓ Cleaned up {cleaned} expired tokens")
        
        return True
        
    except Exception as e:
        print(f"✗ Token manager test failed: {e}")
        return False


def test_session_auth():
    """Test the session authentication functionality."""
    print("\n=== Testing Session Auth ===")
    
    try:
        # Test user creation
        user = session_auth.create_user(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            preferences={"theme": "dark"}
        )
        print(f"✓ User created: {user.username} ({user.user_id})")
        
        # Test user retrieval
        retrieved_user = session_auth.get_user(user.user_id)
        if retrieved_user:
            print(f"✓ User retrieved: {retrieved_user.username}")
        else:
            print("✗ User retrieval failed")
            return False
        
        # Test user by username
        user_by_name = session_auth.get_user_by_username("testuser")
        if user_by_name:
            print(f"✓ User found by username: {user_by_name.user_id}")
        else:
            print("✗ User lookup by username failed")
            return False
        
        # Test API key creation
        api_key = session_auth.create_api_key(
            user_id=user.user_id,
            name="Test API Key",
            scope=["read", "write"],
            expires_days=30
        )
        print(f"✓ API key created: {api_key[:20]}...")
        
        # Test session listing
        sessions = session_auth.list_user_sessions(user.user_id)
        print(f"✓ User sessions listed: {len(sessions)} sessions")
        
        # Test auth stats
        stats = session_auth.get_auth_stats()
        print(f"✓ Auth stats: {stats['total_users']} users, {stats['total_tokens']} tokens")
        
        return True
        
    except Exception as e:
        print(f"✗ Session auth test failed: {e}")
        return False


def test_auth_endpoints():
    """Test the authentication API endpoints."""
    print("\n=== Testing Auth Endpoints ===")
    
    base_url = "http://localhost:8080"
    
    try:
        # Test health endpoint
        response = requests.get(f"{base_url}/auth/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✓ Auth health check: {health_data['status']}")
        else:
            print(f"✗ Auth health check failed: {response.status_code}")
            return False
        
        # Test device authorization
        device_response = requests.post(f"{base_url}/auth/device", data={
            "client_id": "ai-coding-agent-default",
            "scope": "read write"
        }, timeout=5)
        
        if device_response.status_code == 200:
            device_data = device_response.json()
            print(f"✓ Device authorization: {device_data['user_code']}")
            
            # Test device verification page
            verify_response = requests.get(f"{base_url}/auth/device", timeout=5)
            if verify_response.status_code == 200:
                print("✓ Device verification page accessible")
            else:
                print(f"✗ Device verification page failed: {verify_response.status_code}")
        else:
            print(f"✗ Device authorization failed: {device_response.status_code}")
            return False
        
        # Test client credentials grant
        token_response = requests.post(f"{base_url}/auth/token", data={
            "grant_type": "client_credentials",
            "client_id": "ai-coding-agent-default",
            "client_secret": oauth_provider.get_client("ai-coding-agent-default").client_secret,
            "scope": "read write"
        }, timeout=5)
        
        if token_response.status_code == 200:
            token_data = token_response.json()
            access_token = token_data['access_token']
            print(f"✓ Client credentials token: {access_token[:20]}...")
            
            # Test userinfo endpoint with token
            userinfo_response = requests.get(f"{base_url}/auth/userinfo", 
                                           headers={"Authorization": f"Bearer {access_token}"}, 
                                           timeout=5)
            if userinfo_response.status_code == 200:
                userinfo_data = userinfo_response.json()
                print(f"✓ Userinfo: {userinfo_data['username']}")
            else:
                print(f"✗ Userinfo failed: {userinfo_response.status_code}")
        else:
            print(f"✗ Client credentials grant failed: {token_response.status_code}")
            return False
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ Auth endpoints test failed (server not running?): {e}")
        return False
    except Exception as e:
        print(f"✗ Auth endpoints test failed: {e}")
        return False


def test_auth_integration():
    """Test authentication integration with existing endpoints."""
    print("\n=== Testing Auth Integration ===")
    
    base_url = "http://localhost:8080"
    
    try:
        # Get access token
        token_response = requests.post(f"{base_url}/auth/token", data={
            "grant_type": "client_credentials",
            "client_id": "ai-coding-agent-default",
            "client_secret": oauth_provider.get_client("ai-coding-agent-default").client_secret,
            "scope": "read write"
        }, timeout=5)
        
        if token_response.status_code != 200:
            print("✗ Could not get access token for integration test")
            return False
        
        access_token = token_response.json()['access_token']
        headers = {"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}
        
        # Test authenticated completion request
        completion_data = {
            "file_path": "test.py",
            "content": "def hello():\n    print(",
            "cursor_line": 1,
            "cursor_column": 10,
            "language": "python"
        }
        
        completion_response = requests.post(f"{base_url}/api/completion/complete", 
                                          json=completion_data, 
                                          headers=headers, 
                                          timeout=10)
        
        if completion_response.status_code == 200:
            print("✓ Authenticated completion request successful")
        else:
            print(f"✗ Authenticated completion request failed: {completion_response.status_code}")
        
        # Test authenticated chat session creation
        chat_data = {
            "title": "Test Auth Chat",
            "context": {
                "file_path": "test.py",
                "language": "python"
            }
        }
        
        chat_response = requests.post(f"{base_url}/api/chat/sessions", 
                                    json=chat_data, 
                                    headers=headers, 
                                    timeout=10)
        
        if chat_response.status_code == 200:
            print("✓ Authenticated chat session creation successful")
        else:
            print(f"✗ Authenticated chat session creation failed: {chat_response.status_code}")
        
        # Test unauthenticated request (should still work with optional auth)
        unauth_completion_response = requests.post(f"{base_url}/api/completion/complete", 
                                                 json=completion_data, 
                                                 timeout=10)
        
        if unauth_completion_response.status_code == 200:
            print("✓ Unauthenticated completion request successful (optional auth)")
        else:
            print(f"✗ Unauthenticated completion request failed: {unauth_completion_response.status_code}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ Auth integration test failed (server not running?): {e}")
        return False
    except Exception as e:
        print(f"✗ Auth integration test failed: {e}")
        return False


def main():
    """Run all Phase 3.3 authentication tests."""
    print("Starting Phase 3.3 OAuth Authentication Tests")
    print("=" * 50)
    
    tests = [
        ("OAuth Provider", test_oauth_provider),
        ("Token Manager", test_token_manager),
        ("Session Auth", test_session_auth),
        ("Auth Endpoints", test_auth_endpoints),
        ("Auth Integration", test_auth_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} test PASSED")
            else:
                print(f"✗ {test_name} test FAILED")
        except Exception as e:
            print(f"✗ {test_name} test ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Phase 3.3 authentication tests PASSED!")
        return 0
    else:
        print("❌ Some Phase 3.3 authentication tests FAILED!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
