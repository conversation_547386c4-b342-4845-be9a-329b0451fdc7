#!/usr/bin/env python3
"""
Simple test server for OAuth authentication system.
Tests Phase 3 OAuth implementation without conflicts.
"""

import sys
import os
import logging

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, jsonify
from flask_cors import CORS

# Import OAuth auth blueprint directly to avoid circular imports
import importlib.util
import sys

# Load the auth endpoints module directly
spec = importlib.util.spec_from_file_location(
    "auth_endpoints",
    "/u/Arun Dev/Python Projects/AI-Coding-Agent/bridge/api/auth_endpoints.py"
)
auth_endpoints = importlib.util.module_from_spec(spec)
sys.modules["auth_endpoints"] = auth_endpoints
spec.loader.exec_module(auth_endpoints)

oauth_auth_bp = auth_endpoints.oauth_auth_bp

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def create_test_app():
    """Create a test Flask app with only OAuth authentication."""
    app = Flask(__name__)
    CORS(app)
    
    # Register only the OAuth auth blueprint
    app.register_blueprint(oauth_auth_bp)
    
    @app.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint."""
        return jsonify({
            "status": "ok",
            "service": "OAuth Test Server",
            "version": "1.0.0"
        })
    
    @app.route('/', methods=['GET'])
    def root():
        """Root endpoint with OAuth system info."""
        return jsonify({
            "message": "AI Coding Agent OAuth Test Server",
            "endpoints": {
                "health": "/health",
                "oauth_health": "/api/auth/health",
                "oauth_providers": "/api/auth/providers",
                "oauth_login": "/api/auth/oauth/{provider}/login",
                "oauth_callback": "/api/auth/oauth/{provider}/callback",
                "token_refresh": "/api/auth/token/refresh",
                "user_info": "/api/auth/user"
            },
            "supported_providers": ["github", "google", "microsoft"],
            "note": "Configure OAuth providers via environment variables"
        })
    
    return app

def run_test_server():
    """Run the OAuth test server."""
    app = create_test_app()
    
    logger.info("🚀 Starting OAuth Test Server")
    logger.info("📊 OAuth Authentication System - Phase 3")
    logger.info("🔗 Available at: http://localhost:8080")
    logger.info("🔍 Health check: http://localhost:8080/api/auth/health")
    logger.info("📋 Providers: http://localhost:8080/api/auth/providers")
    
    try:
        app.run(
            host="localhost",
            port=8080,
            debug=False,
            threaded=True
        )
    except KeyboardInterrupt:
        logger.info("🛑 OAuth Test Server stopped")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")

if __name__ == "__main__":
    run_test_server()
