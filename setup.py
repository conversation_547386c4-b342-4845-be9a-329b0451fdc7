import os
import sys
import time
import subprocess
from pathlib import Path
from setuptools import setup, find_packages, Command
from setuptools.command.develop import develop
from setuptools.command.install import install

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = fh.read().splitlines()

# Define paths
PROJECT_ROOT = Path(os.path.dirname(os.path.abspath(__file__)))
PATCHES_DIR = PROJECT_ROOT / "patches"
SWE_AGENT_DIR = PROJECT_ROOT / "swe-agent"
SWE_REX_DIR = PROJECT_ROOT / "swe-rex"
VIM_EXTENSION_DIR = PROJECT_ROOT / "vim-extension"

# Create patches directory if it doesn't exist
os.makedirs(PATCHES_DIR / "swe-agent", exist_ok=True)
os.makedirs(PATCHES_DIR / "swe-rex", exist_ok=True)
os.makedirs(PATCHES_DIR / "vim-extension", exist_ok=True)


class CreatePatchCommand(Command):
    """Command to create patches from submodule changes."""
    description = "Create patches from submodule changes"
    user_options = [
        ("submodule=", "s", "Submodule to create patches for (swe-agent, swe-rex, vim-extension)"),
    ]

    def initialize_options(self):
        self.submodule = None

    def finalize_options(self):
        if self.submodule not in ["swe-agent", "swe-rex", "vim-extension", None]:
            raise ValueError("Submodule must be one of: swe-agent, swe-rex, vim-extension")

    def run(self):
        submodules = [self.submodule] if self.submodule else ["swe-agent", "swe-rex", "vim-extension"]
        
        for submodule in submodules:
            submodule_dir = PROJECT_ROOT / submodule
            patches_dir = PATCHES_DIR / submodule
            
            if not submodule_dir.exists():
                print(f"Submodule {submodule} not found at {submodule_dir}")
                continue
            
            print(f"Creating patches for {submodule}...")
            
            # Check if there are any changes
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                cwd=str(submodule_dir),
                capture_output=True,
                text=True
            )
            
            if not result.stdout.strip():
                print(f"No changes found in {submodule}")
                continue
            
            # Create a patch file
            patch_file = patches_dir / f"{submodule}-{int(time.time())}.patch"
            subprocess.run(
                ["git", "diff"],
                cwd=str(submodule_dir),
                stdout=open(patch_file, "w"),
                check=True
            )
            
            print(f"Created patch file: {patch_file}")


class ApplyPatchesCommand(Command):
    """Command to apply patches to submodules."""
    description = "Apply patches to submodules"
    user_options = [
        ("submodule=", "s", "Submodule to apply patches to (swe-agent, swe-rex, vim-extension)"),
    ]

    def initialize_options(self):
        self.submodule = None

    def finalize_options(self):
        if self.submodule not in ["swe-agent", "swe-rex", "vim-extension", None]:
            raise ValueError("Submodule must be one of: swe-agent, swe-rex, vim-extension")

    def run(self):
        submodules = [self.submodule] if self.submodule else ["swe-agent", "swe-rex", "vim-extension"]
        
        for submodule in submodules:
            submodule_dir = PROJECT_ROOT / submodule
            patches_dir = PATCHES_DIR / submodule
            
            if not submodule_dir.exists():
                print(f"Submodule {submodule} not found at {submodule_dir}")
                continue
            
            if not patches_dir.exists() or not any(patches_dir.iterdir()):
                print(f"No patches found for {submodule}")
                continue
            
            print(f"Applying patches to {submodule}...")
            
            for patch_file in sorted(patches_dir.glob("*.patch")):
                print(f"Applying patch: {patch_file}")
                try:
                    subprocess.run(
                        ["git", "apply", str(patch_file)],
                        cwd=str(submodule_dir),
                        check=True
                    )
                    print(f"Successfully applied patch: {patch_file}")
                except subprocess.CalledProcessError as e:
                    print(f"Failed to apply patch {patch_file}: {e}")


class CustomDevelopCommand(develop):
    """Custom develop command that applies patches after installation."""
    def run(self):
        develop.run(self)
        self.run_command("apply_patches")


class CustomInstallCommand(install):
    """Custom install command that applies patches after installation."""
    def run(self):
        install.run(self)
        self.run_command("apply_patches")


setup(
    name="ai-coding-agent",
    version="0.1.0",
    author="Arun Dev",
    description="AI Coding Agent - A bridge between SWE-Agent and IDE plugins",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/ArunkumarKv-iN/AI-Coding-Agent",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "ai-coding-agent=bridge.__main__:main",
        ],
    },
    cmdclass={
        "develop": CustomDevelopCommand,
        "install": CustomInstallCommand,
        "create_patches": CreatePatchCommand,
        "apply_patches": ApplyPatchesCommand,
    },
)
