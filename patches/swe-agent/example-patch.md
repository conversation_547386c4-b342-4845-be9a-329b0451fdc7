# Example Patch File

This is an example of how to create and apply patches for the SWE-agent submodule.

## Creating a Patch

When you need to make changes to the SWE-agent submodule:

1. Make your changes directly in the submodule directory
2. Run the create_patches command:
   ```bash
   python setup.py create_patches --submodule=swe-agent
   ```
3. This will create a timestamped patch file in this directory
4. Commit the patch file to your repository

## Patch File Format

Patch files are created using `git diff` and have the following format:

```diff
diff --git a/path/to/file b/path/to/file
index abcdef..123456 100644
--- a/path/to/file
+++ b/path/to/file
@@ -10,7 +10,7 @@
 unchanged line
 unchanged line
-removed line
+added line
 unchanged line
```

## Applying Patches

Patches are automatically applied during installation or development setup.
You can also apply them manually:

```bash
python setup.py apply_patches --submodule=swe-agent
```

This approach allows you to track and manage changes to the submodules without needing write access to their repositories.
