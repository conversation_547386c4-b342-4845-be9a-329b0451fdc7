name: build-docs

on:
  push:
    branches:
      - main
      - "build-docs-*"
  pull_request:
    branches:
      - main
permissions:
  contents: write
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Configure Git Credentials
        run: |
          git config user.name github-actions[bot]
          git config user.email 41898282+github-actions[bot]@users.noreply.github.com
      - uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - run: echo "cache_id=$(date --utc '+%V')" >> $GITHUB_ENV
      - uses: actions/cache@v4
        with:
          key: mkdocs-material-${{ env.cache_id }}
          path: .cache
          restore-keys: |
            mkdocs-material-
      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
      - run: uv pip install --python ${Python_ROOT_DIR} '.[dev]'
      - name: Build Documentation
        if: github.ref != 'refs/heads/main'
        run: mkdocs build
      - name: Build + Deploy Documentation
        if: github.ref == 'refs/heads/main'
        run: mike deploy --push 1.0 latest
