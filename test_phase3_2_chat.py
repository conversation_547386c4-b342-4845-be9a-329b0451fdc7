#!/usr/bin/env python3
"""
Test script for Phase 3.2 multi-turn chat functionality.
Tests the chat manager, conversation context, and Vim integration.
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path

# Add the bridge to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from bridge.integrations.chat_manager import chat_manager, MessageRole, ChatContext
from bridge.core.conversation_context import conversation_context
from bridge.integrations.vim_client import send_command

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_chat_manager():
    """Test the chat manager functionality."""
    print("\n=== Testing Chat Manager ===")
    
    try:
        # Create a chat session
        context = ChatContext(
            file_path="test.py",
            project_root="/tmp/test",
            language="python"
        )
        
        session_id = chat_manager.create_session("Test Chat Session", context)
        print(f"✓ Chat session created: {session_id}")
        
        # Add a user message
        message_id = chat_manager.add_message(
            session_id, 
            MessageRole.USER, 
            "Hello, can you help me with Python code?"
        )
        print(f"✓ User message added: {message_id}")
        
        # Add an assistant message
        assistant_message_id = chat_manager.add_message(
            session_id,
            MessageRole.ASSISTANT,
            "Of course! I'd be happy to help you with Python code. What specific question do you have?"
        )
        print(f"✓ Assistant message added: {assistant_message_id}")
        
        # Get session
        session = chat_manager.get_session(session_id)
        if session:
            print(f"✓ Session retrieved: {len(session.messages)} messages")
        
        # List sessions
        sessions = chat_manager.list_sessions()
        print(f"✓ Sessions listed: {len(sessions)} total sessions")
        
        # Get session summary
        summary = chat_manager.get_session_summary(session_id)
        if summary:
            print(f"✓ Session summary: {summary['message_count']} messages")
        
        return True
        
    except Exception as e:
        print(f"✗ Chat manager test failed: {e}")
        return False


async def test_chat_streaming():
    """Test the chat streaming functionality."""
    print("\n=== Testing Chat Streaming ===")
    
    try:
        # Create a chat session
        context = ChatContext(
            file_path="example.py",
            language="python"
        )
        
        session_id = chat_manager.create_session("Streaming Test", context)
        print(f"✓ Chat session created for streaming: {session_id}")
        
        # Send a message and collect streaming response
        message = "Can you explain how to create a Python function?"
        response_chunks = []
        
        async for chunk in chat_manager.send_message(session_id, message):
            response_chunks.append(chunk)
            print(f"  Chunk: {chunk[:50]}..." if len(chunk) > 50 else f"  Chunk: {chunk}")
        
        full_response = "".join(response_chunks)
        print(f"✓ Streaming response completed: {len(full_response)} characters")
        
        # Verify the message was added to the session
        session = chat_manager.get_session(session_id)
        if session and len(session.messages) >= 2:
            print("✓ Messages properly stored in session")
        
        return True
        
    except Exception as e:
        print(f"✗ Chat streaming test failed: {e}")
        return False


def test_conversation_context():
    """Test the conversation context manager."""
    print("\n=== Testing Conversation Context ===")
    
    try:
        session_id = "test_context_session"
        
        # Add various context items
        context_id1 = conversation_context.add_context_item(
            session_id,
            "file",
            "def hello_world():\n    print('Hello, World!')",
            {"file_path": "hello.py", "language": "python"}
        )
        print(f"✓ File context added: {context_id1}")
        
        context_id2 = conversation_context.add_context_item(
            session_id,
            "message",
            "How do I create a function in Python?",
            {"role": "user"}
        )
        print(f"✓ Message context added: {context_id2}")
        
        context_id3 = conversation_context.add_context_item(
            session_id,
            "code_block",
            "def my_function(param):\n    return param * 2",
            {"source": "example"}
        )
        print(f"✓ Code block context added: {context_id3}")
        
        # Test context retrieval
        relevant_context = conversation_context.get_relevant_context(
            session_id, 
            "function definition", 
            max_items=5
        )
        print(f"✓ Relevant context retrieved: {len(relevant_context)} items")
        
        # Test context summary
        summary = conversation_context.get_context_summary(session_id)
        print(f"✓ Context summary: {summary}")
        
        # Test context stats
        stats = conversation_context.get_context_stats(session_id)
        print(f"✓ Context stats: {stats['total_items']} items, avg relevance: {stats['average_relevance']:.2f}")
        
        # Test context update from message
        conversation_context.update_context_from_message(
            session_id,
            "Can you help me debug this code: `print('hello')`",
            {
                "file_path": "debug.py",
                "content": "print('hello')\nprint('world')",
                "language": "python"
            }
        )
        print("✓ Context updated from message")
        
        return True
        
    except Exception as e:
        print(f"✗ Conversation context test failed: {e}")
        return False


def test_vim_chat_integration():
    """Test the Vim chat integration."""
    print("\n=== Testing Vim Chat Integration ===")
    
    try:
        # Test ping first
        response = send_command("ping")
        if response.get("status") != "success":
            print("✗ Vim integration server not responding")
            return False
        print("✓ Vim integration server is running")
        
        # Test chat session creation
        response = send_command(
            "chat_create",
            title="Test Vim Chat",
            context={
                "file_path": "test.py",
                "language": "python"
            }
        )
        
        if response.get("status") == "success":
            session_id = response.get("session_id")
            print(f"✓ Chat session created via Vim: {session_id}")
        else:
            print(f"✗ Chat session creation failed: {response.get('message', 'Unknown error')}")
            return False
        
        # Test chat list
        response = send_command("chat_list")
        if response.get("status") == "success":
            sessions = response.get("sessions", [])
            print(f"✓ Chat sessions listed: {len(sessions)} sessions")
        else:
            print(f"✗ Chat list failed: {response.get('message', 'Unknown error')}")
            return False
        
        # Test sending a chat message
        response = send_command(
            "chat",
            session_id=session_id,
            message="Hello, can you help me write a Python function?",
            context={
                "file_path": "test.py",
                "content": "# Python file\n",
                "language": "python"
            }
        )
        
        if response.get("status") == "success":
            chat_response = response.get("response", "")
            print(f"✓ Chat message sent and response received: {len(chat_response)} characters")
        else:
            print(f"✗ Chat message failed: {response.get('message', 'Unknown error')}")
            return False
        
        # Test chat history
        response = send_command("chat_history", session_id=session_id)
        if response.get("status") == "success":
            messages = response.get("messages", [])
            print(f"✓ Chat history retrieved: {len(messages)} messages")
        else:
            print(f"✗ Chat history failed: {response.get('message', 'Unknown error')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Vim chat integration test failed: {e}")
        return False


def test_chat_performance():
    """Test chat system performance."""
    print("\n=== Testing Chat Performance ===")
    
    try:
        # Create multiple sessions
        session_ids = []
        start_time = time.time()
        
        for i in range(5):
            context = ChatContext(
                file_path=f"test_{i}.py",
                language="python"
            )
            session_id = chat_manager.create_session(f"Performance Test {i}", context)
            session_ids.append(session_id)
        
        creation_time = time.time() - start_time
        print(f"✓ Created 5 sessions in {creation_time:.2f}s")
        
        # Add messages to each session
        start_time = time.time()
        
        for session_id in session_ids:
            for j in range(3):
                chat_manager.add_message(
                    session_id,
                    MessageRole.USER,
                    f"Test message {j} for performance testing"
                )
        
        message_time = time.time() - start_time
        print(f"✓ Added 15 messages in {message_time:.2f}s")
        
        # Test context operations
        start_time = time.time()
        
        for session_id in session_ids:
            conversation_context.add_context_item(
                session_id,
                "performance_test",
                "This is a performance test context item",
                {"test": True}
            )
        
        context_time = time.time() - start_time
        print(f"✓ Added 5 context items in {context_time:.2f}s")
        
        # Test retrieval performance
        start_time = time.time()
        
        for session_id in session_ids:
            relevant = conversation_context.get_relevant_context(session_id, "test")
            summary = conversation_context.get_context_summary(session_id)
        
        retrieval_time = time.time() - start_time
        print(f"✓ Retrieved context for 5 sessions in {retrieval_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"✗ Chat performance test failed: {e}")
        return False


def main():
    """Run all Phase 3.2 chat tests."""
    print("Starting Phase 3.2 Multi-turn Chat Tests")
    print("=" * 50)
    
    tests = [
        ("Chat Manager", test_chat_manager),
        ("Chat Streaming", lambda: asyncio.run(test_chat_streaming())),
        ("Conversation Context", test_conversation_context),
        ("Vim Chat Integration", test_vim_chat_integration),
        ("Chat Performance", test_chat_performance)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} test PASSED")
            else:
                print(f"✗ {test_name} test FAILED")
        except Exception as e:
            print(f"✗ {test_name} test ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:25} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Phase 3.2 chat tests PASSED!")
        return 0
    else:
        print("❌ Some Phase 3.2 chat tests FAILED!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
