"""
Refactoring assistant for automated code improvements and suggestions.
Provides intelligent refactoring suggestions and code transformation capabilities.
"""

import ast
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict

from bridge.core.context_manager import context_analyzer, Symbol, ContextType

logger = logging.getLogger(__name__)


@dataclass
class RefactoringRule:
    """Refactoring rule definition."""
    name: str
    description: str
    category: str  # naming, structure, performance, readability
    severity: str  # low, medium, high
    language: str
    pattern: str
    suggestion: str
    auto_fixable: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class RefactoringSuggestion:
    """Refactoring suggestion for code improvement."""
    rule: RefactoringRule
    file_path: str
    line_start: int
    line_end: int
    column_start: int
    column_end: int
    original_code: str
    suggested_code: Optional[str] = None
    explanation: Optional[str] = None
    confidence: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['rule'] = self.rule.to_dict()
        return data


@dataclass
class RefactoringReport:
    """Complete refactoring analysis report."""
    file_path: str
    suggestions: List[RefactoringSuggestion]
    metrics: Dict[str, Any]
    summary: Dict[str, int]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['suggestions'] = [s.to_dict() for s in self.suggestions]
        return data


class RefactoringAssistant:
    """Intelligent refactoring assistant for code improvements."""
    
    def __init__(self):
        self.rules = self._initialize_rules()
    
    def _initialize_rules(self) -> List[RefactoringRule]:
        """Initialize refactoring rules."""
        rules = []
        
        # Python refactoring rules
        rules.extend([
            RefactoringRule(
                name="long_function",
                description="Function is too long and should be broken down",
                category="structure",
                severity="medium",
                language="python",
                pattern="function_length > 50",
                suggestion="Break down into smaller, focused functions",
                auto_fixable=False
            ),
            RefactoringRule(
                name="too_many_parameters",
                description="Function has too many parameters",
                category="structure",
                severity="medium",
                language="python",
                pattern="parameter_count > 5",
                suggestion="Consider using a configuration object or dataclass",
                auto_fixable=False
            ),
            RefactoringRule(
                name="missing_docstring",
                description="Function or class missing docstring",
                category="readability",
                severity="low",
                language="python",
                pattern="no_docstring",
                suggestion="Add descriptive docstring",
                auto_fixable=True
            ),
            RefactoringRule(
                name="unused_import",
                description="Import statement is not used",
                category="cleanup",
                severity="low",
                language="python",
                pattern="unused_import",
                suggestion="Remove unused import",
                auto_fixable=True
            ),
            RefactoringRule(
                name="magic_number",
                description="Magic number should be a named constant",
                category="readability",
                severity="medium",
                language="python",
                pattern="magic_number",
                suggestion="Replace with named constant",
                auto_fixable=False
            ),
            RefactoringRule(
                name="nested_loops",
                description="Deeply nested loops reduce readability",
                category="structure",
                severity="medium",
                language="python",
                pattern="nested_depth > 3",
                suggestion="Extract nested logic into separate functions",
                auto_fixable=False
            ),
            RefactoringRule(
                name="long_line",
                description="Line is too long",
                category="readability",
                severity="low",
                language="python",
                pattern="line_length > 88",
                suggestion="Break line into multiple lines",
                auto_fixable=True
            ),
            RefactoringRule(
                name="duplicate_code",
                description="Duplicate code detected",
                category="structure",
                severity="high",
                language="python",
                pattern="duplicate_block",
                suggestion="Extract common code into a function",
                auto_fixable=False
            )
        ])
        
        # JavaScript/TypeScript rules
        rules.extend([
            RefactoringRule(
                name="var_declaration",
                description="Use const or let instead of var",
                category="modernization",
                severity="medium",
                language="javascript",
                pattern="var_usage",
                suggestion="Replace var with const or let",
                auto_fixable=True
            ),
            RefactoringRule(
                name="arrow_function",
                description="Consider using arrow function",
                category="modernization",
                severity="low",
                language="javascript",
                pattern="function_expression",
                suggestion="Use arrow function for shorter syntax",
                auto_fixable=True
            ),
            RefactoringRule(
                name="console_log",
                description="Remove console.log statements",
                category="cleanup",
                severity="low",
                language="javascript",
                pattern="console_log",
                suggestion="Remove debug console.log",
                auto_fixable=True
            )
        ])
        
        return rules
    
    def analyze_file(self, file_path: str, content: str, language: str = None) -> RefactoringReport:
        """
        Analyze a file for refactoring opportunities.
        
        Args:
            file_path: Path to the file.
            content: File content.
            language: Programming language.
            
        Returns:
            Refactoring report with suggestions.
        """
        if language is None:
            language = self._detect_language(file_path)
        
        suggestions = []
        
        try:
            if language == "python":
                suggestions = self._analyze_python_file(file_path, content)
            elif language in ["javascript", "typescript"]:
                suggestions = self._analyze_js_file(file_path, content)
            else:
                suggestions = self._analyze_generic_file(file_path, content, language)
            
            # Calculate metrics
            metrics = self._calculate_metrics(content, language)
            
            # Create summary
            summary = self._create_summary(suggestions)
            
            return RefactoringReport(
                file_path=file_path,
                suggestions=suggestions,
                metrics=metrics,
                summary=summary
            )
            
        except Exception as e:
            logger.error(f"Refactoring analysis failed for {file_path}: {e}")
            return RefactoringReport(
                file_path=file_path,
                suggestions=[],
                metrics={},
                summary={}
            )
    
    def _analyze_python_file(self, file_path: str, content: str) -> List[RefactoringSuggestion]:
        """Analyze Python file for refactoring opportunities."""
        suggestions = []
        lines = content.split('\n')
        
        try:
            # Parse AST for detailed analysis
            tree = ast.parse(content)
            
            # Analyze functions
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    suggestions.extend(self._analyze_python_function(node, lines, file_path))
                elif isinstance(node, ast.ClassDef):
                    suggestions.extend(self._analyze_python_class(node, lines, file_path))
                elif isinstance(node, ast.Import) or isinstance(node, ast.ImportFrom):
                    suggestions.extend(self._analyze_python_import(node, content, file_path))
            
            # Analyze line-level issues
            suggestions.extend(self._analyze_python_lines(lines, file_path))
            
        except SyntaxError as e:
            logger.warning(f"Syntax error in Python file {file_path}: {e}")
        
        return suggestions
    
    def _analyze_python_function(self, node: ast.FunctionDef, lines: List[str], file_path: str) -> List[RefactoringSuggestion]:
        """Analyze Python function for refactoring opportunities."""
        suggestions = []
        
        # Check function length
        function_length = node.end_lineno - node.lineno + 1 if node.end_lineno else 1
        if function_length > 50:
            rule = next(r for r in self.rules if r.name == "long_function")
            suggestions.append(RefactoringSuggestion(
                rule=rule,
                file_path=file_path,
                line_start=node.lineno,
                line_end=node.end_lineno or node.lineno,
                column_start=node.col_offset,
                column_end=node.end_col_offset or 0,
                original_code=f"def {node.name}(...)",
                explanation=f"Function has {function_length} lines, consider breaking it down",
                confidence=0.8
            ))
        
        # Check parameter count
        param_count = len(node.args.args)
        if param_count > 5:
            rule = next(r for r in self.rules if r.name == "too_many_parameters")
            suggestions.append(RefactoringSuggestion(
                rule=rule,
                file_path=file_path,
                line_start=node.lineno,
                line_end=node.lineno,
                column_start=node.col_offset,
                column_end=node.end_col_offset or 0,
                original_code=f"def {node.name}(...)",
                explanation=f"Function has {param_count} parameters",
                confidence=0.9
            ))
        
        # Check for missing docstring
        if not ast.get_docstring(node):
            rule = next(r for r in self.rules if r.name == "missing_docstring")
            suggestions.append(RefactoringSuggestion(
                rule=rule,
                file_path=file_path,
                line_start=node.lineno,
                line_end=node.lineno,
                column_start=node.col_offset,
                column_end=node.end_col_offset or 0,
                original_code=f"def {node.name}(...):",
                suggested_code=f'def {node.name}(...):\n    """Add function description here."""',
                explanation="Function is missing docstring",
                confidence=1.0
            ))
        
        return suggestions
    
    def _analyze_python_class(self, node: ast.ClassDef, lines: List[str], file_path: str) -> List[RefactoringSuggestion]:
        """Analyze Python class for refactoring opportunities."""
        suggestions = []
        
        # Check for missing docstring
        if not ast.get_docstring(node):
            rule = next(r for r in self.rules if r.name == "missing_docstring")
            suggestions.append(RefactoringSuggestion(
                rule=rule,
                file_path=file_path,
                line_start=node.lineno,
                line_end=node.lineno,
                column_start=node.col_offset,
                column_end=node.end_col_offset or 0,
                original_code=f"class {node.name}:",
                suggested_code=f'class {node.name}:\n    """Add class description here."""',
                explanation="Class is missing docstring",
                confidence=1.0
            ))
        
        return suggestions
    
    def _analyze_python_import(self, node: ast.AST, content: str, file_path: str) -> List[RefactoringSuggestion]:
        """Analyze Python import for unused imports."""
        suggestions = []
        
        # Simple unused import detection (could be enhanced)
        if isinstance(node, ast.Import):
            for alias in node.names:
                import_name = alias.asname or alias.name
                if content.count(import_name) == 1:  # Only appears in import statement
                    rule = next(r for r in self.rules if r.name == "unused_import")
                    suggestions.append(RefactoringSuggestion(
                        rule=rule,
                        file_path=file_path,
                        line_start=node.lineno,
                        line_end=node.lineno,
                        column_start=node.col_offset,
                        column_end=node.end_col_offset or 0,
                        original_code=f"import {alias.name}",
                        suggested_code="",
                        explanation=f"Import '{import_name}' appears to be unused",
                        confidence=0.7
                    ))
        
        return suggestions
    
    def _analyze_python_lines(self, lines: List[str], file_path: str) -> List[RefactoringSuggestion]:
        """Analyze Python lines for various issues."""
        suggestions = []
        
        for line_num, line in enumerate(lines, 1):
            # Check line length
            if len(line) > 88:
                rule = next(r for r in self.rules if r.name == "long_line")
                suggestions.append(RefactoringSuggestion(
                    rule=rule,
                    file_path=file_path,
                    line_start=line_num,
                    line_end=line_num,
                    column_start=0,
                    column_end=len(line),
                    original_code=line.strip(),
                    explanation=f"Line is {len(line)} characters long",
                    confidence=1.0
                ))
            
            # Check for magic numbers
            magic_number_pattern = r'\b\d{2,}\b'
            if re.search(magic_number_pattern, line) and 'def ' not in line and 'class ' not in line:
                rule = next(r for r in self.rules if r.name == "magic_number")
                suggestions.append(RefactoringSuggestion(
                    rule=rule,
                    file_path=file_path,
                    line_start=line_num,
                    line_end=line_num,
                    column_start=0,
                    column_end=len(line),
                    original_code=line.strip(),
                    explanation="Consider using named constants for magic numbers",
                    confidence=0.6
                ))
        
        return suggestions
    
    def _analyze_js_file(self, file_path: str, content: str) -> List[RefactoringSuggestion]:
        """Analyze JavaScript/TypeScript file for refactoring opportunities."""
        suggestions = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # Check for var usage
            if re.search(r'\bvar\s+', line_stripped):
                rule = next(r for r in self.rules if r.name == "var_declaration")
                suggestions.append(RefactoringSuggestion(
                    rule=rule,
                    file_path=file_path,
                    line_start=line_num,
                    line_end=line_num,
                    column_start=0,
                    column_end=len(line),
                    original_code=line_stripped,
                    suggested_code=line_stripped.replace('var ', 'const '),
                    explanation="Replace var with const or let",
                    confidence=0.9
                ))
            
            # Check for console.log
            if 'console.log' in line_stripped:
                rule = next(r for r in self.rules if r.name == "console_log")
                suggestions.append(RefactoringSuggestion(
                    rule=rule,
                    file_path=file_path,
                    line_start=line_num,
                    line_end=line_num,
                    column_start=0,
                    column_end=len(line),
                    original_code=line_stripped,
                    suggested_code="",
                    explanation="Remove debug console.log statement",
                    confidence=0.8
                ))
        
        return suggestions
    
    def _analyze_generic_file(self, file_path: str, content: str, language: str) -> List[RefactoringSuggestion]:
        """Analyze generic file for basic refactoring opportunities."""
        suggestions = []
        lines = content.split('\n')
        
        # Basic line length check
        for line_num, line in enumerate(lines, 1):
            if len(line) > 120:  # Generic long line threshold
                rule = RefactoringRule(
                    name="long_line_generic",
                    description="Line is too long",
                    category="readability",
                    severity="low",
                    language=language,
                    pattern="line_length > 120",
                    suggestion="Break line into multiple lines"
                )
                suggestions.append(RefactoringSuggestion(
                    rule=rule,
                    file_path=file_path,
                    line_start=line_num,
                    line_end=line_num,
                    column_start=0,
                    column_end=len(line),
                    original_code=line.strip(),
                    explanation=f"Line is {len(line)} characters long",
                    confidence=1.0
                ))
        
        return suggestions
    
    def _calculate_metrics(self, content: str, language: str) -> Dict[str, Any]:
        """Calculate code metrics."""
        lines = content.split('\n')
        
        return {
            "total_lines": len(lines),
            "non_empty_lines": len([line for line in lines if line.strip()]),
            "comment_lines": len([line for line in lines if line.strip().startswith('#')]),
            "average_line_length": sum(len(line) for line in lines) / len(lines) if lines else 0,
            "max_line_length": max(len(line) for line in lines) if lines else 0,
            "language": language
        }
    
    def _create_summary(self, suggestions: List[RefactoringSuggestion]) -> Dict[str, int]:
        """Create summary of refactoring suggestions."""
        summary = defaultdict(int)
        
        for suggestion in suggestions:
            summary[suggestion.rule.category] += 1
            summary[suggestion.rule.severity] += 1
        
        summary["total"] = len(suggestions)
        return dict(summary)
    
    def _detect_language(self, file_path: str) -> str:
        """Detect programming language from file extension."""
        extension = file_path.split('.')[-1].lower()
        
        language_map = {
            'py': 'python',
            'js': 'javascript',
            'ts': 'typescript',
            'jsx': 'javascript',
            'tsx': 'typescript',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'go': 'go',
            'rs': 'rust'
        }
        
        return language_map.get(extension, 'text')
    
    def get_refactoring_rules(self, language: Optional[str] = None) -> List[RefactoringRule]:
        """Get available refactoring rules."""
        if language:
            return [rule for rule in self.rules if rule.language == language]
        return self.rules
    
    def apply_auto_fixes(self, suggestions: List[RefactoringSuggestion]) -> Dict[str, str]:
        """
        Apply automatic fixes for auto-fixable suggestions.
        
        Args:
            suggestions: List of refactoring suggestions.
            
        Returns:
            Dictionary mapping file paths to fixed content.
        """
        fixes = {}
        
        # Group suggestions by file
        by_file = defaultdict(list)
        for suggestion in suggestions:
            if suggestion.rule.auto_fixable and suggestion.suggested_code is not None:
                by_file[suggestion.file_path].append(suggestion)
        
        # Apply fixes file by file
        for file_path, file_suggestions in by_file.items():
            try:
                # Read original content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Apply fixes (simplified - would need more sophisticated logic)
                fixed_content = content
                for suggestion in sorted(file_suggestions, key=lambda s: s.line_start, reverse=True):
                    # Simple line replacement (could be enhanced)
                    lines = fixed_content.split('\n')
                    if suggestion.line_start <= len(lines):
                        lines[suggestion.line_start - 1] = suggestion.suggested_code
                        fixed_content = '\n'.join(lines)
                
                fixes[file_path] = fixed_content
                
            except Exception as e:
                logger.error(f"Could not apply fixes to {file_path}: {e}")
        
        return fixes


# Global refactoring assistant instance
refactoring_assistant = RefactoringAssistant()
