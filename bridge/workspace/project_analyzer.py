"""
Project analyzer for comprehensive workspace analysis and indexing.
Provides project structure analysis, dependency tracking, and intelligent insights.
"""

import ast
import json
import logging
import os
import re
import subprocess
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
import threading

from bridge.core.context_manager import context_analyzer, FileContext

logger = logging.getLogger(__name__)


@dataclass
class ProjectMetrics:
    """Project metrics and statistics."""
    total_files: int
    total_lines: int
    total_functions: int
    total_classes: int
    languages: Dict[str, int]
    file_types: Dict[str, int]
    complexity_score: float
    test_coverage: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class DependencyInfo:
    """Dependency information."""
    name: str
    version: Optional[str]
    type: str  # direct, dev, peer, etc.
    source: str  # package.json, requirements.txt, etc.
    is_local: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class ProjectStructure:
    """Project directory structure."""
    name: str
    type: str  # file, directory
    path: str
    size: Optional[int] = None
    children: Optional[List['ProjectStructure']] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        if self.children:
            data['children'] = [child.to_dict() for child in self.children]
        return data


@dataclass
class ProjectAnalysis:
    """Complete project analysis result."""
    project_path: str
    project_name: str
    main_language: str
    structure: ProjectStructure
    metrics: ProjectMetrics
    dependencies: List[DependencyInfo]
    files: Dict[str, FileContext]
    issues: List[Dict[str, Any]]
    recommendations: List[str]
    analyzed_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['structure'] = self.structure.to_dict()
        data['metrics'] = self.metrics.to_dict()
        data['dependencies'] = [dep.to_dict() for dep in self.dependencies]
        data['files'] = {path: asdict(ctx) for path, ctx in self.files.items()}
        data['analyzed_at'] = self.analyzed_at.isoformat()
        return data


class ProjectAnalyzer:
    """Comprehensive project analyzer for workspace insights."""
    
    def __init__(self):
        self.analyses: Dict[str, ProjectAnalysis] = {}
        self.lock = threading.RLock()
        
        # File patterns to ignore
        self.ignore_patterns = {
            '.git', '.svn', '.hg', '__pycache__', '.pytest_cache',
            'node_modules', '.venv', 'venv', 'env', '.env',
            '.DS_Store', 'Thumbs.db', '*.pyc', '*.pyo', '*.pyd',
            '*.so', '*.dll', '*.dylib', '*.egg-info', 'dist', 'build'
        }
        
        # Language file extensions
        self.language_extensions = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.hpp': 'cpp',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.sh': 'bash',
            '.sql': 'sql',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml'
        }
    
    def analyze_project(self, project_path: str, force_refresh: bool = False) -> ProjectAnalysis:
        """
        Analyze a complete project.
        
        Args:
            project_path: Path to the project root.
            force_refresh: Force re-analysis even if cached.
            
        Returns:
            Complete project analysis.
        """
        project_path = os.path.abspath(project_path)
        
        with self.lock:
            # Check if we have a recent analysis
            if not force_refresh and project_path in self.analyses:
                analysis = self.analyses[project_path]
                # Return cached analysis if less than 1 hour old
                if (datetime.now() - analysis.analyzed_at).total_seconds() < 3600:
                    logger.info(f"Returning cached analysis for {project_path}")
                    return analysis
        
        logger.info(f"Starting project analysis for {project_path}")
        start_time = time.time()
        
        try:
            # Analyze project structure
            structure = self._analyze_structure(project_path)
            
            # Analyze files and extract metrics
            files, metrics = self._analyze_files(project_path)
            
            # Analyze dependencies
            dependencies = self._analyze_dependencies(project_path)
            
            # Detect main language
            main_language = self._detect_main_language(metrics.languages)
            
            # Generate issues and recommendations
            issues = self._analyze_issues(project_path, files)
            recommendations = self._generate_recommendations(metrics, dependencies, issues)
            
            # Create analysis result
            analysis = ProjectAnalysis(
                project_path=project_path,
                project_name=os.path.basename(project_path),
                main_language=main_language,
                structure=structure,
                metrics=metrics,
                dependencies=dependencies,
                files=files,
                issues=issues,
                recommendations=recommendations,
                analyzed_at=datetime.now()
            )
            
            # Cache the analysis
            with self.lock:
                self.analyses[project_path] = analysis
            
            analysis_time = time.time() - start_time
            logger.info(f"Project analysis completed in {analysis_time:.2f}s: {len(files)} files, {metrics.total_lines} lines")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Project analysis failed for {project_path}: {e}")
            raise
    
    def get_project_analysis(self, project_path: str) -> Optional[ProjectAnalysis]:
        """Get cached project analysis."""
        project_path = os.path.abspath(project_path)
        return self.analyses.get(project_path)
    
    def _analyze_structure(self, project_path: str) -> ProjectStructure:
        """Analyze project directory structure."""
        def build_structure(path: str, max_depth: int = 3, current_depth: int = 0) -> ProjectStructure:
            path_obj = Path(path)
            
            if path_obj.is_file():
                return ProjectStructure(
                    name=path_obj.name,
                    type="file",
                    path=str(path_obj),
                    size=path_obj.stat().st_size
                )
            
            children = []
            if current_depth < max_depth:
                try:
                    for item in sorted(path_obj.iterdir()):
                        if self._should_ignore(item.name):
                            continue
                        
                        child_structure = build_structure(str(item), max_depth, current_depth + 1)
                        children.append(child_structure)
                except PermissionError:
                    pass
            
            return ProjectStructure(
                name=path_obj.name,
                type="directory",
                path=str(path_obj),
                children=children if children else None
            )
        
        return build_structure(project_path)
    
    def _analyze_files(self, project_path: str) -> Tuple[Dict[str, FileContext], ProjectMetrics]:
        """Analyze all files in the project."""
        files = {}
        total_lines = 0
        total_functions = 0
        total_classes = 0
        languages = defaultdict(int)
        file_types = defaultdict(int)
        
        for root, dirs, filenames in os.walk(project_path):
            # Filter out ignored directories
            dirs[:] = [d for d in dirs if not self._should_ignore(d)]
            
            for filename in filenames:
                if self._should_ignore(filename):
                    continue
                
                file_path = os.path.join(root, filename)
                relative_path = os.path.relpath(file_path, project_path)
                
                # Get file extension and language
                ext = Path(filename).suffix.lower()
                language = self.language_extensions.get(ext, 'text')
                
                # Count file types
                file_types[ext or 'no_extension'] += 1
                
                # Analyze source files
                if language in ['python', 'javascript', 'typescript', 'java', 'cpp', 'c']:
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        
                        # Analyze file with context manager
                        file_context = context_analyzer.analyze_file(relative_path, content, language)
                        files[relative_path] = file_context
                        
                        # Count metrics
                        lines = len(content.splitlines())
                        total_lines += lines
                        languages[language] += lines
                        
                        # Count symbols
                        for symbol in file_context.symbols:
                            if symbol.type.value == 'function':
                                total_functions += 1
                            elif symbol.type.value == 'class':
                                total_classes += 1
                        
                    except Exception as e:
                        logger.warning(f"Could not analyze file {file_path}: {e}")
                else:
                    # Count lines for non-source files
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = len(f.readlines())
                        total_lines += lines
                        languages[language] += lines
                    except Exception:
                        pass
        
        # Calculate complexity score (simplified)
        complexity_score = self._calculate_complexity_score(total_lines, total_functions, total_classes)
        
        metrics = ProjectMetrics(
            total_files=len(files),
            total_lines=total_lines,
            total_functions=total_functions,
            total_classes=total_classes,
            languages=dict(languages),
            file_types=dict(file_types),
            complexity_score=complexity_score
        )
        
        return files, metrics
    
    def _analyze_dependencies(self, project_path: str) -> List[DependencyInfo]:
        """Analyze project dependencies."""
        dependencies = []
        
        # Python dependencies
        dependencies.extend(self._analyze_python_dependencies(project_path))
        
        # JavaScript/Node.js dependencies
        dependencies.extend(self._analyze_js_dependencies(project_path))
        
        # Other language dependencies could be added here
        
        return dependencies
    
    def _analyze_python_dependencies(self, project_path: str) -> List[DependencyInfo]:
        """Analyze Python dependencies."""
        dependencies = []
        
        # requirements.txt
        req_file = os.path.join(project_path, 'requirements.txt')
        if os.path.exists(req_file):
            try:
                with open(req_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            # Parse requirement line
                            match = re.match(r'^([a-zA-Z0-9_-]+)([>=<!=]+.*)?', line)
                            if match:
                                name = match.group(1)
                                version = match.group(2).strip('>=<!=') if match.group(2) else None
                                dependencies.append(DependencyInfo(
                                    name=name,
                                    version=version,
                                    type='direct',
                                    source='requirements.txt'
                                ))
            except Exception as e:
                logger.warning(f"Could not parse requirements.txt: {e}")
        
        # setup.py / pyproject.toml could be added here
        
        return dependencies
    
    def _analyze_js_dependencies(self, project_path: str) -> List[DependencyInfo]:
        """Analyze JavaScript/Node.js dependencies."""
        dependencies = []
        
        # package.json
        package_file = os.path.join(project_path, 'package.json')
        if os.path.exists(package_file):
            try:
                with open(package_file, 'r') as f:
                    package_data = json.load(f)
                
                # Regular dependencies
                for name, version in package_data.get('dependencies', {}).items():
                    dependencies.append(DependencyInfo(
                        name=name,
                        version=version,
                        type='direct',
                        source='package.json'
                    ))
                
                # Dev dependencies
                for name, version in package_data.get('devDependencies', {}).items():
                    dependencies.append(DependencyInfo(
                        name=name,
                        version=version,
                        type='dev',
                        source='package.json'
                    ))
                
            except Exception as e:
                logger.warning(f"Could not parse package.json: {e}")
        
        return dependencies
    
    def _analyze_issues(self, project_path: str, files: Dict[str, FileContext]) -> List[Dict[str, Any]]:
        """Analyze potential issues in the project."""
        issues = []
        
        # Check for missing documentation
        readme_exists = any(
            os.path.exists(os.path.join(project_path, name))
            for name in ['README.md', 'README.txt', 'README.rst', 'README']
        )
        
        if not readme_exists:
            issues.append({
                "type": "documentation",
                "severity": "medium",
                "message": "No README file found",
                "suggestion": "Add a README.md file to document your project"
            })
        
        # Check for large files
        for file_path, file_context in files.items():
            if len(file_context.symbols) > 50:
                issues.append({
                    "type": "complexity",
                    "severity": "medium",
                    "file": file_path,
                    "message": f"File has {len(file_context.symbols)} symbols, consider splitting",
                    "suggestion": "Break down large files into smaller, more focused modules"
                })
        
        # Check for missing tests
        test_files = [f for f in files.keys() if 'test' in f.lower() or f.startswith('test_')]
        if not test_files:
            issues.append({
                "type": "testing",
                "severity": "high",
                "message": "No test files found",
                "suggestion": "Add unit tests to improve code reliability"
            })
        
        return issues
    
    def _generate_recommendations(self, metrics: ProjectMetrics, dependencies: List[DependencyInfo], 
                                issues: List[Dict[str, Any]]) -> List[str]:
        """Generate project recommendations."""
        recommendations = []
        
        # Code organization recommendations
        if metrics.complexity_score > 0.7:
            recommendations.append("Consider refactoring complex code into smaller, more manageable functions")
        
        # Documentation recommendations
        if any(issue['type'] == 'documentation' for issue in issues):
            recommendations.append("Add comprehensive documentation including README, API docs, and code comments")
        
        # Testing recommendations
        if any(issue['type'] == 'testing' for issue in issues):
            recommendations.append("Implement unit tests to improve code reliability and maintainability")
        
        # Dependency recommendations
        if len(dependencies) > 20:
            recommendations.append("Review dependencies and remove unused packages to reduce project complexity")
        
        # Language-specific recommendations
        if metrics.languages.get('python', 0) > 0:
            recommendations.append("Consider using type hints and docstrings for better Python code documentation")
        
        if metrics.languages.get('javascript', 0) > 0:
            recommendations.append("Consider migrating to TypeScript for better type safety")
        
        return recommendations
    
    def _should_ignore(self, name: str) -> bool:
        """Check if a file or directory should be ignored."""
        return any(
            name == pattern or name.startswith(pattern.rstrip('*'))
            for pattern in self.ignore_patterns
        )
    
    def _detect_main_language(self, languages: Dict[str, int]) -> str:
        """Detect the main programming language."""
        if not languages:
            return 'unknown'
        
        # Filter out non-programming languages
        programming_languages = {
            lang: count for lang, count in languages.items()
            if lang in ['python', 'javascript', 'typescript', 'java', 'cpp', 'c', 'go', 'rust']
        }
        
        if programming_languages:
            return max(programming_languages.items(), key=lambda x: x[1])[0]
        else:
            return max(languages.items(), key=lambda x: x[1])[0]
    
    def _calculate_complexity_score(self, total_lines: int, total_functions: int, total_classes: int) -> float:
        """Calculate a simplified complexity score (0-1)."""
        if total_lines == 0:
            return 0.0
        
        # Simple heuristic: more functions and classes per line = higher complexity
        function_density = total_functions / total_lines if total_lines > 0 else 0
        class_density = total_classes / total_lines if total_lines > 0 else 0
        
        # Normalize to 0-1 scale
        complexity = min(1.0, (function_density + class_density) * 1000)
        return round(complexity, 3)
    
    def get_project_stats(self) -> Dict[str, Any]:
        """Get statistics about analyzed projects."""
        with self.lock:
            return {
                "total_projects": len(self.analyses),
                "projects": [
                    {
                        "path": analysis.project_path,
                        "name": analysis.project_name,
                        "language": analysis.main_language,
                        "files": analysis.metrics.total_files,
                        "lines": analysis.metrics.total_lines,
                        "analyzed_at": analysis.analyzed_at.isoformat()
                    }
                    for analysis in self.analyses.values()
                ]
            }


# Global project analyzer instance
project_analyzer = ProjectAnalyzer()
