"""
Code quality analyzer for comprehensive code assessment and metrics.
Provides code quality scoring, complexity analysis, and improvement suggestions.
"""

import ast
import logging
import math
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass
class QualityMetric:
    """Individual quality metric."""
    name: str
    value: float
    max_value: float
    description: str
    category: str  # complexity, maintainability, readability, testability
    
    @property
    def score(self) -> float:
        """Normalized score (0-1)."""
        return min(1.0, self.value / self.max_value) if self.max_value > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['score'] = self.score
        return data


@dataclass
class QualityIssue:
    """Code quality issue."""
    type: str
    severity: str  # low, medium, high, critical
    message: str
    file_path: str
    line_number: int
    column: Optional[int] = None
    suggestion: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class QualityReport:
    """Comprehensive code quality report."""
    file_path: str
    overall_score: float
    metrics: List[QualityMetric]
    issues: List[QualityIssue]
    complexity_analysis: Dict[str, Any]
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['metrics'] = [m.to_dict() for m in self.metrics]
        data['issues'] = [i.to_dict() for i in self.issues]
        return data


class CodeQualityAnalyzer:
    """Comprehensive code quality analyzer."""
    
    def __init__(self):
        self.quality_thresholds = {
            'cyclomatic_complexity': 10,
            'function_length': 50,
            'class_length': 500,
            'parameter_count': 5,
            'nesting_depth': 4,
            'line_length': 88,
            'cognitive_complexity': 15
        }
    
    def analyze_file(self, file_path: str, content: str, language: str = None) -> QualityReport:
        """
        Analyze file for code quality.
        
        Args:
            file_path: Path to the file.
            content: File content.
            language: Programming language.
            
        Returns:
            Quality report with metrics and issues.
        """
        if language is None:
            language = self._detect_language(file_path)
        
        try:
            if language == "python":
                return self._analyze_python_quality(file_path, content)
            elif language in ["javascript", "typescript"]:
                return self._analyze_js_quality(file_path, content)
            else:
                return self._analyze_generic_quality(file_path, content, language)
                
        except Exception as e:
            logger.error(f"Quality analysis failed for {file_path}: {e}")
            return QualityReport(
                file_path=file_path,
                overall_score=0.0,
                metrics=[],
                issues=[],
                complexity_analysis={},
                recommendations=[]
            )
    
    def _analyze_python_quality(self, file_path: str, content: str) -> QualityReport:
        """Analyze Python file quality."""
        metrics = []
        issues = []
        
        try:
            tree = ast.parse(content)
            lines = content.split('\n')
            
            # Calculate metrics
            complexity_metrics = self._calculate_python_complexity(tree, lines)
            maintainability_metrics = self._calculate_python_maintainability(tree, content)
            readability_metrics = self._calculate_python_readability(lines, content)
            
            metrics.extend(complexity_metrics)
            metrics.extend(maintainability_metrics)
            metrics.extend(readability_metrics)
            
            # Find issues
            issues.extend(self._find_python_issues(tree, lines, file_path))
            
            # Calculate overall score
            overall_score = self._calculate_overall_score(metrics)
            
            # Generate complexity analysis
            complexity_analysis = self._generate_complexity_analysis(tree, lines)
            
            # Generate recommendations
            recommendations = self._generate_quality_recommendations(metrics, issues)
            
            return QualityReport(
                file_path=file_path,
                overall_score=overall_score,
                metrics=metrics,
                issues=issues,
                complexity_analysis=complexity_analysis,
                recommendations=recommendations
            )
            
        except SyntaxError as e:
            issues.append(QualityIssue(
                type="syntax_error",
                severity="critical",
                message=f"Syntax error: {e}",
                file_path=file_path,
                line_number=e.lineno or 1
            ))
            
            return QualityReport(
                file_path=file_path,
                overall_score=0.0,
                metrics=[],
                issues=issues,
                complexity_analysis={},
                recommendations=["Fix syntax errors before quality analysis"]
            )
    
    def _calculate_python_complexity(self, tree: ast.AST, lines: List[str]) -> List[QualityMetric]:
        """Calculate complexity metrics for Python code."""
        metrics = []
        
        # Cyclomatic complexity
        complexity = self._calculate_cyclomatic_complexity(tree)
        metrics.append(QualityMetric(
            name="cyclomatic_complexity",
            value=complexity,
            max_value=self.quality_thresholds['cyclomatic_complexity'],
            description="Cyclomatic complexity of the code",
            category="complexity"
        ))
        
        # Cognitive complexity
        cognitive_complexity = self._calculate_cognitive_complexity(tree)
        metrics.append(QualityMetric(
            name="cognitive_complexity",
            value=cognitive_complexity,
            max_value=self.quality_thresholds['cognitive_complexity'],
            description="Cognitive complexity (how hard it is to understand)",
            category="complexity"
        ))
        
        # Nesting depth
        max_depth = self._calculate_max_nesting_depth(tree)
        metrics.append(QualityMetric(
            name="max_nesting_depth",
            value=max_depth,
            max_value=self.quality_thresholds['nesting_depth'],
            description="Maximum nesting depth",
            category="complexity"
        ))
        
        return metrics
    
    def _calculate_python_maintainability(self, tree: ast.AST, content: str) -> List[QualityMetric]:
        """Calculate maintainability metrics for Python code."""
        metrics = []
        
        # Function length
        function_lengths = []
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                length = (node.end_lineno or node.lineno) - node.lineno + 1
                function_lengths.append(length)
        
        avg_function_length = sum(function_lengths) / len(function_lengths) if function_lengths else 0
        metrics.append(QualityMetric(
            name="average_function_length",
            value=avg_function_length,
            max_value=self.quality_thresholds['function_length'],
            description="Average function length in lines",
            category="maintainability"
        ))
        
        # Parameter count
        parameter_counts = []
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                param_count = len(node.args.args)
                parameter_counts.append(param_count)
        
        avg_param_count = sum(parameter_counts) / len(parameter_counts) if parameter_counts else 0
        metrics.append(QualityMetric(
            name="average_parameter_count",
            value=avg_param_count,
            max_value=self.quality_thresholds['parameter_count'],
            description="Average number of function parameters",
            category="maintainability"
        ))
        
        # Documentation coverage
        total_functions = len([n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)])
        documented_functions = len([n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef) and ast.get_docstring(n)])
        doc_coverage = documented_functions / total_functions if total_functions > 0 else 1.0
        
        metrics.append(QualityMetric(
            name="documentation_coverage",
            value=doc_coverage,
            max_value=1.0,
            description="Percentage of functions with docstrings",
            category="maintainability"
        ))
        
        return metrics
    
    def _calculate_python_readability(self, lines: List[str], content: str) -> List[QualityMetric]:
        """Calculate readability metrics for Python code."""
        metrics = []
        
        # Average line length
        non_empty_lines = [line for line in lines if line.strip()]
        avg_line_length = sum(len(line) for line in non_empty_lines) / len(non_empty_lines) if non_empty_lines else 0
        
        metrics.append(QualityMetric(
            name="average_line_length",
            value=avg_line_length,
            max_value=self.quality_thresholds['line_length'],
            description="Average line length",
            category="readability"
        ))
        
        # Comment ratio
        comment_lines = len([line for line in lines if line.strip().startswith('#')])
        total_lines = len(non_empty_lines)
        comment_ratio = comment_lines / total_lines if total_lines > 0 else 0
        
        metrics.append(QualityMetric(
            name="comment_ratio",
            value=comment_ratio,
            max_value=0.3,  # 30% comments is good
            description="Ratio of comment lines to code lines",
            category="readability"
        ))
        
        return metrics
    
    def _find_python_issues(self, tree: ast.AST, lines: List[str], file_path: str) -> List[QualityIssue]:
        """Find quality issues in Python code."""
        issues = []
        
        # Check for long functions
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                length = (node.end_lineno or node.lineno) - node.lineno + 1
                if length > self.quality_thresholds['function_length']:
                    issues.append(QualityIssue(
                        type="long_function",
                        severity="medium",
                        message=f"Function '{node.name}' is {length} lines long",
                        file_path=file_path,
                        line_number=node.lineno,
                        suggestion="Consider breaking down into smaller functions"
                    ))
                
                # Check parameter count
                param_count = len(node.args.args)
                if param_count > self.quality_thresholds['parameter_count']:
                    issues.append(QualityIssue(
                        type="too_many_parameters",
                        severity="medium",
                        message=f"Function '{node.name}' has {param_count} parameters",
                        file_path=file_path,
                        line_number=node.lineno,
                        suggestion="Consider using a configuration object"
                    ))
        
        # Check for long lines
        for line_num, line in enumerate(lines, 1):
            if len(line) > self.quality_thresholds['line_length']:
                issues.append(QualityIssue(
                    type="long_line",
                    severity="low",
                    message=f"Line {line_num} is {len(line)} characters long",
                    file_path=file_path,
                    line_number=line_num,
                    suggestion="Break line into multiple lines"
                ))
        
        return issues
    
    def _calculate_cyclomatic_complexity(self, tree: ast.AST) -> int:
        """Calculate cyclomatic complexity."""
        complexity = 1  # Base complexity
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
            elif isinstance(node, (ast.And, ast.Or)):
                complexity += 1
            elif isinstance(node, ast.comprehension):
                complexity += 1
        
        return complexity
    
    def _calculate_cognitive_complexity(self, tree: ast.AST) -> int:
        """Calculate cognitive complexity."""
        complexity = 0
        nesting_level = 0
        
        def visit_node(node, level):
            nonlocal complexity
            
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1 + level
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1 + level
            elif isinstance(node, (ast.And, ast.Or)):
                complexity += 1
            
            # Increase nesting for certain constructs
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor, ast.With, ast.AsyncWith)):
                level += 1
            
            for child in ast.iter_child_nodes(node):
                visit_node(child, level)
        
        visit_node(tree, 0)
        return complexity
    
    def _calculate_max_nesting_depth(self, tree: ast.AST) -> int:
        """Calculate maximum nesting depth."""
        max_depth = 0
        
        def visit_node(node, depth):
            nonlocal max_depth
            max_depth = max(max_depth, depth)
            
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor, ast.With, ast.AsyncWith, ast.Try)):
                depth += 1
            
            for child in ast.iter_child_nodes(node):
                visit_node(child, depth)
        
        visit_node(tree, 0)
        return max_depth
    
    def _analyze_js_quality(self, file_path: str, content: str) -> QualityReport:
        """Analyze JavaScript/TypeScript file quality."""
        lines = content.split('\n')
        metrics = []
        issues = []
        
        # Basic metrics for JS (could be enhanced with proper JS parser)
        non_empty_lines = [line for line in lines if line.strip()]
        avg_line_length = sum(len(line) for line in non_empty_lines) / len(non_empty_lines) if non_empty_lines else 0
        
        metrics.append(QualityMetric(
            name="average_line_length",
            value=avg_line_length,
            max_value=120,
            description="Average line length",
            category="readability"
        ))
        
        # Check for long lines
        for line_num, line in enumerate(lines, 1):
            if len(line) > 120:
                issues.append(QualityIssue(
                    type="long_line",
                    severity="low",
                    message=f"Line {line_num} is {len(line)} characters long",
                    file_path=file_path,
                    line_number=line_num
                ))
        
        overall_score = self._calculate_overall_score(metrics)
        
        return QualityReport(
            file_path=file_path,
            overall_score=overall_score,
            metrics=metrics,
            issues=issues,
            complexity_analysis={},
            recommendations=self._generate_quality_recommendations(metrics, issues)
        )
    
    def _analyze_generic_quality(self, file_path: str, content: str, language: str) -> QualityReport:
        """Analyze generic file quality."""
        lines = content.split('\n')
        metrics = []
        issues = []
        
        # Basic line metrics
        non_empty_lines = [line for line in lines if line.strip()]
        avg_line_length = sum(len(line) for line in non_empty_lines) / len(non_empty_lines) if non_empty_lines else 0
        
        metrics.append(QualityMetric(
            name="average_line_length",
            value=avg_line_length,
            max_value=120,
            description="Average line length",
            category="readability"
        ))
        
        overall_score = self._calculate_overall_score(metrics)
        
        return QualityReport(
            file_path=file_path,
            overall_score=overall_score,
            metrics=metrics,
            issues=issues,
            complexity_analysis={},
            recommendations=[]
        )
    
    def _calculate_overall_score(self, metrics: List[QualityMetric]) -> float:
        """Calculate overall quality score (0-1)."""
        if not metrics:
            return 0.0
        
        # Weight different categories
        weights = {
            'complexity': 0.3,
            'maintainability': 0.3,
            'readability': 0.2,
            'testability': 0.2
        }
        
        category_scores = defaultdict(list)
        for metric in metrics:
            # Invert score for metrics where lower is better
            if metric.name in ['cyclomatic_complexity', 'cognitive_complexity', 'max_nesting_depth', 'average_line_length']:
                score = max(0.0, 1.0 - metric.score)
            else:
                score = metric.score
            
            category_scores[metric.category].append(score)
        
        # Calculate weighted average
        total_score = 0.0
        total_weight = 0.0
        
        for category, scores in category_scores.items():
            if scores:
                category_score = sum(scores) / len(scores)
                weight = weights.get(category, 0.1)
                total_score += category_score * weight
                total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def _generate_complexity_analysis(self, tree: ast.AST, lines: List[str]) -> Dict[str, Any]:
        """Generate detailed complexity analysis."""
        analysis = {
            "total_lines": len(lines),
            "code_lines": len([line for line in lines if line.strip() and not line.strip().startswith('#')]),
            "functions": [],
            "classes": []
        }
        
        # Analyze functions
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                func_complexity = self._calculate_cyclomatic_complexity(node)
                analysis["functions"].append({
                    "name": node.name,
                    "line": node.lineno,
                    "length": (node.end_lineno or node.lineno) - node.lineno + 1,
                    "complexity": func_complexity,
                    "parameters": len(node.args.args)
                })
            elif isinstance(node, ast.ClassDef):
                analysis["classes"].append({
                    "name": node.name,
                    "line": node.lineno,
                    "length": (node.end_lineno or node.lineno) - node.lineno + 1
                })
        
        return analysis
    
    def _generate_quality_recommendations(self, metrics: List[QualityMetric], issues: List[QualityIssue]) -> List[str]:
        """Generate quality improvement recommendations."""
        recommendations = []
        
        # Analyze metrics for recommendations
        for metric in metrics:
            if metric.score < 0.5:  # Poor score
                if metric.name == "cyclomatic_complexity":
                    recommendations.append("Reduce cyclomatic complexity by breaking down complex functions")
                elif metric.name == "documentation_coverage":
                    recommendations.append("Add docstrings to functions and classes")
                elif metric.name == "average_line_length":
                    recommendations.append("Break long lines to improve readability")
        
        # Analyze issues for recommendations
        issue_types = set(issue.type for issue in issues)
        if "long_function" in issue_types:
            recommendations.append("Break down long functions into smaller, focused functions")
        if "too_many_parameters" in issue_types:
            recommendations.append("Reduce function parameters using configuration objects or dataclasses")
        
        return list(set(recommendations))  # Remove duplicates
    
    def _detect_language(self, file_path: str) -> str:
        """Detect programming language from file extension."""
        extension = file_path.split('.')[-1].lower()
        
        language_map = {
            'py': 'python',
            'js': 'javascript',
            'ts': 'typescript',
            'jsx': 'javascript',
            'tsx': 'typescript'
        }
        
        return language_map.get(extension, 'text')


# Global code quality analyzer instance
code_quality_analyzer = CodeQualityAnalyzer()
