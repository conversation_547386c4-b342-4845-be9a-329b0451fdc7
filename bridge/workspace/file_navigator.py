"""
Intelligent file and symbol navigation for enhanced workspace features.
Provides smart file search, symbol lookup, and navigation assistance.
"""

import logging
import os
import re
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, asdict
from collections import defaultdict
import threading

from bridge.core.context_manager import context_analyzer, Symbol, ContextType
from bridge.workspace.project_analyzer import project_analyzer

logger = logging.getLogger(__name__)


@dataclass
class FileMatch:
    """File search match result."""
    file_path: str
    score: float
    match_type: str  # name, path, content
    context: Optional[str] = None
    line_number: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class SymbolMatch:
    """Symbol search match result."""
    symbol: Symbol
    file_path: str
    score: float
    match_type: str  # name, type, signature
    context: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['symbol'] = asdict(self.symbol)
        return data


@dataclass
class NavigationSuggestion:
    """Navigation suggestion."""
    type: str  # file, symbol, definition, reference
    target: str
    description: str
    file_path: str
    line_number: Optional[int] = None
    confidence: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


class FileNavigator:
    """Intelligent file and symbol navigation system."""
    
    def __init__(self):
        self.file_index: Dict[str, Dict[str, Any]] = {}
        self.symbol_index: Dict[str, List[Symbol]] = {}
        self.lock = threading.RLock()
        
        # Search weights
        self.search_weights = {
            'exact_name': 1.0,
            'partial_name': 0.8,
            'path_match': 0.6,
            'content_match': 0.4,
            'fuzzy_match': 0.3
        }
    
    def index_project(self, project_path: str) -> bool:
        """
        Index a project for navigation.
        
        Args:
            project_path: Path to the project root.
            
        Returns:
            True if indexing successful.
        """
        try:
            logger.info(f"Indexing project for navigation: {project_path}")
            start_time = time.time()
            
            # Get or create project analysis
            analysis = project_analyzer.get_project_analysis(project_path)
            if not analysis:
                analysis = project_analyzer.analyze_project(project_path)
            
            with self.lock:
                # Index files
                self.file_index[project_path] = {}
                self.symbol_index[project_path] = []
                
                for file_path, file_context in analysis.files.items():
                    full_path = os.path.join(project_path, file_path)
                    
                    # Index file
                    self.file_index[project_path][file_path] = {
                        'name': os.path.basename(file_path),
                        'path': file_path,
                        'full_path': full_path,
                        'language': file_context.language,
                        'symbols': len(file_context.symbols),
                        'imports': file_context.imports
                    }
                    
                    # Index symbols
                    for symbol in file_context.symbols:
                        symbol_with_file = Symbol(
                            name=symbol.name,
                            type=symbol.type,
                            line_start=symbol.line_start,
                            line_end=symbol.line_end,
                            column_start=symbol.column_start,
                            column_end=symbol.column_end,
                            scope=symbol.scope,
                            signature=symbol.signature,
                            docstring=symbol.docstring,
                            parent=file_path  # Store file path in parent field
                        )
                        self.symbol_index[project_path].append(symbol_with_file)
            
            index_time = time.time() - start_time
            logger.info(f"Project indexing completed in {index_time:.2f}s: {len(analysis.files)} files, {len(self.symbol_index[project_path])} symbols")
            
            return True
            
        except Exception as e:
            logger.error(f"Project indexing failed for {project_path}: {e}")
            return False
    
    def search_files(self, project_path: str, query: str, max_results: int = 20) -> List[FileMatch]:
        """
        Search for files in a project.
        
        Args:
            project_path: Path to the project root.
            query: Search query.
            max_results: Maximum number of results.
            
        Returns:
            List of file matches.
        """
        if project_path not in self.file_index:
            self.index_project(project_path)
        
        matches = []
        query_lower = query.lower()
        
        with self.lock:
            file_data = self.file_index.get(project_path, {})
            
            for file_path, file_info in file_data.items():
                score = 0.0
                match_type = None
                
                file_name = file_info['name'].lower()
                file_path_lower = file_path.lower()
                
                # Exact name match
                if file_name == query_lower:
                    score = self.search_weights['exact_name']
                    match_type = 'exact_name'
                
                # Partial name match
                elif query_lower in file_name:
                    score = self.search_weights['partial_name']
                    match_type = 'partial_name'
                
                # Path match
                elif query_lower in file_path_lower:
                    score = self.search_weights['path_match']
                    match_type = 'path_match'
                
                # Fuzzy match
                elif self._fuzzy_match(query_lower, file_name):
                    score = self.search_weights['fuzzy_match']
                    match_type = 'fuzzy_match'
                
                if score > 0:
                    matches.append(FileMatch(
                        file_path=file_path,
                        score=score,
                        match_type=match_type,
                        context=f"{file_info['language']} file with {file_info['symbols']} symbols"
                    ))
        
        # Sort by score and return top results
        matches.sort(key=lambda x: x.score, reverse=True)
        return matches[:max_results]
    
    def search_symbols(self, project_path: str, query: str, symbol_type: Optional[str] = None, 
                      max_results: int = 20) -> List[SymbolMatch]:
        """
        Search for symbols in a project.
        
        Args:
            project_path: Path to the project root.
            query: Search query.
            symbol_type: Optional symbol type filter (function, class, variable).
            max_results: Maximum number of results.
            
        Returns:
            List of symbol matches.
        """
        if project_path not in self.symbol_index:
            self.index_project(project_path)
        
        matches = []
        query_lower = query.lower()
        
        with self.lock:
            symbols = self.symbol_index.get(project_path, [])
            
            for symbol in symbols:
                # Filter by type if specified
                if symbol_type and symbol.type.value != symbol_type:
                    continue
                
                score = 0.0
                match_type = None
                
                symbol_name_lower = symbol.name.lower()
                
                # Exact name match
                if symbol_name_lower == query_lower:
                    score = self.search_weights['exact_name']
                    match_type = 'exact_name'
                
                # Partial name match
                elif query_lower in symbol_name_lower:
                    score = self.search_weights['partial_name']
                    match_type = 'partial_name'
                
                # Signature match
                elif symbol.signature and query_lower in symbol.signature.lower():
                    score = self.search_weights['content_match']
                    match_type = 'signature_match'
                
                # Fuzzy match
                elif self._fuzzy_match(query_lower, symbol_name_lower):
                    score = self.search_weights['fuzzy_match']
                    match_type = 'fuzzy_match'
                
                if score > 0:
                    matches.append(SymbolMatch(
                        symbol=symbol,
                        file_path=symbol.parent,  # File path stored in parent field
                        score=score,
                        match_type=match_type,
                        context=symbol.signature or f"{symbol.type.value} in {symbol.scope}"
                    ))
        
        # Sort by score and return top results
        matches.sort(key=lambda x: x.score, reverse=True)
        return matches[:max_results]
    
    def find_definition(self, project_path: str, file_path: str, line: int, column: int) -> Optional[NavigationSuggestion]:
        """
        Find definition of symbol at cursor position.
        
        Args:
            project_path: Path to the project root.
            file_path: Current file path.
            line: Line number (0-based).
            column: Column number (0-based).
            
        Returns:
            Navigation suggestion for definition.
        """
        try:
            # Get symbol at position
            symbol = context_analyzer.get_context_at_position(file_path, line, column)
            if not symbol:
                return None
            
            # Search for definition in project
            definition_matches = self.search_symbols(project_path, symbol.name, symbol.type.value)
            
            if definition_matches:
                best_match = definition_matches[0]
                return NavigationSuggestion(
                    type='definition',
                    target=best_match.symbol.name,
                    description=f"Go to definition of {symbol.name}",
                    file_path=best_match.file_path,
                    line_number=best_match.symbol.line_start - 1,  # Convert to 0-based
                    confidence=best_match.score
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Find definition failed: {e}")
            return None
    
    def find_references(self, project_path: str, symbol_name: str, symbol_type: Optional[str] = None) -> List[NavigationSuggestion]:
        """
        Find references to a symbol.
        
        Args:
            project_path: Path to the project root.
            symbol_name: Symbol name to find references for.
            symbol_type: Optional symbol type.
            
        Returns:
            List of navigation suggestions for references.
        """
        references = []
        
        try:
            # Get project analysis
            analysis = project_analyzer.get_project_analysis(project_path)
            if not analysis:
                return references
            
            # Search for symbol usage in all files
            for file_path, file_context in analysis.files.items():
                # Simple text search for now (could be enhanced with AST analysis)
                try:
                    full_path = os.path.join(project_path, file_path)
                    with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                    
                    for line_num, line_content in enumerate(lines):
                        if symbol_name in line_content:
                            references.append(NavigationSuggestion(
                                type='reference',
                                target=symbol_name,
                                description=f"Reference to {symbol_name}",
                                file_path=file_path,
                                line_number=line_num,
                                confidence=0.8
                            ))
                
                except Exception as e:
                    logger.warning(f"Could not search references in {file_path}: {e}")
            
            return references[:50]  # Limit results
            
        except Exception as e:
            logger.error(f"Find references failed: {e}")
            return references
    
    def get_navigation_suggestions(self, project_path: str, file_path: str, 
                                 context: Optional[str] = None) -> List[NavigationSuggestion]:
        """
        Get intelligent navigation suggestions for current context.
        
        Args:
            project_path: Path to the project root.
            file_path: Current file path.
            context: Optional context information.
            
        Returns:
            List of navigation suggestions.
        """
        suggestions = []
        
        try:
            # Get file analysis
            analysis = project_analyzer.get_project_analysis(project_path)
            if not analysis or file_path not in analysis.files:
                return suggestions
            
            file_context = analysis.files[file_path]
            
            # Suggest related files based on imports
            for import_name in file_context.imports:
                # Try to find corresponding files
                related_files = self.search_files(project_path, import_name, max_results=3)
                for file_match in related_files:
                    suggestions.append(NavigationSuggestion(
                        type='file',
                        target=file_match.file_path,
                        description=f"Related import: {import_name}",
                        file_path=file_match.file_path,
                        confidence=file_match.score * 0.8
                    ))
            
            # Suggest test files
            if not 'test' in file_path.lower():
                test_files = self.search_files(project_path, f"test_{os.path.basename(file_path)}", max_results=3)
                for test_file in test_files:
                    suggestions.append(NavigationSuggestion(
                        type='file',
                        target=test_file.file_path,
                        description=f"Test file for {os.path.basename(file_path)}",
                        file_path=test_file.file_path,
                        confidence=test_file.score * 0.7
                    ))
            
            # Suggest main files if in test
            if 'test' in file_path.lower():
                main_name = os.path.basename(file_path).replace('test_', '').replace('_test', '')
                main_files = self.search_files(project_path, main_name, max_results=3)
                for main_file in main_files:
                    if 'test' not in main_file.file_path.lower():
                        suggestions.append(NavigationSuggestion(
                            type='file',
                            target=main_file.file_path,
                            description=f"Main file for test",
                            file_path=main_file.file_path,
                            confidence=main_file.score * 0.7
                        ))
            
            # Sort by confidence
            suggestions.sort(key=lambda x: x.confidence, reverse=True)
            return suggestions[:10]  # Limit to top 10
            
        except Exception as e:
            logger.error(f"Get navigation suggestions failed: {e}")
            return suggestions
    
    def _fuzzy_match(self, query: str, target: str) -> bool:
        """Simple fuzzy matching algorithm."""
        if len(query) > len(target):
            return False
        
        query_idx = 0
        for char in target:
            if query_idx < len(query) and char == query[query_idx]:
                query_idx += 1
        
        return query_idx == len(query)
    
    def get_file_outline(self, project_path: str, file_path: str) -> List[Dict[str, Any]]:
        """
        Get file outline with symbols.
        
        Args:
            project_path: Path to the project root.
            file_path: File path relative to project.
            
        Returns:
            List of symbols in outline format.
        """
        try:
            analysis = project_analyzer.get_project_analysis(project_path)
            if not analysis or file_path not in analysis.files:
                return []
            
            file_context = analysis.files[file_path]
            outline = []
            
            for symbol in file_context.symbols:
                outline.append({
                    'name': symbol.name,
                    'type': symbol.type.value,
                    'line': symbol.line_start,
                    'scope': symbol.scope,
                    'signature': symbol.signature,
                    'docstring': symbol.docstring
                })
            
            # Sort by line number
            outline.sort(key=lambda x: x['line'])
            return outline
            
        except Exception as e:
            logger.error(f"Get file outline failed: {e}")
            return []
    
    def get_navigation_stats(self) -> Dict[str, Any]:
        """Get navigation statistics."""
        with self.lock:
            total_files = sum(len(files) for files in self.file_index.values())
            total_symbols = sum(len(symbols) for symbols in self.symbol_index.values())
            
            return {
                "indexed_projects": len(self.file_index),
                "total_files": total_files,
                "total_symbols": total_symbols,
                "projects": [
                    {
                        "path": project_path,
                        "files": len(files),
                        "symbols": len(self.symbol_index.get(project_path, []))
                    }
                    for project_path, files in self.file_index.items()
                ]
            }


# Global file navigator instance
file_navigator = FileNavigator()
