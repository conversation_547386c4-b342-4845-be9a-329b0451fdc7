"""
Performance optimization module for achieving <200ms completion response times.
Implements advanced caching, pre-fetching, and optimization strategies.
"""

import asyncio
import logging
import time
import threading
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque
import hashlib
import json

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics for tracking optimization effectiveness."""
    response_time_ms: float
    cache_hit: bool
    tools_used: List[str]
    context_size: int
    completion_count: int
    memory_usage_mb: float
    timestamp: float

class CompletionPredictor:
    """Predicts likely completion requests for pre-fetching."""
    
    def __init__(self):
        self.request_patterns = defaultdict(list)
        self.file_patterns = defaultdict(list)
        self.language_patterns = defaultdict(list)
        self.max_history = 1000
    
    def record_request(self, context: Dict[str, Any], completions: List[Dict[str, Any]]):
        """Record a completion request for pattern analysis."""
        file_path = context.get('file_path', '')
        language = context.get('language', '')
        cursor_line = context.get('cursor_line', 0)
        
        # Extract patterns
        file_extension = file_path.split('.')[-1] if '.' in file_path else ''
        
        # Record patterns
        pattern_key = f"{language}:{file_extension}"
        self.request_patterns[pattern_key].append({
            'context': self._extract_context_features(context),
            'completions': len(completions),
            'timestamp': time.time()
        })
        
        # Limit history size
        if len(self.request_patterns[pattern_key]) > self.max_history:
            self.request_patterns[pattern_key] = self.request_patterns[pattern_key][-self.max_history:]
    
    def predict_next_requests(self, current_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Predict likely next completion requests."""
        predictions = []
        
        file_path = current_context.get('file_path', '')
        language = current_context.get('language', '')
        cursor_line = current_context.get('cursor_line', 0)
        
        file_extension = file_path.split('.')[-1] if '.' in file_path else ''
        pattern_key = f"{language}:{file_extension}"
        
        if pattern_key in self.request_patterns:
            recent_patterns = self.request_patterns[pattern_key][-50:]  # Last 50 requests
            
            # Predict next line completions
            for offset in [1, 2, 3]:
                predicted_context = current_context.copy()
                predicted_context['cursor_line'] = cursor_line + offset
                predicted_context['cursor_column'] = 0
                predictions.append(predicted_context)
        
        return predictions[:5]  # Limit to 5 predictions
    
    def _extract_context_features(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract relevant features from context for pattern matching."""
        return {
            'language': context.get('language', ''),
            'cursor_line': context.get('cursor_line', 0),
            'file_size': len(context.get('content', '')),
            'has_imports': 'import' in context.get('content', ''),
            'has_functions': 'def ' in context.get('content', '') or 'function' in context.get('content', ''),
            'has_classes': 'class ' in context.get('content', '')
        }

class AdvancedCompletionCache:
    """Advanced caching system with intelligent invalidation and pre-fetching."""
    
    def __init__(self, max_size: int = 2000, max_age_seconds: int = 300):
        self.cache = {}
        self.access_times = {}
        self.access_counts = defaultdict(int)
        self.max_size = max_size
        self.max_age_seconds = max_age_seconds
        self.predictor = CompletionPredictor()
        self.prefetch_queue = asyncio.Queue()
        self.prefetch_cache = {}
        self.performance_metrics = deque(maxlen=1000)
        self._lock = threading.RLock()
        
        # Start background prefetch worker
        self._start_prefetch_worker()
    
    def get(self, key: str) -> Optional[Tuple[List[Dict[str, Any]], Dict[str, Any]]]:
        """Get cached completion with metadata."""
        with self._lock:
            if key not in self.cache:
                return None
            
            entry = self.cache[key]
            
            # Check if expired
            if time.time() - entry['timestamp'] > self.max_age_seconds:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                return None
            
            # Update access statistics
            self.access_times[key] = time.time()
            self.access_counts[key] += 1
            
            return entry['completions'], entry['metadata']
    
    def set(self, key: str, completions: List[Dict[str, Any]], metadata: Dict[str, Any], context: Dict[str, Any]):
        """Set cached completion with context for prediction."""
        with self._lock:
            # Evict if cache is full
            if len(self.cache) >= self.max_size:
                self._evict_lru()
            
            # Store in cache
            self.cache[key] = {
                'completions': completions,
                'metadata': metadata,
                'timestamp': time.time(),
                'context': context
            }
            self.access_times[key] = time.time()
            self.access_counts[key] = 1
            
            # Record for prediction
            self.predictor.record_request(context, completions)
            
            # Queue prefetch predictions
            predictions = self.predictor.predict_next_requests(context)
            for prediction in predictions:
                try:
                    self.prefetch_queue.put_nowait(prediction)
                except asyncio.QueueFull:
                    pass  # Skip if queue is full
    
    def get_prefetched(self, key: str) -> Optional[Tuple[List[Dict[str, Any]], Dict[str, Any]]]:
        """Get prefetched completion."""
        with self._lock:
            if key in self.prefetch_cache:
                entry = self.prefetch_cache[key]
                
                # Check if expired
                if time.time() - entry['timestamp'] > self.max_age_seconds / 2:  # Shorter TTL for prefetch
                    del self.prefetch_cache[key]
                    return None
                
                # Move to main cache
                self.cache[key] = entry
                del self.prefetch_cache[key]
                
                return entry['completions'], entry['metadata']
            
            return None
    
    def record_performance(self, metrics: PerformanceMetrics):
        """Record performance metrics for optimization."""
        self.performance_metrics.append(metrics)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        if not self.performance_metrics:
            return {}
        
        recent_metrics = list(self.performance_metrics)[-100:]  # Last 100 requests
        
        response_times = [m.response_time_ms for m in recent_metrics]
        cache_hits = [m.cache_hit for m in recent_metrics]
        
        return {
            'avg_response_time_ms': sum(response_times) / len(response_times),
            'median_response_time_ms': sorted(response_times)[len(response_times) // 2],
            'p95_response_time_ms': sorted(response_times)[int(len(response_times) * 0.95)],
            'cache_hit_rate': sum(cache_hits) / len(cache_hits),
            'total_requests': len(recent_metrics),
            'cache_size': len(self.cache),
            'prefetch_cache_size': len(self.prefetch_cache)
        }
    
    def optimize_cache(self):
        """Optimize cache based on performance metrics."""
        stats = self.get_performance_stats()
        
        if not stats:
            return
        
        # Adjust cache size based on hit rate
        if stats['cache_hit_rate'] < 0.5:
            self.max_size = min(self.max_size * 1.2, 5000)  # Increase cache size
        elif stats['cache_hit_rate'] > 0.9:
            self.max_size = max(self.max_size * 0.9, 500)   # Decrease cache size
        
        # Adjust TTL based on response times
        if stats['avg_response_time_ms'] > 200:
            self.max_age_seconds = min(self.max_age_seconds * 1.1, 600)  # Increase TTL
        elif stats['avg_response_time_ms'] < 100:
            self.max_age_seconds = max(self.max_age_seconds * 0.9, 60)   # Decrease TTL
        
        logger.info(f"Cache optimized: size={self.max_size}, ttl={self.max_age_seconds}s, hit_rate={stats['cache_hit_rate']:.2f}")
    
    def _evict_lru(self):
        """Evict least recently used entries."""
        if not self.access_times:
            return
        
        # Find least recently used key
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        # Remove from cache
        if lru_key in self.cache:
            del self.cache[lru_key]
        if lru_key in self.access_times:
            del self.access_times[lru_key]
        if lru_key in self.access_counts:
            del self.access_counts[lru_key]
    
    def _start_prefetch_worker(self):
        """Start background worker for prefetching."""
        def prefetch_worker():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self._prefetch_loop())
        
        thread = threading.Thread(target=prefetch_worker, daemon=True)
        thread.start()
    
    async def _prefetch_loop(self):
        """Background loop for prefetching completions."""
        while True:
            try:
                # Get prediction from queue
                prediction = await asyncio.wait_for(self.prefetch_queue.get(), timeout=1.0)
                
                # Generate cache key
                cache_key = self._generate_cache_key(prediction)
                
                # Skip if already cached
                if cache_key in self.cache or cache_key in self.prefetch_cache:
                    continue
                
                # Simulate prefetch (in real implementation, call completion API)
                await asyncio.sleep(0.1)  # Simulate processing time
                
                # Store in prefetch cache
                with self._lock:
                    if len(self.prefetch_cache) < 100:  # Limit prefetch cache size
                        self.prefetch_cache[cache_key] = {
                            'completions': [],  # Would be actual completions
                            'metadata': {'prefetched': True},
                            'timestamp': time.time(),
                            'context': prediction
                        }
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error in prefetch worker: {e}")
                await asyncio.sleep(1)
    
    def _generate_cache_key(self, context: Dict[str, Any]) -> str:
        """Generate cache key from context."""
        key_data = {
            'file_path': context.get('file_path', ''),
            'cursor_line': context.get('cursor_line', 0),
            'cursor_column': context.get('cursor_column', 0),
            'content_hash': hashlib.md5(context.get('content', '').encode()).hexdigest()[:16]
        }
        return hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()

class PerformanceOptimizer:
    """Main performance optimizer coordinating all optimization strategies."""
    
    def __init__(self):
        self.cache = AdvancedCompletionCache()
        self.optimization_interval = 60  # Optimize every minute
        self.last_optimization = time.time()
        self.target_response_time_ms = 200
        
    def get_cached_completion(self, context: Dict[str, Any]) -> Optional[Tuple[List[Dict[str, Any]], Dict[str, Any]]]:
        """Get completion from cache with prefetch fallback."""
        cache_key = self.cache._generate_cache_key(context)
        
        # Try main cache first
        result = self.cache.get(cache_key)
        if result:
            return result
        
        # Try prefetch cache
        result = self.cache.get_prefetched(cache_key)
        if result:
            return result
        
        return None
    
    def cache_completion(self, context: Dict[str, Any], completions: List[Dict[str, Any]], metadata: Dict[str, Any]):
        """Cache completion with performance tracking."""
        cache_key = self.cache._generate_cache_key(context)
        self.cache.set(cache_key, completions, metadata, context)
        
        # Record performance metrics
        metrics = PerformanceMetrics(
            response_time_ms=metadata.get('response_time_ms', 0),
            cache_hit=False,
            tools_used=metadata.get('tools_used', []),
            context_size=len(context.get('content', '')),
            completion_count=len(completions),
            memory_usage_mb=0,  # TODO: Implement memory tracking
            timestamp=time.time()
        )
        self.cache.record_performance(metrics)
        
        # Periodic optimization
        if time.time() - self.last_optimization > self.optimization_interval:
            self.optimize()
            self.last_optimization = time.time()
    
    def record_cache_hit(self, response_time_ms: float, tools_used: List[str]):
        """Record cache hit performance."""
        metrics = PerformanceMetrics(
            response_time_ms=response_time_ms,
            cache_hit=True,
            tools_used=tools_used,
            context_size=0,
            completion_count=0,
            memory_usage_mb=0,
            timestamp=time.time()
        )
        self.cache.record_performance(metrics)
    
    def optimize(self):
        """Run optimization strategies."""
        stats = self.cache.get_performance_stats()
        
        if not stats:
            return
        
        logger.info(f"Performance optimization - avg: {stats.get('avg_response_time_ms', 0):.1f}ms, "
                   f"cache hit rate: {stats.get('cache_hit_rate', 0):.2f}")
        
        # Optimize cache
        self.cache.optimize_cache()
        
        # Check if we're meeting performance targets
        if stats.get('avg_response_time_ms', 0) > self.target_response_time_ms:
            logger.warning(f"Performance target missed: {stats['avg_response_time_ms']:.1f}ms > {self.target_response_time_ms}ms")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report."""
        stats = self.cache.get_performance_stats()
        
        return {
            'performance_stats': stats,
            'target_response_time_ms': self.target_response_time_ms,
            'meeting_target': stats.get('avg_response_time_ms', float('inf')) <= self.target_response_time_ms,
            'optimization_recommendations': self._get_optimization_recommendations(stats)
        }
    
    def _get_optimization_recommendations(self, stats: Dict[str, Any]) -> List[str]:
        """Get optimization recommendations based on current performance."""
        recommendations = []
        
        if not stats:
            return recommendations
        
        if stats.get('cache_hit_rate', 0) < 0.6:
            recommendations.append("Increase cache size or TTL to improve hit rate")
        
        if stats.get('avg_response_time_ms', 0) > self.target_response_time_ms:
            recommendations.append("Consider reducing SWE-Agent tool usage or implementing parallel processing")
        
        if stats.get('p95_response_time_ms', 0) > self.target_response_time_ms * 2:
            recommendations.append("Investigate and optimize slow completion requests")
        
        return recommendations

# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()
