"""
Context management for intelligent code analysis and understanding.
Provides context-aware features for code completion, chat, and workspace analysis.
"""

import ast
import logging
import re
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import json

logger = logging.getLogger(__name__)


class ContextType(Enum):
    """Types of code context."""
    FUNCTION = "function"
    CLASS = "class"
    MODULE = "module"
    VARIABLE = "variable"
    IMPORT = "import"
    COMMENT = "comment"
    STRING = "string"
    UNKNOWN = "unknown"


@dataclass
class Symbol:
    """Represents a code symbol (function, class, variable, etc.)."""
    name: str
    type: ContextType
    line_start: int
    line_end: int
    column_start: int
    column_end: int
    scope: str
    signature: Optional[str] = None
    docstring: Optional[str] = None
    parent: Optional[str] = None


@dataclass
class FileContext:
    """Context information for a file."""
    file_path: str
    language: str
    symbols: List[Symbol]
    imports: List[str]
    dependencies: List[str]
    project_root: Optional[str] = None
    last_modified: Optional[float] = None


@dataclass
class ProjectContext:
    """Context information for an entire project."""
    root_path: str
    files: Dict[str, FileContext]
    main_language: str
    dependencies: List[str]
    structure: Dict[str, Any]


class ContextAnalyzer:
    """Analyzes code context for intelligent features."""
    
    def __init__(self):
        self.file_contexts: Dict[str, FileContext] = {}
        self.project_contexts: Dict[str, ProjectContext] = {}
    
    def analyze_file(self, file_path: str, content: str, language: str = None) -> FileContext:
        """
        Analyze a file and extract context information.
        
        Args:
            file_path: Path to the file.
            content: File content.
            language: Programming language (auto-detected if None).
            
        Returns:
            File context with symbols and metadata.
        """
        if language is None:
            language = self._detect_language(file_path)
        
        try:
            if language.lower() == "python":
                context = self._analyze_python_file(file_path, content)
            elif language.lower() in ["javascript", "typescript"]:
                context = self._analyze_js_file(file_path, content)
            else:
                context = self._analyze_generic_file(file_path, content, language)
            
            # Cache the context
            self.file_contexts[file_path] = context
            
            logger.debug(f"Analyzed {file_path}: {len(context.symbols)} symbols found")
            return context
            
        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            return FileContext(
                file_path=file_path,
                language=language,
                symbols=[],
                imports=[],
                dependencies=[]
            )
    
    def _detect_language(self, file_path: str) -> str:
        """Detect programming language from file extension."""
        extension = Path(file_path).suffix.lower()
        
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.hpp': 'cpp',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.sh': 'bash',
            '.sql': 'sql',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml'
        }
        
        return language_map.get(extension, 'text')
    
    def _analyze_python_file(self, file_path: str, content: str) -> FileContext:
        """Analyze Python file using AST."""
        symbols = []
        imports = []
        
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    symbols.append(Symbol(
                        name=node.name,
                        type=ContextType.FUNCTION,
                        line_start=node.lineno,
                        line_end=node.end_lineno or node.lineno,
                        column_start=node.col_offset,
                        column_end=node.end_col_offset or node.col_offset,
                        scope=self._get_python_scope(node, tree),
                        signature=self._get_python_signature(node),
                        docstring=ast.get_docstring(node)
                    ))
                
                elif isinstance(node, ast.ClassDef):
                    symbols.append(Symbol(
                        name=node.name,
                        type=ContextType.CLASS,
                        line_start=node.lineno,
                        line_end=node.end_lineno or node.lineno,
                        column_start=node.col_offset,
                        column_end=node.end_col_offset or node.col_offset,
                        scope="global",
                        docstring=ast.get_docstring(node)
                    ))
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    else:
                        module = node.module or ""
                        for alias in node.names:
                            imports.append(f"{module}.{alias.name}" if module else alias.name)
        
        except SyntaxError as e:
            logger.warning(f"Syntax error in Python file {file_path}: {e}")
        except Exception as e:
            logger.error(f"Error parsing Python file {file_path}: {e}")
        
        return FileContext(
            file_path=file_path,
            language="python",
            symbols=symbols,
            imports=imports,
            dependencies=self._extract_python_dependencies(imports)
        )
    
    def _analyze_js_file(self, file_path: str, content: str) -> FileContext:
        """Analyze JavaScript/TypeScript file using regex patterns."""
        symbols = []
        imports = []
        
        # Function declarations
        func_pattern = r'(?:function\s+(\w+)|(?:const|let|var)\s+(\w+)\s*=\s*(?:function|\([^)]*\)\s*=>))'
        for match in re.finditer(func_pattern, content, re.MULTILINE):
            name = match.group(1) or match.group(2)
            line_num = content[:match.start()].count('\n') + 1
            symbols.append(Symbol(
                name=name,
                type=ContextType.FUNCTION,
                line_start=line_num,
                line_end=line_num,
                column_start=match.start(),
                column_end=match.end(),
                scope="global"
            ))
        
        # Class declarations
        class_pattern = r'class\s+(\w+)'
        for match in re.finditer(class_pattern, content, re.MULTILINE):
            name = match.group(1)
            line_num = content[:match.start()].count('\n') + 1
            symbols.append(Symbol(
                name=name,
                type=ContextType.CLASS,
                line_start=line_num,
                line_end=line_num,
                column_start=match.start(),
                column_end=match.end(),
                scope="global"
            ))
        
        # Import statements
        import_pattern = r'import\s+(?:{[^}]+}|\w+|\*\s+as\s+\w+)\s+from\s+[\'"]([^\'"]+)[\'"]'
        for match in re.finditer(import_pattern, content, re.MULTILINE):
            imports.append(match.group(1))
        
        return FileContext(
            file_path=file_path,
            language="javascript",
            symbols=symbols,
            imports=imports,
            dependencies=imports
        )
    
    def _analyze_generic_file(self, file_path: str, content: str, language: str) -> FileContext:
        """Generic analysis for unsupported languages."""
        symbols = []
        
        # Basic function detection using common patterns
        func_patterns = [
            r'def\s+(\w+)',  # Python
            r'function\s+(\w+)',  # JavaScript
            r'(\w+)\s*\([^)]*\)\s*{',  # C-style
            r'public\s+\w+\s+(\w+)\s*\(',  # Java
        ]
        
        for pattern in func_patterns:
            for match in re.finditer(pattern, content, re.MULTILINE):
                name = match.group(1)
                line_num = content[:match.start()].count('\n') + 1
                symbols.append(Symbol(
                    name=name,
                    type=ContextType.FUNCTION,
                    line_start=line_num,
                    line_end=line_num,
                    column_start=match.start(),
                    column_end=match.end(),
                    scope="global"
                ))
        
        return FileContext(
            file_path=file_path,
            language=language,
            symbols=symbols,
            imports=[],
            dependencies=[]
        )
    
    def _get_python_scope(self, node: ast.AST, tree: ast.AST) -> str:
        """Get the scope of a Python AST node."""
        # Simplified scope detection
        for parent in ast.walk(tree):
            if hasattr(parent, 'body') and node in parent.body:
                if isinstance(parent, ast.ClassDef):
                    return parent.name
                elif isinstance(parent, ast.FunctionDef):
                    return parent.name
        return "global"
    
    def _get_python_signature(self, node: ast.FunctionDef) -> str:
        """Get function signature from Python AST node."""
        args = []
        for arg in node.args.args:
            args.append(arg.arg)
        return f"{node.name}({', '.join(args)})"
    
    def _extract_python_dependencies(self, imports: List[str]) -> List[str]:
        """Extract package dependencies from Python imports."""
        dependencies = set()
        for imp in imports:
            # Extract top-level package name
            package = imp.split('.')[0]
            if not package.startswith('.'):  # Skip relative imports
                dependencies.add(package)
        return list(dependencies)
    
    def get_context_at_position(self, file_path: str, line: int, column: int) -> Optional[Symbol]:
        """
        Get the context symbol at a specific position.
        
        Args:
            file_path: Path to the file.
            line: Line number (0-based).
            column: Column number (0-based).
            
        Returns:
            Symbol at the position or None.
        """
        context = self.file_contexts.get(file_path)
        if not context:
            return None
        
        for symbol in context.symbols:
            if (symbol.line_start <= line + 1 <= symbol.line_end and
                symbol.column_start <= column <= symbol.column_end):
                return symbol
        
        return None
    
    def get_symbols_in_scope(self, file_path: str, line: int) -> List[Symbol]:
        """
        Get all symbols visible at a specific line.
        
        Args:
            file_path: Path to the file.
            line: Line number (0-based).
            
        Returns:
            List of visible symbols.
        """
        context = self.file_contexts.get(file_path)
        if not context:
            return []
        
        visible_symbols = []
        for symbol in context.symbols:
            # Symbol is visible if we're inside its scope or it's global
            if (symbol.scope == "global" or 
                symbol.line_start <= line + 1 <= symbol.line_end):
                visible_symbols.append(symbol)
        
        return visible_symbols
    
    def analyze_project(self, root_path: str) -> ProjectContext:
        """
        Analyze an entire project for context.
        
        Args:
            root_path: Root directory of the project.
            
        Returns:
            Project context with all files analyzed.
        """
        project_files = {}
        languages = {}
        all_dependencies = set()
        
        root = Path(root_path)
        
        # Find all source files
        for file_path in root.rglob("*"):
            if file_path.is_file() and self._is_source_file(file_path):
                try:
                    content = file_path.read_text(encoding='utf-8')
                    context = self.analyze_file(str(file_path), content)
                    project_files[str(file_path)] = context
                    
                    # Track language usage
                    lang = context.language
                    languages[lang] = languages.get(lang, 0) + 1
                    
                    # Collect dependencies
                    all_dependencies.update(context.dependencies)
                    
                except Exception as e:
                    logger.warning(f"Could not analyze file {file_path}: {e}")
        
        # Determine main language
        main_language = max(languages.items(), key=lambda x: x[1])[0] if languages else "unknown"
        
        project_context = ProjectContext(
            root_path=root_path,
            files=project_files,
            main_language=main_language,
            dependencies=list(all_dependencies),
            structure=self._analyze_project_structure(root)
        )
        
        self.project_contexts[root_path] = project_context
        logger.info(f"Analyzed project {root_path}: {len(project_files)} files, main language: {main_language}")
        
        return project_context
    
    def _is_source_file(self, file_path: Path) -> bool:
        """Check if a file is a source code file."""
        source_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.h', '.hpp',
            '.cs', '.go', '.rs', '.php', '.rb', '.swift', '.kt', '.scala'
        }
        return file_path.suffix.lower() in source_extensions
    
    def _analyze_project_structure(self, root: Path) -> Dict[str, Any]:
        """Analyze project directory structure."""
        structure = {}
        
        for item in root.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                structure[item.name] = "directory"
            elif item.is_file():
                structure[item.name] = "file"
        
        return structure


# Global context analyzer instance
context_analyzer = ContextAnalyzer()
