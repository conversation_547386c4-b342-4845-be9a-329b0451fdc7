"""
Conversation context management for maintaining context across chat interactions.
Provides intelligent context preservation, relevance scoring, and context summarization.
"""

import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
import hashlib

logger = logging.getLogger(__name__)


@dataclass
class ContextItem:
    """A single context item with relevance scoring."""
    id: str
    type: str  # file, symbol, conversation, code_block, etc.
    content: str
    metadata: Dict[str, Any]
    relevance_score: float
    timestamp: datetime
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['last_accessed'] = self.last_accessed.isoformat() if self.last_accessed else None
        return data


@dataclass
class ContextWindow:
    """A sliding window of relevant context."""
    items: List[ContextItem]
    max_size: int
    total_relevance: float
    
    def add_item(self, item: ContextItem):
        """Add item to context window."""
        self.items.append(item)
        self.total_relevance += item.relevance_score
        
        # Sort by relevance and trim if necessary
        self.items.sort(key=lambda x: x.relevance_score, reverse=True)
        if len(self.items) > self.max_size:
            removed = self.items.pop()
            self.total_relevance -= removed.relevance_score
    
    def get_summary(self) -> str:
        """Get a summary of the context window."""
        if not self.items:
            return "No context available."
        
        summary_parts = []
        for item in self.items[:5]:  # Top 5 most relevant items
            summary_parts.append(f"- {item.type}: {item.content[:100]}...")
        
        return "\n".join(summary_parts)


class ConversationContextManager:
    """Manages conversation context with intelligent relevance scoring."""
    
    def __init__(self, max_context_items: int = 100, context_window_size: int = 20):
        self.context_items: Dict[str, ContextItem] = {}
        self.session_contexts: Dict[str, ContextWindow] = {}
        self.max_context_items = max_context_items
        self.context_window_size = context_window_size
        self.relevance_decay_factor = 0.95  # Decay factor for time-based relevance
    
    def add_context_item(self, session_id: str, item_type: str, content: str, 
                        metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Add a context item to the session.
        
        Args:
            session_id: Session identifier.
            item_type: Type of context item.
            content: Content of the item.
            metadata: Optional metadata.
            
        Returns:
            Context item ID.
        """
        if metadata is None:
            metadata = {}
        
        # Generate unique ID
        item_id = hashlib.md5(f"{session_id}:{item_type}:{content}".encode()).hexdigest()[:12]
        
        # Calculate initial relevance score
        relevance_score = self._calculate_relevance_score(item_type, content, metadata)
        
        # Create context item
        item = ContextItem(
            id=item_id,
            type=item_type,
            content=content,
            metadata=metadata,
            relevance_score=relevance_score,
            timestamp=datetime.now()
        )
        
        # Store globally
        self.context_items[item_id] = item
        
        # Add to session context window
        if session_id not in self.session_contexts:
            self.session_contexts[session_id] = ContextWindow([], self.context_window_size, 0.0)
        
        self.session_contexts[session_id].add_item(item)
        
        # Cleanup old items if necessary
        self._cleanup_old_items()
        
        logger.debug(f"Added context item {item_id} to session {session_id}")
        return item_id
    
    def get_relevant_context(self, session_id: str, query: str, max_items: int = 10) -> List[ContextItem]:
        """
        Get relevant context items for a query.
        
        Args:
            session_id: Session identifier.
            query: Query to find relevant context for.
            max_items: Maximum number of items to return.
            
        Returns:
            List of relevant context items.
        """
        if session_id not in self.session_contexts:
            return []
        
        context_window = self.session_contexts[session_id]
        
        # Score items based on query relevance
        scored_items = []
        for item in context_window.items:
            query_relevance = self._calculate_query_relevance(item, query)
            time_relevance = self._calculate_time_relevance(item)
            
            # Update access information
            item.access_count += 1
            item.last_accessed = datetime.now()
            
            # Combined relevance score
            combined_score = (item.relevance_score * 0.4 + 
                            query_relevance * 0.4 + 
                            time_relevance * 0.2)
            
            scored_items.append((item, combined_score))
        
        # Sort by combined score and return top items
        scored_items.sort(key=lambda x: x[1], reverse=True)
        return [item for item, score in scored_items[:max_items]]
    
    def update_context_from_message(self, session_id: str, message: str, 
                                   context: Optional[Dict[str, Any]] = None):
        """
        Update context based on a new message.
        
        Args:
            session_id: Session identifier.
            message: Message content.
            context: Optional context information.
        """
        # Add message as context
        self.add_context_item(
            session_id, 
            "message", 
            message, 
            {"role": "user", "timestamp": datetime.now().isoformat()}
        )
        
        # Add file context if available
        if context and context.get('file_path'):
            file_content = context.get('content', '')
            if file_content:
                self.add_context_item(
                    session_id,
                    "file",
                    file_content[:1000],  # Limit content size
                    {
                        "file_path": context['file_path'],
                        "language": context.get('language'),
                        "cursor_line": context.get('cursor_line'),
                        "cursor_column": context.get('cursor_column')
                    }
                )
        
        # Add selection context if available
        if context and context.get('selection'):
            self.add_context_item(
                session_id,
                "selection",
                context['selection'],
                {
                    "file_path": context.get('file_path'),
                    "language": context.get('language')
                }
            )
        
        # Extract and add code blocks from message
        code_blocks = self._extract_code_blocks(message)
        for i, code_block in enumerate(code_blocks):
            self.add_context_item(
                session_id,
                "code_block",
                code_block,
                {"source": "user_message", "index": i}
            )
    
    def get_context_summary(self, session_id: str) -> str:
        """Get a summary of the current context for a session."""
        if session_id not in self.session_contexts:
            return "No context available for this session."
        
        context_window = self.session_contexts[session_id]
        
        # Group items by type
        items_by_type = defaultdict(list)
        for item in context_window.items:
            items_by_type[item.type].append(item)
        
        summary_parts = []
        
        # Add file context
        if "file" in items_by_type:
            file_items = items_by_type["file"]
            file_paths = [item.metadata.get("file_path", "unknown") for item in file_items]
            summary_parts.append(f"Files in context: {', '.join(set(file_paths))}")
        
        # Add recent messages
        if "message" in items_by_type:
            message_count = len(items_by_type["message"])
            summary_parts.append(f"Recent messages: {message_count}")
        
        # Add code blocks
        if "code_block" in items_by_type:
            code_count = len(items_by_type["code_block"])
            summary_parts.append(f"Code blocks: {code_count}")
        
        # Add selection context
        if "selection" in items_by_type:
            summary_parts.append("Code selection available")
        
        return "; ".join(summary_parts) if summary_parts else "No specific context available."
    
    def _calculate_relevance_score(self, item_type: str, content: str, metadata: Dict[str, Any]) -> float:
        """Calculate initial relevance score for a context item."""
        base_scores = {
            "file": 0.8,
            "selection": 0.9,
            "message": 0.7,
            "code_block": 0.8,
            "symbol": 0.6,
            "error": 0.9
        }
        
        base_score = base_scores.get(item_type, 0.5)
        
        # Adjust based on content length (longer content might be more relevant)
        length_factor = min(1.0, len(content) / 500)
        
        # Adjust based on metadata
        metadata_bonus = 0.0
        if metadata.get("language") in ["python", "javascript", "typescript"]:
            metadata_bonus += 0.1
        if metadata.get("cursor_line") is not None:
            metadata_bonus += 0.1
        
        return min(1.0, base_score + length_factor * 0.2 + metadata_bonus)
    
    def _calculate_query_relevance(self, item: ContextItem, query: str) -> float:
        """Calculate how relevant an item is to a specific query."""
        query_lower = query.lower()
        content_lower = item.content.lower()
        
        # Simple keyword matching
        query_words = set(query_lower.split())
        content_words = set(content_lower.split())
        
        if not query_words:
            return 0.0
        
        # Calculate word overlap
        overlap = len(query_words.intersection(content_words))
        relevance = overlap / len(query_words)
        
        # Boost for exact phrase matches
        if query_lower in content_lower:
            relevance += 0.3
        
        # Boost for code-related queries
        code_keywords = ["function", "class", "variable", "error", "bug", "fix"]
        if any(keyword in query_lower for keyword in code_keywords):
            if item.type in ["code_block", "selection", "file"]:
                relevance += 0.2
        
        return min(1.0, relevance)
    
    def _calculate_time_relevance(self, item: ContextItem) -> float:
        """Calculate time-based relevance (more recent = more relevant)."""
        now = datetime.now()
        age_hours = (now - item.timestamp).total_seconds() / 3600
        
        # Exponential decay
        time_relevance = self.relevance_decay_factor ** age_hours
        
        # Boost for recently accessed items
        if item.last_accessed:
            access_age_hours = (now - item.last_accessed).total_seconds() / 3600
            if access_age_hours < 1:  # Accessed within last hour
                time_relevance += 0.2
        
        return min(1.0, time_relevance)
    
    def _extract_code_blocks(self, text: str) -> List[str]:
        """Extract code blocks from text (markdown format)."""
        import re
        
        # Match code blocks with triple backticks
        pattern = r'```(?:\w+)?\n(.*?)\n```'
        matches = re.findall(pattern, text, re.DOTALL)
        
        # Also match inline code
        inline_pattern = r'`([^`]+)`'
        inline_matches = re.findall(inline_pattern, text)
        
        # Filter out very short inline code
        inline_matches = [match for match in inline_matches if len(match) > 10]
        
        return matches + inline_matches
    
    def _cleanup_old_items(self):
        """Clean up old context items to prevent memory bloat."""
        if len(self.context_items) <= self.max_context_items:
            return
        
        # Sort by timestamp and remove oldest items
        items_by_age = sorted(
            self.context_items.items(),
            key=lambda x: x[1].timestamp
        )
        
        # Remove oldest 20% of items
        items_to_remove = len(self.context_items) - self.max_context_items
        for i in range(items_to_remove):
            item_id, _ = items_by_age[i]
            del self.context_items[item_id]
        
        # Also clean up session context windows
        for session_id, context_window in self.session_contexts.items():
            context_window.items = [
                item for item in context_window.items 
                if item.id in self.context_items
            ]
        
        logger.info(f"Cleaned up {items_to_remove} old context items")
    
    def clear_session_context(self, session_id: str):
        """Clear all context for a specific session."""
        if session_id in self.session_contexts:
            del self.session_contexts[session_id]
            logger.info(f"Cleared context for session {session_id}")
    
    def get_context_stats(self, session_id: str) -> Dict[str, Any]:
        """Get statistics about the context for a session."""
        if session_id not in self.session_contexts:
            return {"error": "Session not found"}
        
        context_window = self.session_contexts[session_id]
        
        # Count items by type
        type_counts = defaultdict(int)
        for item in context_window.items:
            type_counts[item.type] += 1
        
        return {
            "total_items": len(context_window.items),
            "total_relevance": context_window.total_relevance,
            "average_relevance": context_window.total_relevance / len(context_window.items) if context_window.items else 0,
            "items_by_type": dict(type_counts),
            "window_size": context_window.max_size
        }


# Global conversation context manager
conversation_context = ConversationContextManager()
