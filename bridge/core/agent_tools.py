"""
AI Agent Tools for enhanced coding capabilities.
Provides file operations, terminal execution, workspace navigation tools, and SWE-Agent tool integration.
"""

import asyncio
import json
import logging
import os
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ToolResult:
    """Result of a tool execution."""
    success: bool
    data: Any = None
    error: str = None
    metadata: Dict[str, Any] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            'success': self.success,
            'data': self.data,
            'error': self.error,
            'metadata': self.metadata or {}
        }


class AgentTools:
    """
    Collection of tools available to the AI agent for enhanced coding capabilities.
    """
    
    def __init__(self):
        self.tools = {}
        self._register_tools()
    
    def _register_tools(self):
        """Register all available tools."""
        # File operation tools
        self.tools['read_file'] = {
            'function': self.read_file,
            'description': 'Read the contents of a file',
            'parameters': {
                'file_path': 'Path to the file to read',
                'encoding': 'File encoding (optional, defaults to utf-8)'
            }
        }
        
        self.tools['write_file'] = {
            'function': self.write_file,
            'description': 'Write content to a file',
            'parameters': {
                'file_path': 'Path to the file to write',
                'content': 'Content to write to the file',
                'encoding': 'File encoding (optional, defaults to utf-8)',
                'create_dirs': 'Create parent directories if needed (optional)'
            }
        }
        
        self.tools['create_file'] = {
            'function': self.create_file,
            'description': 'Create a new file with optional content',
            'parameters': {
                'file_path': 'Path to the new file',
                'content': 'Initial content (optional)',
                'encoding': 'File encoding (optional, defaults to utf-8)',
                'create_dirs': 'Create parent directories if needed (optional)'
            }
        }
        
        self.tools['delete_file'] = {
            'function': self.delete_file,
            'description': 'Delete a file or directory',
            'parameters': {
                'file_path': 'Path to the file or directory to delete',
                'recursive': 'Delete directories recursively (optional)'
            }
        }
        
        self.tools['list_directory'] = {
            'function': self.list_directory,
            'description': 'List contents of a directory',
            'parameters': {
                'directory_path': 'Path to the directory',
                'include_hidden': 'Include hidden files (optional)',
                'recursive': 'Recursive listing (optional)',
                'max_depth': 'Maximum recursion depth (optional)'
            }
        }
        
        # Terminal operation tools
        self.tools['execute_command'] = {
            'function': self.execute_command,
            'description': 'Execute a shell command',
            'parameters': {
                'command': 'Command to execute',
                'working_directory': 'Working directory (optional)',
                'timeout': 'Timeout in seconds (optional)',
                'capture_output': 'Whether to capture output (optional)'
            }
        }
        
        self.tools['create_terminal_session'] = {
            'function': self.create_terminal_session,
            'description': 'Create a persistent terminal session',
            'parameters': {
                'working_directory': 'Working directory (optional)',
                'session_name': 'Session name (optional)'
            }
        }
        
        self.tools['send_terminal_input'] = {
            'function': self.send_terminal_input,
            'description': 'Send input to a terminal session',
            'parameters': {
                'session_id': 'Terminal session ID',
                'input': 'Input to send'
            }
        }
        
        self.tools['get_terminal_output'] = {
            'function': self.get_terminal_output,
            'description': 'Get output from a terminal session',
            'parameters': {
                'session_id': 'Terminal session ID',
                'since_timestamp': 'Get output since timestamp (optional)',
                'format': 'Output format (json or text, optional)'
            }
        }
        
        # Workspace navigation tools
        self.tools['search_files'] = {
            'function': self.search_files,
            'description': 'Search for files in the workspace',
            'parameters': {
                'project_path': 'Path to the project root',
                'query': 'Search query',
                'max_results': 'Maximum number of results (optional)'
            }
        }
        
        self.tools['analyze_project'] = {
            'function': self.analyze_project,
            'description': 'Analyze project structure and dependencies',
            'parameters': {
                'project_path': 'Path to the project root',
                'force_refresh': 'Force refresh of analysis (optional)'
            }
        }
        
        self.tools['get_file_outline'] = {
            'function': self.get_file_outline,
            'description': 'Get outline/symbols of a file',
            'parameters': {
                'project_path': 'Path to the project root',
                'file_path': 'Relative path to the file'
            }
        }

        # SWE-Agent tool integration
        self.tools['execute_swe_tool'] = {
            'function': self.execute_swe_tool,
            'description': 'Execute a SWE-Agent tool with given parameters',
            'parameters': {
                'tool_name': 'Name of the SWE-Agent tool to execute',
                'parameters': 'Parameters for the tool (dict)',
                'working_directory': 'Working directory for execution (optional)',
                'timeout': 'Execution timeout in seconds (optional)'
            }
        }

        self.tools['list_swe_tools'] = {
            'function': self.list_swe_tools,
            'description': 'List all available SWE-Agent tools',
            'parameters': {}
        }

        self.tools['get_swe_tool_schema'] = {
            'function': self.get_swe_tool_schema,
            'description': 'Get OpenAI function calling schema for a SWE-Agent tool',
            'parameters': {
                'tool_name': 'Name of the SWE-Agent tool'
            }
        }

        self.tools['validate_swe_tool_parameters'] = {
            'function': self.validate_swe_tool_parameters,
            'description': 'Validate SWE-Agent tool parameters',
            'parameters': {
                'tool_name': 'Name of the SWE-Agent tool',
                'parameters': 'Parameters to validate (dict)'
            }
        }
    
    def get_available_tools(self) -> Dict[str, Dict[str, Any]]:
        """Get list of available tools with their descriptions."""
        return {name: {
            'description': tool['description'],
            'parameters': tool['parameters']
        } for name, tool in self.tools.items()}
    
    def execute_tool(self, tool_name: str, **kwargs) -> ToolResult:
        """Execute a tool with given parameters."""
        if tool_name not in self.tools:
            return ToolResult(
                success=False,
                error=f"Tool '{tool_name}' not found"
            )
        
        try:
            tool_function = self.tools[tool_name]['function']
            result = tool_function(**kwargs)
            return result
        except Exception as e:
            logger.error(f"Error executing tool '{tool_name}': {e}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    # File operation tool implementations
    
    def read_file(self, file_path: str, encoding: str = 'utf-8') -> ToolResult:
        """Read file content."""
        try:
            from bridge.api.file_endpoints import _is_safe_path
            
            file_path = os.path.abspath(file_path)
            if not _is_safe_path(file_path):
                return ToolResult(success=False, error="Access denied to this path")
            
            if not os.path.exists(file_path):
                return ToolResult(success=False, error="File not found")
            
            if not os.path.isfile(file_path):
                return ToolResult(success=False, error="Path is not a file")
            
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            stat = os.stat(file_path)
            
            return ToolResult(
                success=True,
                data={
                    'content': content,
                    'file_path': file_path,
                    'size': stat.st_size,
                    'modified': stat.st_mtime,
                    'encoding': encoding
                }
            )
            
        except Exception as e:
            return ToolResult(success=False, error=str(e))
    
    def write_file(self, file_path: str, content: str, encoding: str = 'utf-8', create_dirs: bool = False) -> ToolResult:
        """Write content to a file."""
        try:
            from bridge.api.file_endpoints import _is_safe_path
            
            file_path = os.path.abspath(file_path)
            if not _is_safe_path(file_path):
                return ToolResult(success=False, error="Access denied to this path")
            
            if create_dirs:
                parent_dir = os.path.dirname(file_path)
                if parent_dir and not os.path.exists(parent_dir):
                    os.makedirs(parent_dir, exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            stat = os.stat(file_path)
            
            return ToolResult(
                success=True,
                data={
                    'file_path': file_path,
                    'size': stat.st_size,
                    'modified': stat.st_mtime,
                    'message': 'File written successfully'
                }
            )
            
        except Exception as e:
            return ToolResult(success=False, error=str(e))
    
    def create_file(self, file_path: str, content: str = '', encoding: str = 'utf-8', create_dirs: bool = False) -> ToolResult:
        """Create a new file."""
        try:
            from bridge.api.file_endpoints import _is_safe_path
            
            file_path = os.path.abspath(file_path)
            if not _is_safe_path(file_path):
                return ToolResult(success=False, error="Access denied to this path")
            
            if os.path.exists(file_path):
                return ToolResult(success=False, error="File already exists")
            
            if create_dirs:
                parent_dir = os.path.dirname(file_path)
                if parent_dir and not os.path.exists(parent_dir):
                    os.makedirs(parent_dir, exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            stat = os.stat(file_path)
            
            return ToolResult(
                success=True,
                data={
                    'file_path': file_path,
                    'size': stat.st_size,
                    'modified': stat.st_mtime,
                    'message': 'File created successfully'
                }
            )
            
        except Exception as e:
            return ToolResult(success=False, error=str(e))
    
    def delete_file(self, file_path: str, recursive: bool = False) -> ToolResult:
        """Delete a file or directory."""
        try:
            import shutil
            from bridge.api.file_endpoints import _is_safe_path
            
            file_path = os.path.abspath(file_path)
            if not _is_safe_path(file_path):
                return ToolResult(success=False, error="Access denied to this path")
            
            if not os.path.exists(file_path):
                return ToolResult(success=False, error="File or directory not found")
            
            if os.path.isfile(file_path):
                os.remove(file_path)
                message = "File deleted successfully"
            elif os.path.isdir(file_path):
                if recursive:
                    shutil.rmtree(file_path)
                    message = "Directory deleted successfully"
                else:
                    os.rmdir(file_path)
                    message = "Directory deleted successfully"
            else:
                return ToolResult(success=False, error="Path is neither a file nor a directory")
            
            return ToolResult(
                success=True,
                data={
                    'file_path': file_path,
                    'message': message
                }
            )
            
        except Exception as e:
            return ToolResult(success=False, error=str(e))

    def list_directory(self, directory_path: str, include_hidden: bool = False, recursive: bool = False, max_depth: int = 3) -> ToolResult:
        """List directory contents."""
        try:
            from bridge.api.file_endpoints import _is_safe_path

            directory_path = os.path.abspath(directory_path)
            if not _is_safe_path(directory_path):
                return ToolResult(success=False, error="Access denied to this path")

            if not os.path.exists(directory_path):
                return ToolResult(success=False, error="Directory not found")

            if not os.path.isdir(directory_path):
                return ToolResult(success=False, error="Path is not a directory")

            def _list_recursive(path: str, current_depth: int = 0) -> List[Dict[str, Any]]:
                items = []

                if current_depth >= max_depth:
                    return items

                try:
                    for item_name in os.listdir(path):
                        if not include_hidden and item_name.startswith('.'):
                            continue

                        item_path = os.path.join(path, item_name)

                        try:
                            stat = os.stat(item_path)
                            is_dir = os.path.isdir(item_path)

                            item_info = {
                                "name": item_name,
                                "path": item_path,
                                "type": "directory" if is_dir else "file",
                                "size": stat.st_size if not is_dir else None,
                                "modified": stat.st_mtime,
                                "permissions": oct(stat.st_mode)[-3:]
                            }

                            if recursive and is_dir:
                                item_info["children"] = _list_recursive(item_path, current_depth + 1)

                            items.append(item_info)

                        except (PermissionError, OSError):
                            continue

                except PermissionError:
                    pass

                return sorted(items, key=lambda x: (x["type"] == "file", x["name"].lower()))

            items = _list_recursive(directory_path)

            return ToolResult(
                success=True,
                data={
                    'directory_path': directory_path,
                    'items': items,
                    'count': len(items)
                }
            )

        except Exception as e:
            return ToolResult(success=False, error=str(e))

    # Terminal operation tool implementations

    def execute_command(self, command: str, working_directory: str = None, timeout: int = 30, capture_output: bool = True) -> ToolResult:
        """Execute a shell command."""
        try:
            import subprocess
            import time
            from bridge.api.terminal_endpoints import _is_safe_command

            if not _is_safe_command(command):
                return ToolResult(success=False, error="Command not allowed for security reasons")

            working_directory = working_directory or os.getcwd()
            start_time = time.time()

            if capture_output:
                result = subprocess.run(
                    command,
                    shell=True,
                    cwd=working_directory,
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )

                execution_time = time.time() - start_time

                return ToolResult(
                    success=True,
                    data={
                        'command': command,
                        'return_code': result.returncode,
                        'stdout': result.stdout,
                        'stderr': result.stderr,
                        'execution_time': execution_time,
                        'working_directory': working_directory
                    }
                )
            else:
                process = subprocess.Popen(
                    command,
                    shell=True,
                    cwd=working_directory
                )

                return ToolResult(
                    success=True,
                    data={
                        'command': command,
                        'process_id': process.pid,
                        'message': 'Command started (output not captured)',
                        'working_directory': working_directory
                    }
                )

        except subprocess.TimeoutExpired:
            return ToolResult(success=False, error=f"Command timed out after {timeout} seconds")
        except Exception as e:
            return ToolResult(success=False, error=str(e))

    def create_terminal_session(self, working_directory: str = None, session_name: str = None) -> ToolResult:
        """Create a persistent terminal session."""
        try:
            import requests
            from bridge.core.config import config

            host = config.get("bridge", "api_host", default="localhost")
            port = config.get("bridge", "api_port", default=8080)
            base_url = f"http://{host}:{port}"

            response = requests.post(f"{base_url}/api/terminal/sessions", json={
                'working_directory': working_directory,
                'session_name': session_name
            })

            if response.status_code == 200:
                data = response.json()
                return ToolResult(success=True, data=data)
            else:
                return ToolResult(success=False, error=f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            return ToolResult(success=False, error=str(e))

    def send_terminal_input(self, session_id: str, input: str) -> ToolResult:
        """Send input to a terminal session."""
        try:
            import requests
            from bridge.core.config import config

            host = config.get("bridge", "api_host", default="localhost")
            port = config.get("bridge", "api_port", default=8080)
            base_url = f"http://{host}:{port}"

            response = requests.post(f"{base_url}/api/terminal/sessions/{session_id}/input", json={
                'input': input
            })

            if response.status_code == 200:
                data = response.json()
                return ToolResult(success=True, data=data)
            else:
                return ToolResult(success=False, error=f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            return ToolResult(success=False, error=str(e))

    def get_terminal_output(self, session_id: str, since_timestamp: float = None, format: str = 'json') -> ToolResult:
        """Get output from a terminal session."""
        try:
            import requests
            from bridge.core.config import config

            host = config.get("bridge", "api_host", default="localhost")
            port = config.get("bridge", "api_port", default=8080)
            base_url = f"http://{host}:{port}"

            params = {}
            if since_timestamp:
                params['since'] = str(since_timestamp)
            if format:
                params['format'] = format

            response = requests.get(f"{base_url}/api/terminal/sessions/{session_id}/output", params=params)

            if response.status_code == 200:
                if format == 'text':
                    return ToolResult(success=True, data={'output': response.text})
                else:
                    data = response.json()
                    return ToolResult(success=True, data=data)
            else:
                return ToolResult(success=False, error=f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            return ToolResult(success=False, error=str(e))

    # Workspace navigation tool implementations

    def search_files(self, project_path: str, query: str, max_results: int = 20) -> ToolResult:
        """Search for files in the workspace."""
        try:
            import requests
            from bridge.core.config import config

            host = config.get("bridge", "api_host", default="localhost")
            port = config.get("bridge", "api_port", default=8080)
            base_url = f"http://{host}:{port}"

            response = requests.post(f"{base_url}/api/workspace/navigation/search/files", json={
                'project_path': project_path,
                'query': query,
                'max_results': max_results
            })

            if response.status_code == 200:
                data = response.json()
                return ToolResult(success=True, data=data)
            else:
                return ToolResult(success=False, error=f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            return ToolResult(success=False, error=str(e))

    def analyze_project(self, project_path: str, force_refresh: bool = False) -> ToolResult:
        """Analyze project structure and dependencies."""
        try:
            import requests
            from bridge.core.config import config

            host = config.get("bridge", "api_host", default="localhost")
            port = config.get("bridge", "api_port", default=8080)
            base_url = f"http://{host}:{port}"

            response = requests.post(f"{base_url}/api/workspace/analyze", json={
                'project_path': project_path,
                'force_refresh': force_refresh
            })

            if response.status_code == 200:
                data = response.json()
                return ToolResult(success=True, data=data)
            else:
                return ToolResult(success=False, error=f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            return ToolResult(success=False, error=str(e))

    def get_file_outline(self, project_path: str, file_path: str) -> ToolResult:
        """Get outline/symbols of a file."""
        try:
            import requests
            from bridge.core.config import config

            host = config.get("bridge", "api_host", default="localhost")
            port = config.get("bridge", "api_port", default=8080)
            base_url = f"http://{host}:{port}"

            response = requests.post(f"{base_url}/api/workspace/outline", json={
                'project_path': project_path,
                'file_path': file_path
            })

            if response.status_code == 200:
                data = response.json()
                return ToolResult(success=True, data=data)
            else:
                return ToolResult(success=False, error=f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            return ToolResult(success=False, error=str(e))

    # SWE-Agent tool integration methods

    def execute_swe_tool(self, tool_name: str, parameters: Dict[str, Any],
                        working_directory: str = None, timeout: int = 30) -> ToolResult:
        """Execute a SWE-Agent tool with given parameters."""
        try:
            import requests
            from bridge.core.config import config

            host = config.get("bridge", "api_host", default="localhost")
            port = config.get("bridge", "api_port", default=8080)
            base_url = f"http://{host}:{port}"

            response = requests.post(f"{base_url}/api/swe-tools/execute", json={
                'tool_name': tool_name,
                'parameters': parameters,
                'working_directory': working_directory,
                'timeout': timeout
            })

            if response.status_code == 200:
                data = response.json()
                return ToolResult(
                    success=data.get('success', False),
                    data=data.get('output', ''),
                    error=data.get('error', '')
                )
            else:
                return ToolResult(success=False, error=f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            return ToolResult(success=False, error=str(e))

    def list_swe_tools(self) -> ToolResult:
        """List all available SWE-Agent tools."""
        try:
            import requests
            from bridge.core.config import config

            host = config.get("bridge", "api_host", default="localhost")
            port = config.get("bridge", "api_port", default=8080)
            base_url = f"http://{host}:{port}"

            response = requests.get(f"{base_url}/api/swe-tools/list")

            if response.status_code == 200:
                data = response.json()
                return ToolResult(success=True, data=data)
            else:
                return ToolResult(success=False, error=f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            return ToolResult(success=False, error=str(e))

    def get_swe_tool_schema(self, tool_name: str) -> ToolResult:
        """Get OpenAI function calling schema for a SWE-Agent tool."""
        try:
            import requests
            from bridge.core.config import config

            host = config.get("bridge", "api_host", default="localhost")
            port = config.get("bridge", "api_port", default=8080)
            base_url = f"http://{host}:{port}"

            response = requests.get(f"{base_url}/api/swe-tools/{tool_name}/schema")

            if response.status_code == 200:
                data = response.json()
                return ToolResult(success=True, data=data)
            else:
                return ToolResult(success=False, error=f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            return ToolResult(success=False, error=str(e))

    def validate_swe_tool_parameters(self, tool_name: str, parameters: Dict[str, Any]) -> ToolResult:
        """Validate SWE-Agent tool parameters."""
        try:
            import requests
            from bridge.core.config import config

            host = config.get("bridge", "api_host", default="localhost")
            port = config.get("bridge", "api_port", default=8080)
            base_url = f"http://{host}:{port}"

            response = requests.post(f"{base_url}/api/swe-tools/validate", json={
                'tool_name': tool_name,
                'parameters': parameters
            })

            if response.status_code == 200:
                data = response.json()
                return ToolResult(success=True, data=data)
            else:
                data = response.json() if response.headers.get('content-type', '').startswith('application/json') else {}
                return ToolResult(success=False, error=data.get('summary', response.text), data=data)

        except Exception as e:
            return ToolResult(success=False, error=str(e))


# Global instance
agent_tools = AgentTools()
