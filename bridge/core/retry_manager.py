"""
Enhanced retry mechanisms for the AI Coding Agent bridge.
Provides exponential backoff, circuit breaker, and intelligent retry strategies.
"""

import time
import random
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable, Type
from dataclasses import dataclass, field
from enum import Enum
import asyncio
from functools import wraps

logger = logging.getLogger(__name__)


class RetryReason(Enum):
    """Reasons for retry attempts."""
    TIMEOUT = "timeout"
    CONNECTION_ERROR = "connection_error"
    RATE_LIMIT = "rate_limit"
    SERVER_ERROR = "server_error"
    API_ERROR = "api_error"
    RESOURCE_EXHAUSTED = "resource_exhausted"
    TEMPORARY_FAILURE = "temporary_failure"
    UNKNOWN_ERROR = "unknown_error"


@dataclass
class RetryAttempt:
    """Represents a single retry attempt."""
    attempt_number: int
    timestamp: str
    reason: RetryReason
    delay: float
    error_message: str
    success: bool = False


@dataclass
class RetryStats:
    """Statistics for retry operations."""
    total_attempts: int = 0
    successful_attempts: int = 0
    failed_attempts: int = 0
    total_delay: float = 0.0
    reasons: Dict[str, int] = field(default_factory=dict)
    last_attempt: Optional[str] = None
    
    def add_attempt(self, attempt: RetryAttempt):
        """Add an attempt to the statistics."""
        self.total_attempts += 1
        self.total_delay += attempt.delay
        self.last_attempt = attempt.timestamp
        
        if attempt.success:
            self.successful_attempts += 1
        else:
            self.failed_attempts += 1
        
        reason_key = attempt.reason.value
        self.reasons[reason_key] = self.reasons.get(reason_key, 0) + 1


class CircuitBreaker:
    """Circuit breaker implementation for preventing cascading failures."""
    
    def __init__(self, 
                 failure_threshold: int = 5,
                 timeout: int = 60,
                 expected_exception: Type[Exception] = Exception):
        """
        Initialize circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening circuit.
            timeout: Time to wait before trying again (seconds).
            expected_exception: Exception type to catch.
        """
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func: Callable, *args, **kwargs):
        """
        Call function with circuit breaker protection.
        
        Args:
            func: Function to call.
            *args: Function arguments.
            **kwargs: Function keyword arguments.
            
        Returns:
            Function result.
            
        Raises:
            Exception: If circuit is open or function fails.
        """
        if self.state == "OPEN":
            if self._should_attempt_reset():
                self.state = "HALF_OPEN"
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        if self.last_failure_time is None:
            return True
        
        return time.time() - self.last_failure_time >= self.timeout
    
    def _on_success(self):
        """Handle successful call."""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def _on_failure(self):
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"


class RetryManager:
    """Enhanced retry manager with multiple strategies."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize retry manager.
        
        Args:
            config: Retry configuration.
        """
        self.max_attempts = config.get('max_attempts', 3)
        self.backoff_factor = config.get('backoff_factor', 2.0)
        self.max_delay = config.get('max_delay', 300)
        self.min_delay = config.get('min_delay', 1)
        self.use_jitter = config.get('use_jitter', True)
        self.jitter_factor = config.get('jitter_factor', 0.1)
        
        # Circuit breaker configuration
        self.circuit_breaker_enabled = config.get('circuit_breaker_enabled', False)
        self.circuit_breaker_threshold = config.get('circuit_breaker_threshold', 5)
        self.circuit_breaker_timeout = config.get('circuit_breaker_timeout', 60)
        
        # Retry conditions
        self.retry_on_errors = config.get('retry_on_errors', [
            'timeout', 'connection_error', 'rate_limit', 'server_error'
        ])
        
        # Statistics
        self.stats = RetryStats()
        self.attempts_history: List[RetryAttempt] = []
        
        # Circuit breaker
        self.circuit_breaker = None
        if self.circuit_breaker_enabled:
            self.circuit_breaker = CircuitBreaker(
                failure_threshold=self.circuit_breaker_threshold,
                timeout=self.circuit_breaker_timeout
            )
    
    def retry(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with retry logic.
        
        Args:
            func: Function to execute.
            *args: Function arguments.
            **kwargs: Function keyword arguments.
            
        Returns:
            Function result.
            
        Raises:
            Exception: If all retry attempts fail.
        """
        last_exception = None
        
        for attempt in range(1, self.max_attempts + 1):
            try:
                # Use circuit breaker if enabled
                if self.circuit_breaker:
                    result = self.circuit_breaker.call(func, *args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Record successful attempt
                attempt_record = RetryAttempt(
                    attempt_number=attempt,
                    timestamp=datetime.now().isoformat(),
                    reason=RetryReason.UNKNOWN_ERROR,
                    delay=0.0,
                    error_message="",
                    success=True
                )
                self._record_attempt(attempt_record)
                
                return result
                
            except Exception as e:
                last_exception = e
                reason = self._classify_error(e)
                
                # Check if we should retry this error
                if reason.value not in self.retry_on_errors:
                    logger.info(f"Not retrying error type: {reason.value}")
                    break
                
                # Calculate delay for next attempt
                if attempt < self.max_attempts:
                    delay = self._calculate_delay(attempt)
                    
                    # Record failed attempt
                    attempt_record = RetryAttempt(
                        attempt_number=attempt,
                        timestamp=datetime.now().isoformat(),
                        reason=reason,
                        delay=delay,
                        error_message=str(e),
                        success=False
                    )
                    self._record_attempt(attempt_record)
                    
                    logger.warning(f"Attempt {attempt} failed: {e}. Retrying in {delay:.2f}s")
                    time.sleep(delay)
                else:
                    # Record final failed attempt
                    attempt_record = RetryAttempt(
                        attempt_number=attempt,
                        timestamp=datetime.now().isoformat(),
                        reason=reason,
                        delay=0.0,
                        error_message=str(e),
                        success=False
                    )
                    self._record_attempt(attempt_record)
        
        # All attempts failed
        logger.error(f"All {self.max_attempts} retry attempts failed")
        raise last_exception
    
    async def async_retry(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute async function with retry logic.
        
        Args:
            func: Async function to execute.
            *args: Function arguments.
            **kwargs: Function keyword arguments.
            
        Returns:
            Function result.
            
        Raises:
            Exception: If all retry attempts fail.
        """
        last_exception = None
        
        for attempt in range(1, self.max_attempts + 1):
            try:
                result = await func(*args, **kwargs)
                
                # Record successful attempt
                attempt_record = RetryAttempt(
                    attempt_number=attempt,
                    timestamp=datetime.now().isoformat(),
                    reason=RetryReason.UNKNOWN_ERROR,
                    delay=0.0,
                    error_message="",
                    success=True
                )
                self._record_attempt(attempt_record)
                
                return result
                
            except Exception as e:
                last_exception = e
                reason = self._classify_error(e)
                
                # Check if we should retry this error
                if reason.value not in self.retry_on_errors:
                    logger.info(f"Not retrying error type: {reason.value}")
                    break
                
                # Calculate delay for next attempt
                if attempt < self.max_attempts:
                    delay = self._calculate_delay(attempt)
                    
                    # Record failed attempt
                    attempt_record = RetryAttempt(
                        attempt_number=attempt,
                        timestamp=datetime.now().isoformat(),
                        reason=reason,
                        delay=delay,
                        error_message=str(e),
                        success=False
                    )
                    self._record_attempt(attempt_record)
                    
                    logger.warning(f"Attempt {attempt} failed: {e}. Retrying in {delay:.2f}s")
                    await asyncio.sleep(delay)
                else:
                    # Record final failed attempt
                    attempt_record = RetryAttempt(
                        attempt_number=attempt,
                        timestamp=datetime.now().isoformat(),
                        reason=reason,
                        delay=0.0,
                        error_message=str(e),
                        success=False
                    )
                    self._record_attempt(attempt_record)
        
        # All attempts failed
        logger.error(f"All {self.max_attempts} retry attempts failed")
        raise last_exception
    
    def _calculate_delay(self, attempt: int) -> float:
        """
        Calculate delay for retry attempt using exponential backoff.
        
        Args:
            attempt: Current attempt number.
            
        Returns:
            Delay in seconds.
        """
        # Exponential backoff
        delay = self.min_delay * (self.backoff_factor ** (attempt - 1))
        
        # Apply maximum delay limit
        delay = min(delay, self.max_delay)
        
        # Add jitter to prevent thundering herd
        if self.use_jitter:
            jitter = delay * self.jitter_factor * random.random()
            delay += jitter
        
        return delay
    
    def _classify_error(self, error: Exception) -> RetryReason:
        """
        Classify error to determine retry strategy.
        
        Args:
            error: Exception to classify.
            
        Returns:
            Retry reason.
        """
        error_str = str(error).lower()
        error_type = type(error).__name__.lower()
        
        if 'timeout' in error_str or 'timeout' in error_type:
            return RetryReason.TIMEOUT
        elif 'connection' in error_str or 'connection' in error_type:
            return RetryReason.CONNECTION_ERROR
        elif 'rate limit' in error_str or 'ratelimit' in error_str:
            return RetryReason.RATE_LIMIT
        elif 'server error' in error_str or '5' in error_str[:3]:
            return RetryReason.SERVER_ERROR
        elif 'api' in error_str and 'error' in error_str:
            return RetryReason.API_ERROR
        elif 'resource' in error_str and 'exhausted' in error_str:
            return RetryReason.RESOURCE_EXHAUSTED
        else:
            return RetryReason.UNKNOWN_ERROR
    
    def _record_attempt(self, attempt: RetryAttempt):
        """Record retry attempt for statistics."""
        self.attempts_history.append(attempt)
        self.stats.add_attempt(attempt)
        
        # Keep only recent attempts (last 100)
        if len(self.attempts_history) > 100:
            self.attempts_history = self.attempts_history[-100:]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get retry statistics."""
        return {
            'total_attempts': self.stats.total_attempts,
            'successful_attempts': self.stats.successful_attempts,
            'failed_attempts': self.stats.failed_attempts,
            'success_rate': self.stats.successful_attempts / max(self.stats.total_attempts, 1),
            'total_delay': self.stats.total_delay,
            'avg_delay': self.stats.total_delay / max(self.stats.failed_attempts, 1),
            'reasons': self.stats.reasons,
            'last_attempt': self.stats.last_attempt,
            'circuit_breaker_state': self.circuit_breaker.state if self.circuit_breaker else None
        }
    
    def reset_stats(self):
        """Reset retry statistics."""
        self.stats = RetryStats()
        self.attempts_history = []
        if self.circuit_breaker:
            self.circuit_breaker.failure_count = 0
            self.circuit_breaker.state = "CLOSED"


def retry_decorator(retry_config: Dict[str, Any]):
    """
    Decorator for adding retry logic to functions.
    
    Args:
        retry_config: Retry configuration.
        
    Returns:
        Decorated function.
    """
    def decorator(func):
        retry_manager = RetryManager(retry_config)
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            return retry_manager.retry(func, *args, **kwargs)
        
        return wrapper
    return decorator


def async_retry_decorator(retry_config: Dict[str, Any]):
    """
    Decorator for adding retry logic to async functions.
    
    Args:
        retry_config: Retry configuration.
        
    Returns:
        Decorated async function.
    """
    def decorator(func):
        retry_manager = RetryManager(retry_config)
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await retry_manager.async_retry(func, *args, **kwargs)
        
        return wrapper
    return decorator
