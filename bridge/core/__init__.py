"""
Core components for the AI Coding Agent bridge.

This package contains the core session management and configuration systems.
"""

from .session_manager import session_manager, SessionManager, SessionConfig, SessionStatus, Session
from .enhanced_config import enhanced_config, EnhancedConfig, EXAMPLE_CONFIGS
from .config import config, Config

# Phase 2 components
from .advanced_swe_config import (
    AdvancedSWEConfig,
    AdvancedAgentConfig,
    AdvancedModelConfig,
    AdvancedToolConfig,
    AdvancedDeploymentConfig,
    AdvancedRepositoryConfig,
    AdvancedRetryConfig,
    ADVANCED_CONFIG_TEMPLATES,
    DeploymentType,
    RepositoryType
)
from .trajectory_manager import (
    trajectory_manager,
    TrajectoryManager,
    Trajectory,
    TrajectoryStep,
    TrajectoryMetadata,
    TrajectoryFormat
)
from .retry_manager import (
    RetryManager,
    CircuitBreaker,
    RetryReason,
    retry_decorator,
    async_retry_decorator
)

__all__ = [
    # Phase 1 components
    'session_manager',
    'SessionManager',
    'SessionConfig',
    'SessionStatus',
    'Session',
    'enhanced_config',
    'EnhancedConfig',
    'EXAMPLE_CONFIGS',
    'config',
    'Config',

    # Phase 2 components
    'AdvancedSWEConfig',
    'AdvancedAgentConfig',
    'AdvancedModelConfig',
    'AdvancedToolConfig',
    'AdvancedDeploymentConfig',
    'AdvancedRepositoryConfig',
    'AdvancedRetryConfig',
    'ADVANCED_CONFIG_TEMPLATES',
    'DeploymentType',
    'RepositoryType',
    'trajectory_manager',
    'TrajectoryManager',
    'Trajectory',
    'TrajectoryStep',
    'TrajectoryMetadata',
    'TrajectoryFormat',
    'RetryManager',
    'CircuitBreaker',
    'RetryReason',
    'retry_decorator',
    'async_retry_decorator'
]
