"""
Security filtering and validation for SWE-Agent tool execution.
Provides comprehensive input validation, path security, and command filtering.
"""

import logging
import os
import re
from pathlib import Path
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class SecurityViolation:
    """Represents a security violation detected during validation."""
    violation_type: str
    message: str
    parameter: Optional[str] = None
    value: Optional[str] = None


class ToolSecurityManager:
    """Manages security filtering and validation for SWE-Agent tools."""
    
    def __init__(self):
        """Initialize the security manager with default rules."""
        self.blocked_patterns = {
            # Dangerous commands
            r'\b(rm\s+-rf|sudo|su|chmod\s+777|chown|passwd|useradd|userdel)\b',
            # Network operations
            r'\b(wget|curl|nc|netcat|ssh|scp|rsync)\b',
            # System modification
            r'\b(mount|umount|fdisk|mkfs|dd)\b',
            # Process manipulation
            r'\b(kill|killall|pkill|nohup|disown)\b',
            # File system traversal
            r'\.\./\.\.',
            # Environment manipulation
            r'\b(export|unset|env)\s+[A-Z_]+=',
        }
        
        self.allowed_directories = {
            '/tmp',
            '/workspace',
            '/testbed',
            '/root',
        }
        
        self.blocked_extensions = {
            '.exe', '.bat', '.cmd', '.com', '.scr', '.pif',
            '.dll', '.so', '.dylib'
        }
        
        # Maximum sizes for various parameters
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.max_string_length = 100000  # 100KB
        self.max_array_length = 1000
        
    def validate_tool_execution(self, tool_name: str, parameters: Dict[str, Any]) -> List[SecurityViolation]:
        """
        Validate tool execution parameters for security violations.
        
        Args:
            tool_name: Name of the tool to execute
            parameters: Tool parameters to validate
            
        Returns:
            List of security violations found
        """
        violations = []
        
        # Validate tool name
        if not self._is_safe_tool_name(tool_name):
            violations.append(SecurityViolation(
                violation_type="invalid_tool",
                message=f"Tool name '{tool_name}' is not allowed",
                parameter="tool_name",
                value=tool_name
            ))
        
        # Validate each parameter
        for param_name, param_value in parameters.items():
            param_violations = self._validate_parameter(param_name, param_value)
            violations.extend(param_violations)
        
        # Tool-specific validations
        tool_violations = self._validate_tool_specific(tool_name, parameters)
        violations.extend(tool_violations)
        
        return violations
    
    def _is_safe_tool_name(self, tool_name: str) -> bool:
        """Check if tool name is safe."""
        if not tool_name or not isinstance(tool_name, str):
            return False
        
        # Only allow alphanumeric characters, underscores, and hyphens
        if not re.match(r'^[a-zA-Z0-9_-]+$', tool_name):
            return False
        
        # Block dangerous tool names
        dangerous_tools = {
            'bash', 'sh', 'zsh', 'fish', 'csh', 'tcsh',
            'python', 'python3', 'node', 'ruby', 'perl',
            'rm', 'mv', 'cp', 'chmod', 'chown', 'sudo'
        }
        
        return tool_name not in dangerous_tools
    
    def _validate_parameter(self, param_name: str, param_value: Any) -> List[SecurityViolation]:
        """Validate a single parameter."""
        violations = []
        
        if param_value is None:
            return violations
        
        # String validation
        if isinstance(param_value, str):
            violations.extend(self._validate_string_parameter(param_name, param_value))
        
        # Array validation
        elif isinstance(param_value, (list, tuple)):
            violations.extend(self._validate_array_parameter(param_name, param_value))
        
        # Integer validation
        elif isinstance(param_value, int):
            violations.extend(self._validate_integer_parameter(param_name, param_value))
        
        return violations
    
    def _validate_string_parameter(self, param_name: str, param_value: str) -> List[SecurityViolation]:
        """Validate string parameters."""
        violations = []
        
        # Check string length
        if len(param_value) > self.max_string_length:
            violations.append(SecurityViolation(
                violation_type="string_too_long",
                message=f"Parameter '{param_name}' exceeds maximum length of {self.max_string_length}",
                parameter=param_name,
                value=f"{param_value[:100]}..."
            ))
        
        # Check for dangerous patterns
        for pattern in self.blocked_patterns:
            if re.search(pattern, param_value, re.IGNORECASE):
                violations.append(SecurityViolation(
                    violation_type="dangerous_pattern",
                    message=f"Parameter '{param_name}' contains dangerous pattern: {pattern}",
                    parameter=param_name,
                    value=param_value
                ))
        
        # Path-specific validation
        if param_name in {'path', 'file_path', 'filename', 'dir', 'directory'}:
            violations.extend(self._validate_path_parameter(param_name, param_value))
        
        return violations
    
    def _validate_path_parameter(self, param_name: str, path_value: str) -> List[SecurityViolation]:
        """Validate path parameters for security."""
        violations = []
        
        try:
            # Normalize the path
            normalized_path = os.path.normpath(path_value)
            
            # Check for path traversal
            if '..' in normalized_path:
                violations.append(SecurityViolation(
                    violation_type="path_traversal",
                    message=f"Path '{path_value}' contains path traversal sequences",
                    parameter=param_name,
                    value=path_value
                ))
            
            # Check if path is within allowed directories
            if os.path.isabs(normalized_path):
                allowed = any(
                    normalized_path.startswith(allowed_dir) 
                    for allowed_dir in self.allowed_directories
                )
                if not allowed:
                    violations.append(SecurityViolation(
                        violation_type="path_not_allowed",
                        message=f"Path '{path_value}' is not within allowed directories",
                        parameter=param_name,
                        value=path_value
                    ))
            
            # Check file extension
            path_obj = Path(normalized_path)
            if path_obj.suffix.lower() in self.blocked_extensions:
                violations.append(SecurityViolation(
                    violation_type="blocked_extension",
                    message=f"File extension '{path_obj.suffix}' is not allowed",
                    parameter=param_name,
                    value=path_value
                ))
                
        except Exception as e:
            violations.append(SecurityViolation(
                violation_type="path_validation_error",
                message=f"Error validating path '{path_value}': {str(e)}",
                parameter=param_name,
                value=path_value
            ))
        
        return violations
    
    def _validate_array_parameter(self, param_name: str, param_value: List[Any]) -> List[SecurityViolation]:
        """Validate array parameters."""
        violations = []
        
        # Check array length
        if len(param_value) > self.max_array_length:
            violations.append(SecurityViolation(
                violation_type="array_too_long",
                message=f"Array parameter '{param_name}' exceeds maximum length of {self.max_array_length}",
                parameter=param_name,
                value=f"Array with {len(param_value)} items"
            ))
        
        # Validate each item in the array
        for i, item in enumerate(param_value):
            if isinstance(item, str):
                item_violations = self._validate_string_parameter(f"{param_name}[{i}]", item)
                violations.extend(item_violations)
        
        return violations
    
    def _validate_integer_parameter(self, param_name: str, param_value: int) -> List[SecurityViolation]:
        """Validate integer parameters."""
        violations = []
        
        # Check reasonable bounds
        if param_value < -1000000 or param_value > 1000000:
            violations.append(SecurityViolation(
                violation_type="integer_out_of_bounds",
                message=f"Integer parameter '{param_name}' is out of reasonable bounds",
                parameter=param_name,
                value=str(param_value)
            ))
        
        return violations
    
    def _validate_tool_specific(self, tool_name: str, parameters: Dict[str, Any]) -> List[SecurityViolation]:
        """Perform tool-specific validation."""
        violations = []
        
        # str_replace_editor specific validation
        if tool_name == "str_replace_editor":
            violations.extend(self._validate_str_replace_editor(parameters))
        
        # bash command validation (if we ever allow it)
        elif tool_name == "bash":
            violations.extend(self._validate_bash_command(parameters))
        
        return violations
    
    def _validate_str_replace_editor(self, parameters: Dict[str, Any]) -> List[SecurityViolation]:
        """Validate str_replace_editor specific parameters."""
        violations = []
        
        command = parameters.get('command')
        if command not in {'view', 'create', 'str_replace', 'insert', 'undo_edit'}:
            violations.append(SecurityViolation(
                violation_type="invalid_command",
                message=f"Invalid str_replace_editor command: {command}",
                parameter="command",
                value=command
            ))
        
        # Validate file_text size for create command
        if command == 'create':
            file_text = parameters.get('file_text', '')
            if len(file_text) > self.max_file_size:
                violations.append(SecurityViolation(
                    violation_type="file_too_large",
                    message=f"File content exceeds maximum size of {self.max_file_size} bytes",
                    parameter="file_text",
                    value=f"Content with {len(file_text)} bytes"
                ))
        
        return violations
    
    def _validate_bash_command(self, parameters: Dict[str, Any]) -> List[SecurityViolation]:
        """Validate bash command parameters (currently blocked)."""
        violations = []
        
        # For now, we block all bash commands for security
        violations.append(SecurityViolation(
            violation_type="bash_blocked",
            message="Direct bash command execution is not allowed through tool interface",
            parameter="command",
            value=parameters.get('command', '')
        ))
        
        return violations
    
    def is_execution_allowed(self, tool_name: str, parameters: Dict[str, Any]) -> bool:
        """
        Check if tool execution is allowed.
        
        Args:
            tool_name: Name of the tool
            parameters: Tool parameters
            
        Returns:
            True if execution is allowed, False otherwise
        """
        violations = self.validate_tool_execution(tool_name, parameters)
        return len(violations) == 0
    
    def get_violation_summary(self, violations: List[SecurityViolation]) -> str:
        """Get a human-readable summary of security violations."""
        if not violations:
            return "No security violations found"
        
        summary = f"Found {len(violations)} security violation(s):\n"
        for i, violation in enumerate(violations, 1):
            summary += f"{i}. {violation.violation_type}: {violation.message}\n"
        
        return summary


# Global instance
tool_security = ToolSecurityManager()
