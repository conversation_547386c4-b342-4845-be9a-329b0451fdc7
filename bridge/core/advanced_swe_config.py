"""
Advanced SWE-Agent configuration system for Phase 2.
Provides full feature parity with SWE-Agent capabilities.
"""

import os
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field, asdict
from enum import Enum

from bridge.utils.env_loader import get_env


class DeploymentType(Enum):
    """Deployment type enumeration."""
    LOCAL = "local"
    DOCKER = "docker"
    MODAL = "modal"
    AWS = "aws"


class RepositoryType(Enum):
    """Repository type enumeration."""
    AUTO = "auto"
    LOCAL = "local"
    GITHUB = "github"
    PREEXISTING = "preexisting"


@dataclass
class ToolBundleConfig:
    """Configuration for SWE-Agent tool bundles."""
    name: str
    path: Optional[str] = None
    enabled: bool = True
    config: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AdvancedToolConfig:
    """Advanced tool configuration for SWE-Agent."""
    # Tool bundles
    bundles: List[ToolBundleConfig] = field(default_factory=lambda: [
        ToolBundleConfig("registry"),
        ToolBundleConfig("windowed"),
        ToolBundleConfig("search")
    ])
    
    # Environment variables for tools
    env_variables: Dict[str, Any] = field(default_factory=lambda: {
        "WINDOW": 100,
        "OVERLAP": 2
    })
    
    # Parse function configuration
    parse_function: Dict[str, str] = field(default_factory=lambda: {
        "type": "thought_action"
    })
    
    # Custom tool paths
    custom_tools: List[str] = field(default_factory=list)
    
    # Tool-specific configurations
    tool_configs: Dict[str, Dict[str, Any]] = field(default_factory=dict)


@dataclass
class AdvancedDeploymentConfig:
    """Advanced deployment configuration."""
    type: DeploymentType = DeploymentType.LOCAL
    
    # Docker configuration
    image: str = "python:3.11"
    dockerfile: Optional[str] = None
    build_args: Dict[str, str] = field(default_factory=dict)
    
    # Resource limits
    memory_limit: Optional[str] = None
    cpu_limit: Optional[str] = None
    gpu_enabled: bool = False
    
    # Timeout settings
    timeout: int = 3600
    startup_timeout: int = 300
    
    # Environment variables
    env_variables: Dict[str, str] = field(default_factory=dict)
    
    # Volume mounts (for Docker)
    volumes: List[str] = field(default_factory=list)
    
    # Network configuration
    network_mode: Optional[str] = None
    ports: List[str] = field(default_factory=list)


@dataclass
class AdvancedRepositoryConfig:
    """Advanced repository configuration."""
    type: RepositoryType = RepositoryType.AUTO
    
    # Repository location
    path: Optional[str] = None
    url: Optional[str] = None
    
    # Git configuration
    base_commit: str = "HEAD"
    branch: Optional[str] = None
    tag: Optional[str] = None
    
    # Clone settings
    clone_timeout: int = 300
    clone_depth: Optional[int] = None
    
    # Setup commands
    setup_commands: List[str] = field(default_factory=list)
    
    # GitHub-specific settings
    github_token: Optional[str] = None
    github_api_base: str = "https://api.github.com"
    
    # Repository metadata
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AdvancedRetryConfig:
    """Advanced retry configuration."""
    max_attempts: int = 3
    backoff_factor: float = 2.0
    max_delay: int = 300
    min_delay: int = 1
    
    # Retry conditions
    retry_on_errors: List[str] = field(default_factory=lambda: [
        "timeout", "connection_error", "rate_limit", "server_error"
    ])
    
    # Exponential backoff with jitter
    use_jitter: bool = True
    jitter_factor: float = 0.1
    
    # Circuit breaker pattern
    circuit_breaker_enabled: bool = False
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: int = 60


@dataclass
class AdvancedModelConfig:
    """Advanced model configuration."""
    model_name: str = "claude-3-opus-20240229"
    
    # Model parameters
    temperature: float = 0.0
    max_tokens: Optional[int] = None
    top_p: Optional[float] = None
    frequency_penalty: Optional[float] = None
    presence_penalty: Optional[float] = None
    
    # API configuration
    api_base: Optional[str] = None
    api_version: Optional[str] = None
    api_key: Optional[str] = None
    
    # Custom headers
    custom_headers: Dict[str, str] = field(default_factory=dict)
    
    # Rate limiting
    rate_limit_requests_per_minute: Optional[int] = None
    rate_limit_tokens_per_minute: Optional[int] = None
    
    # Fallback models
    fallback_models: List[str] = field(default_factory=list)


@dataclass
class AdvancedAgentConfig:
    """Advanced agent configuration."""
    model: AdvancedModelConfig = field(default_factory=AdvancedModelConfig)
    tools: AdvancedToolConfig = field(default_factory=AdvancedToolConfig)
    
    # Agent behavior
    max_iterations: int = 50
    max_cost: Optional[float] = None
    max_time: Optional[int] = None
    
    # Templates and prompts
    templates: Optional[str] = None
    system_prompt: Optional[str] = None
    
    # History processing
    history_processors: List[str] = field(default_factory=list)
    
    # Agent-specific settings
    agent_settings: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AdvancedSWEConfig:
    """Advanced SWE-Agent configuration with full feature parity."""
    agent: AdvancedAgentConfig = field(default_factory=AdvancedAgentConfig)
    deployment: AdvancedDeploymentConfig = field(default_factory=AdvancedDeploymentConfig)
    repository: AdvancedRepositoryConfig = field(default_factory=AdvancedRepositoryConfig)
    retry: AdvancedRetryConfig = field(default_factory=AdvancedRetryConfig)
    
    # Global settings
    workspace_dir: Optional[str] = None
    log_level: str = "INFO"
    debug_mode: bool = False
    
    # Monitoring and observability
    enable_metrics: bool = False
    metrics_endpoint: Optional[str] = None
    enable_tracing: bool = False
    
    # Security settings
    security: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AdvancedSWEConfig':
        """Create configuration from dictionary."""
        # Handle nested configurations
        agent_data = data.get('agent', {})
        if 'model' in agent_data:
            agent_data['model'] = AdvancedModelConfig(**agent_data['model'])
        if 'tools' in agent_data:
            tools_data = agent_data['tools']
            if 'bundles' in tools_data:
                tools_data['bundles'] = [
                    ToolBundleConfig(**bundle) if isinstance(bundle, dict) else ToolBundleConfig(bundle)
                    for bundle in tools_data['bundles']
                ]
            agent_data['tools'] = AdvancedToolConfig(**tools_data)
        
        deployment_data = data.get('deployment', {})
        if 'type' in deployment_data:
            deployment_data['type'] = DeploymentType(deployment_data['type'])
        
        repository_data = data.get('repository', {})
        if 'type' in repository_data:
            repository_data['type'] = RepositoryType(repository_data['type'])
        
        return cls(
            agent=AdvancedAgentConfig(**agent_data),
            deployment=AdvancedDeploymentConfig(**deployment_data),
            repository=AdvancedRepositoryConfig(**repository_data),
            retry=AdvancedRetryConfig(**data.get('retry', {})),
            workspace_dir=data.get('workspace_dir'),
            log_level=data.get('log_level', 'INFO'),
            debug_mode=data.get('debug_mode', False),
            enable_metrics=data.get('enable_metrics', False),
            metrics_endpoint=data.get('metrics_endpoint'),
            enable_tracing=data.get('enable_tracing', False),
            security=data.get('security', {})
        )
    
    def to_swe_agent_config(self) -> Dict[str, Any]:
        """Convert to SWE-Agent compatible configuration."""
        return {
            "agent": {
                "model": asdict(self.agent.model),
                "tools": {
                    "bundles": [asdict(bundle) for bundle in self.agent.tools.bundles],
                    "env_variables": self.agent.tools.env_variables,
                    "parse_function": self.agent.tools.parse_function,
                },
                "max_iterations": self.agent.max_iterations,
                "max_cost": self.agent.max_cost,
                "templates": self.agent.templates,
            },
            "environment": {
                "deployment": asdict(self.deployment),
                "repository": asdict(self.repository),
            },
            "retry": asdict(self.retry),
            "workspace_dir": self.workspace_dir,
            "log_level": self.log_level,
            "debug_mode": self.debug_mode,
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return asdict(self)


# Advanced configuration templates
ADVANCED_CONFIG_TEMPLATES = {
    "development": {
        "agent": {
            "model": {
                "model_name": "claude-3-opus-20240229",
                "temperature": 0.1
            },
            "tools": {
                "bundles": ["registry", "windowed", "search"],
                "env_variables": {"WINDOW": 100, "OVERLAP": 2}
            },
            "max_iterations": 30
        },
        "deployment": {
            "type": "local",
            "timeout": 1800
        },
        "repository": {
            "type": "auto"
        },
        "debug_mode": True
    },
    
    "production": {
        "agent": {
            "model": {
                "model_name": "claude-3-opus-20240229",
                "temperature": 0.0,
                "fallback_models": ["gpt-4"]
            },
            "tools": {
                "bundles": ["registry", "windowed", "search", "windowed_edit_linting", "submit"],
                "env_variables": {"WINDOW": 200, "OVERLAP": 5}
            },
            "max_iterations": 100,
            "max_cost": 10.0
        },
        "deployment": {
            "type": "docker",
            "image": "python:3.11",
            "memory_limit": "4GB",
            "timeout": 7200
        },
        "repository": {
            "type": "github",
            "clone_timeout": 600
        },
        "retry": {
            "max_attempts": 5,
            "backoff_factor": 1.5,
            "circuit_breaker_enabled": True
        },
        "enable_metrics": True,
        "enable_tracing": True
    },
    
    "research": {
        "agent": {
            "model": {
                "model_name": "gpt-4",
                "temperature": 0.2,
                "max_tokens": 8000
            },
            "tools": {
                "bundles": ["registry", "windowed", "search", "analysis"],
                "env_variables": {"WINDOW": 300, "OVERLAP": 10}
            },
            "max_iterations": 200
        },
        "deployment": {
            "type": "modal",
            "gpu_enabled": True,
            "timeout": 14400
        },
        "repository": {
            "type": "github",
            "clone_depth": 1
        }
    }
}
