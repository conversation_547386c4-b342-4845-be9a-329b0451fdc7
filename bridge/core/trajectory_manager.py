"""
Trajectory management system for the AI Coding Agent bridge.
Provides save, load, replay, and analysis capabilities for agent trajectories.
"""

import json
import gzip
import pickle
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Iterator
from dataclasses import dataclass, asdict
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class TrajectoryFormat(Enum):
    """Trajectory storage format."""
    JSON = "json"
    JSON_GZ = "json.gz"
    PICKLE = "pickle"
    PICKLE_GZ = "pickle.gz"


@dataclass
class TrajectoryStep:
    """Represents a single step in an agent trajectory."""
    step_id: int
    timestamp: str
    action_type: str
    action_data: Dict[str, Any]
    observation: Dict[str, Any]
    thought: Optional[str] = None
    reasoning: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TrajectoryStep':
        """Create from dictionary."""
        return cls(**data)


@dataclass
class TrajectoryMetadata:
    """Metadata for a trajectory."""
    session_id: str
    created_at: str
    completed_at: Optional[str] = None
    agent_config: Dict[str, Any] = None
    repository_info: Dict[str, Any] = None
    problem_statement: str = ""
    status: str = "running"
    total_steps: int = 0
    total_cost: Optional[float] = None
    success: Optional[bool] = None
    error_message: Optional[str] = None
    tags: List[str] = None
    
    def __post_init__(self):
        if self.agent_config is None:
            self.agent_config = {}
        if self.repository_info is None:
            self.repository_info = {}
        if self.tags is None:
            self.tags = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TrajectoryMetadata':
        """Create from dictionary."""
        return cls(**data)


@dataclass
class Trajectory:
    """Complete agent trajectory with metadata and steps."""
    metadata: TrajectoryMetadata
    steps: List[TrajectoryStep]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'metadata': self.metadata.to_dict(),
            'steps': [step.to_dict() for step in self.steps]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Trajectory':
        """Create from dictionary."""
        return cls(
            metadata=TrajectoryMetadata.from_dict(data['metadata']),
            steps=[TrajectoryStep.from_dict(step) for step in data['steps']]
        )
    
    def add_step(self, step: TrajectoryStep):
        """Add a step to the trajectory."""
        self.steps.append(step)
        self.metadata.total_steps = len(self.steps)
    
    def get_step(self, step_id: int) -> Optional[TrajectoryStep]:
        """Get a specific step by ID."""
        for step in self.steps:
            if step.step_id == step_id:
                return step
        return None
    
    def get_steps_by_action_type(self, action_type: str) -> List[TrajectoryStep]:
        """Get all steps of a specific action type."""
        return [step for step in self.steps if step.action_type == action_type]
    
    def get_summary(self) -> Dict[str, Any]:
        """Get trajectory summary."""
        action_counts = {}
        for step in self.steps:
            action_type = step.action_type
            action_counts[action_type] = action_counts.get(action_type, 0) + 1
        
        return {
            'session_id': self.metadata.session_id,
            'total_steps': len(self.steps),
            'status': self.metadata.status,
            'success': self.metadata.success,
            'created_at': self.metadata.created_at,
            'completed_at': self.metadata.completed_at,
            'action_counts': action_counts,
            'total_cost': self.metadata.total_cost,
            'problem_statement': self.metadata.problem_statement[:100] + '...' if len(self.metadata.problem_statement) > 100 else self.metadata.problem_statement
        }


class TrajectoryStorage:
    """Storage backend for trajectories."""
    
    def __init__(self, storage_dir: Path, format: TrajectoryFormat = TrajectoryFormat.JSON_GZ):
        """
        Initialize trajectory storage.
        
        Args:
            storage_dir: Directory for storing trajectories.
            format: Storage format.
        """
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        self.format = format
    
    def save_trajectory(self, trajectory: Trajectory) -> bool:
        """
        Save a trajectory to storage.
        
        Args:
            trajectory: Trajectory to save.
            
        Returns:
            True if successful, False otherwise.
        """
        try:
            filename = f"{trajectory.metadata.session_id}.{self.format.value}"
            filepath = self.storage_dir / filename
            
            data = trajectory.to_dict()
            
            if self.format == TrajectoryFormat.JSON:
                with open(filepath, 'w') as f:
                    json.dump(data, f, indent=2)
            elif self.format == TrajectoryFormat.JSON_GZ:
                with gzip.open(filepath, 'wt') as f:
                    json.dump(data, f, indent=2)
            elif self.format == TrajectoryFormat.PICKLE:
                with open(filepath, 'wb') as f:
                    pickle.dump(data, f)
            elif self.format == TrajectoryFormat.PICKLE_GZ:
                with gzip.open(filepath, 'wb') as f:
                    pickle.dump(data, f)
            
            logger.info(f"Saved trajectory {trajectory.metadata.session_id} to {filepath}")
            return True
            
        except Exception as e:
            logger.exception(f"Error saving trajectory {trajectory.metadata.session_id}: {e}")
            return False
    
    def load_trajectory(self, session_id: str) -> Optional[Trajectory]:
        """
        Load a trajectory from storage.
        
        Args:
            session_id: Session ID.
            
        Returns:
            Trajectory object or None if not found.
        """
        try:
            filename = f"{session_id}.{self.format.value}"
            filepath = self.storage_dir / filename
            
            if not filepath.exists():
                logger.warning(f"Trajectory file not found: {filepath}")
                return None
            
            if self.format == TrajectoryFormat.JSON:
                with open(filepath, 'r') as f:
                    data = json.load(f)
            elif self.format == TrajectoryFormat.JSON_GZ:
                with gzip.open(filepath, 'rt') as f:
                    data = json.load(f)
            elif self.format == TrajectoryFormat.PICKLE:
                with open(filepath, 'rb') as f:
                    data = pickle.load(f)
            elif self.format == TrajectoryFormat.PICKLE_GZ:
                with gzip.open(filepath, 'rb') as f:
                    data = pickle.load(f)
            
            return Trajectory.from_dict(data)
            
        except Exception as e:
            logger.exception(f"Error loading trajectory {session_id}: {e}")
            return None
    
    def list_trajectories(self) -> List[str]:
        """
        List all available trajectory session IDs.
        
        Returns:
            List of session IDs.
        """
        try:
            pattern = f"*.{self.format.value}"
            files = list(self.storage_dir.glob(pattern))
            session_ids = []
            
            for file in files:
                session_id = file.stem
                if self.format in [TrajectoryFormat.JSON_GZ, TrajectoryFormat.PICKLE_GZ]:
                    # Remove the .gz extension
                    session_id = session_id.rsplit('.', 1)[0]
                session_ids.append(session_id)
            
            return sorted(session_ids)
            
        except Exception as e:
            logger.exception(f"Error listing trajectories: {e}")
            return []
    
    def delete_trajectory(self, session_id: str) -> bool:
        """
        Delete a trajectory from storage.
        
        Args:
            session_id: Session ID.
            
        Returns:
            True if successful, False otherwise.
        """
        try:
            filename = f"{session_id}.{self.format.value}"
            filepath = self.storage_dir / filename
            
            if filepath.exists():
                filepath.unlink()
                logger.info(f"Deleted trajectory {session_id}")
                return True
            else:
                logger.warning(f"Trajectory file not found: {filepath}")
                return False
                
        except Exception as e:
            logger.exception(f"Error deleting trajectory {session_id}: {e}")
            return False


class TrajectoryManager:
    """High-level trajectory management interface."""
    
    def __init__(self, storage_dir: Optional[Path] = None, format: TrajectoryFormat = TrajectoryFormat.JSON_GZ):
        """
        Initialize trajectory manager.
        
        Args:
            storage_dir: Directory for storing trajectories.
            format: Storage format.
        """
        if storage_dir is None:
            storage_dir = Path.cwd() / "trajectories"
        
        self.storage = TrajectoryStorage(storage_dir, format)
        self.active_trajectories: Dict[str, Trajectory] = {}
    
    def create_trajectory(self, session_id: str, metadata: TrajectoryMetadata) -> Trajectory:
        """
        Create a new trajectory.
        
        Args:
            session_id: Session ID.
            metadata: Trajectory metadata.
            
        Returns:
            New trajectory object.
        """
        trajectory = Trajectory(metadata=metadata, steps=[])
        self.active_trajectories[session_id] = trajectory
        return trajectory
    
    def add_step(self, session_id: str, step: TrajectoryStep) -> bool:
        """
        Add a step to an active trajectory.
        
        Args:
            session_id: Session ID.
            step: Trajectory step.
            
        Returns:
            True if successful, False otherwise.
        """
        if session_id in self.active_trajectories:
            self.active_trajectories[session_id].add_step(step)
            return True
        else:
            logger.warning(f"No active trajectory for session {session_id}")
            return False
    
    def complete_trajectory(self, session_id: str, success: bool = True, error_message: Optional[str] = None) -> bool:
        """
        Mark a trajectory as completed and save it.
        
        Args:
            session_id: Session ID.
            success: Whether the trajectory was successful.
            error_message: Error message if unsuccessful.
            
        Returns:
            True if successful, False otherwise.
        """
        if session_id not in self.active_trajectories:
            logger.warning(f"No active trajectory for session {session_id}")
            return False
        
        trajectory = self.active_trajectories[session_id]
        trajectory.metadata.completed_at = datetime.now().isoformat()
        trajectory.metadata.status = "completed"
        trajectory.metadata.success = success
        if error_message:
            trajectory.metadata.error_message = error_message
        
        # Save trajectory
        saved = self.storage.save_trajectory(trajectory)
        
        # Remove from active trajectories
        if saved:
            del self.active_trajectories[session_id]
        
        return saved
    
    def get_trajectory(self, session_id: str) -> Optional[Trajectory]:
        """
        Get a trajectory (active or stored).
        
        Args:
            session_id: Session ID.
            
        Returns:
            Trajectory object or None if not found.
        """
        # Check active trajectories first
        if session_id in self.active_trajectories:
            return self.active_trajectories[session_id]
        
        # Load from storage
        return self.storage.load_trajectory(session_id)
    
    def list_trajectories(self, include_active: bool = True) -> List[Dict[str, Any]]:
        """
        List all trajectories with summaries.
        
        Args:
            include_active: Whether to include active trajectories.
            
        Returns:
            List of trajectory summaries.
        """
        summaries = []
        
        # Add stored trajectories
        stored_session_ids = self.storage.list_trajectories()
        for session_id in stored_session_ids:
            trajectory = self.storage.load_trajectory(session_id)
            if trajectory:
                summaries.append(trajectory.get_summary())
        
        # Add active trajectories
        if include_active:
            for session_id, trajectory in self.active_trajectories.items():
                summary = trajectory.get_summary()
                summary['status'] = 'active'
                summaries.append(summary)
        
        return sorted(summaries, key=lambda x: x['created_at'], reverse=True)
    
    def replay_trajectory(self, session_id: str) -> Iterator[TrajectoryStep]:
        """
        Replay a trajectory step by step.
        
        Args:
            session_id: Session ID.
            
        Yields:
            Trajectory steps in order.
        """
        trajectory = self.get_trajectory(session_id)
        if trajectory:
            for step in trajectory.steps:
                yield step
        else:
            logger.warning(f"Trajectory not found: {session_id}")
    
    def analyze_trajectory(self, session_id: str) -> Dict[str, Any]:
        """
        Analyze a trajectory and provide insights.
        
        Args:
            session_id: Session ID.
            
        Returns:
            Analysis results.
        """
        trajectory = self.get_trajectory(session_id)
        if not trajectory:
            return {"error": "Trajectory not found"}
        
        analysis = {
            "summary": trajectory.get_summary(),
            "step_analysis": {},
            "patterns": {},
            "recommendations": []
        }
        
        # Analyze step patterns
        action_sequence = [step.action_type for step in trajectory.steps]
        action_counts = {}
        for action in action_sequence:
            action_counts[action] = action_counts.get(action, 0) + 1
        
        analysis["patterns"]["action_distribution"] = action_counts
        analysis["patterns"]["action_sequence"] = action_sequence
        
        # Analyze step timing
        if len(trajectory.steps) > 1:
            step_times = []
            for i in range(1, len(trajectory.steps)):
                prev_time = datetime.fromisoformat(trajectory.steps[i-1].timestamp)
                curr_time = datetime.fromisoformat(trajectory.steps[i].timestamp)
                step_times.append((curr_time - prev_time).total_seconds())
            
            analysis["patterns"]["avg_step_time"] = sum(step_times) / len(step_times)
            analysis["patterns"]["total_time"] = sum(step_times)
        
        # Generate recommendations
        if trajectory.metadata.success is False:
            analysis["recommendations"].append("Review error patterns and consider adjusting agent configuration")
        
        if len(trajectory.steps) > 100:
            analysis["recommendations"].append("Consider reducing max_iterations to improve efficiency")
        
        return analysis
    
    def cleanup_old_trajectories(self, days: int = 30) -> int:
        """
        Clean up trajectories older than specified days.
        
        Args:
            days: Number of days to keep.
            
        Returns:
            Number of trajectories cleaned up.
        """
        cutoff_date = datetime.now().timestamp() - (days * 24 * 3600)
        cleaned_count = 0
        
        session_ids = self.storage.list_trajectories()
        for session_id in session_ids:
            trajectory = self.storage.load_trajectory(session_id)
            if trajectory:
                created_at = datetime.fromisoformat(trajectory.metadata.created_at)
                if created_at.timestamp() < cutoff_date:
                    if self.storage.delete_trajectory(session_id):
                        cleaned_count += 1
        
        logger.info(f"Cleaned up {cleaned_count} old trajectories")
        return cleaned_count


# Global trajectory manager instance
trajectory_manager = TrajectoryManager()
