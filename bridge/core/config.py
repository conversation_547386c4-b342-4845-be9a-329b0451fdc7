"""
Configuration module for the AI Coding Agent bridge.
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional

from bridge.utils.env_loader import get_env

# Base paths
PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
SWE_AGENT_PATH = PROJECT_ROOT / "swe-agent"

# Default configuration
DEFAULT_CONFIG = {
    "swe_agent": {
        "api_port": int(get_env("SWE_AGENT_PORT", 8000)),
        "api_host": "localhost",
        "model": get_env("SWE_AGENT_MODEL", "claude-sonnet-4-20250514"),
    },
    "bridge": {
        "api_port": int(get_env("BRIDGE_API_PORT", 8080)),
        "api_host": "localhost",
        "vim_port": int(get_env("BRIDGE_VIM_PORT", 8081)),
    },
    "logging": {
        "level": get_env("LOG_LEVEL", "INFO"),
        "file": str(PROJECT_ROOT / "logs" / "bridge.log"),
    },
    "api_keys": {
        "anthropic": get_env("ANTHROPIC_API_KEY", ""),
        "openai": get_env("OPENAI_API_KEY", ""),
    },
    "auth": {
        "session_ttl": int(get_env("SESSION_TTL", 86400)),  # 24 hours
        "admin_email": get_env("ADMIN_EMAIL", ""),
        "require_email_verification": get_env("REQUIRE_EMAIL_VERIFICATION", "false").lower() == "true"
    },
    "jwt": {
        "issuer": get_env("JWT_ISSUER", "ai-coding-agent"),
        "audience": get_env("JWT_AUDIENCE", "ai-coding-agent-api"),
        "access_token_ttl": int(get_env("JWT_ACCESS_TOKEN_TTL", 3600)),  # 1 hour
        "refresh_token_ttl": int(get_env("JWT_REFRESH_TOKEN_TTL", 2592000)),  # 30 days
        "private_key": get_env("JWT_PRIVATE_KEY", ""),
        "public_key": get_env("JWT_PUBLIC_KEY", "")
    },
    "oauth": {
        "github": {
            "client_id": get_env("GITHUB_CLIENT_ID", ""),
            "client_secret": get_env("GITHUB_CLIENT_SECRET", ""),
            "redirect_uri": get_env("GITHUB_REDIRECT_URI", "http://localhost:8080/api/auth/oauth/github/callback"),
            "scope": ["user:email", "read:user"]
        },
        "google": {
            "client_id": get_env("GOOGLE_CLIENT_ID", ""),
            "client_secret": get_env("GOOGLE_CLIENT_SECRET", ""),
            "redirect_uri": get_env("GOOGLE_REDIRECT_URI", "http://localhost:8080/api/auth/oauth/google/callback"),
            "scope": ["openid", "email", "profile"]
        },
        "microsoft": {
            "client_id": get_env("MICROSOFT_CLIENT_ID", ""),
            "client_secret": get_env("MICROSOFT_CLIENT_SECRET", ""),
            "tenant_id": get_env("MICROSOFT_TENANT_ID", "common"),
            "redirect_uri": get_env("MICROSOFT_REDIRECT_URI", "http://localhost:8080/api/auth/oauth/microsoft/callback"),
            "scope": ["openid", "email", "profile", "User.Read"]
        }
    },
    "security": {
        "rate_limit_per_minute": int(get_env("RATE_LIMIT_PER_MINUTE", 1000)),
        "max_file_size_mb": int(get_env("MAX_FILE_SIZE_MB", 10)),
        "allowed_file_extensions": [".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp"],
        "blocked_paths": ["/etc", "/proc", "/sys", "/dev"]
    }
}


class Config:
    """Configuration class for the AI Coding Agent bridge."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration.
        
        Args:
            config_path: Path to a custom configuration file.
        """
        self.config = DEFAULT_CONFIG.copy()
        if config_path:
            self._load_config(config_path)
    
    def _load_config(self, config_path: str) -> None:
        """
        Load configuration from a file.
        
        Args:
            config_path: Path to the configuration file.
        """
        import yaml
        try:
            with open(config_path, 'r') as f:
                custom_config = yaml.safe_load(f)
                self._update_nested_dict(self.config, custom_config)
        except Exception as e:
            print(f"Error loading config from {config_path}: {e}")
    
    def _update_nested_dict(self, d: Dict[str, Any], u: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a nested dictionary with another dictionary.
        
        Args:
            d: Dictionary to update.
            u: Dictionary with updates.
            
        Returns:
            Updated dictionary.
        """
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                d[k] = self._update_nested_dict(d[k], v)
            else:
                d[k] = v
        return d
    
    def get(self, *keys: str, default: Any = None) -> Any:
        """
        Get a configuration value.

        Args:
            *keys: Keys to navigate the nested dictionary.
            default: Default value if the key is not found.

        Returns:
            Configuration value.
        """
        current = self.config
        for key in keys:
            if key not in current:
                return default
            current = current[key]
        return current

    def get_oauth_config(self, provider: str) -> Optional[Dict[str, Any]]:
        """Get OAuth configuration for a specific provider."""
        return self.get("oauth", provider)

    def is_oauth_configured(self, provider: str) -> bool:
        """Check if OAuth provider is configured."""
        oauth_config = self.get_oauth_config(provider)
        if not oauth_config:
            return False

        required_fields = ["client_id", "client_secret"]
        return all(oauth_config.get(field) for field in required_fields)

    def get_configured_oauth_providers(self) -> list:
        """Get list of configured OAuth providers."""
        providers = []
        for provider in ["github", "google", "microsoft"]:
            if self.is_oauth_configured(provider):
                providers.append(provider)
        return providers


# Global configuration instance
config = Config()
