"""
Main entry point for the AI Coding Agent bridge.
"""

import argparse
import logging
import os
import sys
import threading
import time

from bridge.core.config import config
from bridge.integrations.vim_integration import vim_integration


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="AI Coding Agent Bridge")
    parser.add_argument(
        "--config",
        type=str,
        help="Path to config file"
    )
    parser.add_argument(
        "--api-only",
        action="store_true",
        help="Only start the API server"
    )
    parser.add_argument(
        "--vim-only",
        action="store_true",
        help="Only start the Vim integration server"
    )
    parser.add_argument(
        "--api-host",
        type=str,
        help="API server host"
    )
    parser.add_argument(
        "--api-port",
        type=int,
        help="API server port"
    )
    parser.add_argument(
        "--vim-host",
        type=str,
        default="localhost",
        help="Vim integration server host"
    )
    parser.add_argument(
        "--vim-port",
        type=int,
        default=8081,
        help="Vim integration server port"
    )
    parser.add_argument(
        "--enhanced",
        action="store_true",
        help="Use enhanced API server with session management and WebSocket support"
    )
    parser.add_argument(
        "--legacy",
        action="store_true",
        help="Force use of legacy API server"
    )

    return parser.parse_args()


def get_env_bool(var_name: str, default: bool = False) -> bool:
    """Get boolean value from environment variable."""
    value = os.getenv(var_name, "").lower()
    return value in ("true", "1", "yes", "on") if value else default


def main():
    """Main entry point."""
    args = parse_args()

    # Determine which API server to use
    use_enhanced = False
    if args.enhanced:
        use_enhanced = True
    elif args.legacy:
        use_enhanced = False
    else:
        # Check environment variables
        use_enhanced = get_env_bool("BRIDGE_ENABLE_ENHANCED_SESSIONS", False)

    # Set up logging
    logging_level = getattr(logging, config.get("logging", "level", default="INFO"))
    logging.basicConfig(
        level=logging_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )
    logger = logging.getLogger(__name__)

    # Load config if provided
    if args.config:
        from bridge.core.config import Config
        # Create new config instance (note: this doesn't replace the global config)
        custom_config = Config(args.config)
        # For now, we'll use the global config and just log the custom config path
        logger.info(f"Custom config specified: {args.config}")

    # Override config with command line arguments
    if args.api_host:
        config.config["bridge"]["api_host"] = args.api_host
    if args.api_port:
        config.config["bridge"]["api_port"] = args.api_port

    # Initialize session manager if using enhanced mode
    if use_enhanced:
        logger.info("Starting in enhanced mode with session management")
        from bridge.core.session_manager import session_manager  # noqa: F401
        # Initialize session manager (imported for initialization)
        logger.info("Session manager initialized")
    else:
        logger.info("Starting in legacy mode")

    # Start servers based on arguments
    api_thread = None

    if not args.vim_only:
        # Choose API server based on mode
        if use_enhanced:
            from bridge.api.enhanced_api_minimal import run_server as run_enhanced_server
            api_server_func = run_enhanced_server
            logger.info("Using enhanced API server with session management")
        else:
            from bridge.api.api_server import run_server as run_legacy_server
            api_server_func = run_legacy_server
            logger.info("Using legacy API server")

        # Start API server in a separate thread
        api_thread = threading.Thread(
            target=api_server_func,
            daemon=True
        )
        api_thread.start()
        logger.info("API server started")

    if not args.api_only:
        # Start Vim integration server
        vim_integration.start_server(
            host=args.vim_host,
            port=args.vim_port
        )

    try:
        # Keep the main thread running
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("Shutting down...")

        # Stop Vim integration server
        if not args.api_only:
            vim_integration.stop_server()

        # API server will stop when the main thread exits
        sys.exit(0)


if __name__ == "__main__":
    main()
