"""
API components for the AI Coding Agent bridge.

This package contains the API server implementations and endpoint handlers.
"""

# Import the main API server functions
try:
    from .enhanced_api_minimal import app as enhanced_app, run_server as run_enhanced_server
    enhanced_available = True
except ImportError:
    enhanced_available = False

try:
    from .api_server import app as legacy_app, run_server as run_legacy_server
    legacy_available = True
except ImportError:
    legacy_available = False

__all__ = []

if enhanced_available:
    __all__.extend(['enhanced_app', 'run_enhanced_server'])

if legacy_available:
    __all__.extend(['legacy_app', 'run_legacy_server'])
