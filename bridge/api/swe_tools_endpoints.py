"""
API endpoints for SWE-Agent tool execution.
Provides REST API access to all SWE-Agent tools with security validation.
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional
from flask import Blueprint, request, jsonify
from flask_socketio import SocketIO, emit

from bridge.integrations.swe_tool_proxy import swe_tool_proxy, Tool<PERSON><PERSON>ult
from bridge.core.tool_security import tool_security
from bridge.auth.session_auth import session_auth

logger = logging.getLogger(__name__)

# Create blueprint for SWE tools endpoints
swe_tools_bp = Blueprint('swe_tools', __name__, url_prefix='/api/swe-tools')


@swe_tools_bp.route('/list', methods=['GET'])
@session_auth.optional_auth
def list_tools():
    """
    List all available SWE-Agent tools.
    
    Returns:
        JSON response with tool definitions
    """
    try:
        tools = swe_tool_proxy.get_available_tools()
        
        # Convert tool definitions to JSON-serializable format
        tools_data = {}
        for tool_name, tool_def in tools.items():
            tools_data[tool_name] = {
                'name': tool_def.name,
                'signature': tool_def.signature,
                'docstring': tool_def.docstring,
                'arguments': tool_def.arguments,
                'bundle_path': tool_def.bundle_path,
                'state_command': tool_def.state_command
            }
        
        return jsonify({
            'status': 'success',
            'tools': tools_data,
            'count': len(tools_data)
        })
        
    except Exception as e:
        logger.error(f"Error listing tools: {e}")
        return jsonify({'error': str(e)}), 500


@swe_tools_bp.route('/<tool_name>/schema', methods=['GET'])
@session_auth.optional_auth
def get_tool_schema(tool_name: str):
    """
    Get OpenAI function calling schema for a specific tool.
    
    Args:
        tool_name: Name of the tool
        
    Returns:
        JSON response with tool schema
    """
    try:
        schema = swe_tool_proxy.get_tool_schema(tool_name)
        
        if schema is None:
            return jsonify({'error': f'Tool "{tool_name}" not found'}), 404
        
        return jsonify({
            'status': 'success',
            'tool_name': tool_name,
            'schema': schema
        })
        
    except Exception as e:
        logger.error(f"Error getting tool schema for {tool_name}: {e}")
        return jsonify({'error': str(e)}), 500


@swe_tools_bp.route('/validate', methods=['POST'])
@session_auth.optional_auth
def validate_tool_parameters():
    """
    Validate tool parameters before execution.
    
    Expected JSON payload:
    {
        "tool_name": "str_replace_editor",
        "parameters": {
            "command": "view",
            "path": "/testbed/file.py"
        }
    }
    
    Returns:
        JSON response with validation results
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        tool_name = data.get('tool_name')
        parameters = data.get('parameters', {})
        
        if not tool_name:
            return jsonify({'error': 'tool_name is required'}), 400
        
        # Validate parameters
        violations = swe_tool_proxy.validate_tool_parameters(tool_name, parameters)
        
        if violations:
            return jsonify({
                'status': 'invalid',
                'tool_name': tool_name,
                'violations': [
                    {
                        'type': v.violation_type,
                        'message': v.message,
                        'parameter': v.parameter,
                        'value': v.value
                    }
                    for v in violations
                ],
                'summary': tool_security.get_violation_summary(violations)
            }), 400
        
        return jsonify({
            'status': 'valid',
            'tool_name': tool_name,
            'message': 'Parameters are valid'
        })
        
    except Exception as e:
        logger.error(f"Error validating tool parameters: {e}")
        return jsonify({'error': str(e)}), 500


@swe_tools_bp.route('/execute', methods=['POST'])
@session_auth.optional_auth
def execute_tool():
    """
    Execute a SWE-Agent tool with given parameters.
    
    Expected JSON payload:
    {
        "tool_name": "str_replace_editor",
        "parameters": {
            "command": "view",
            "path": "/testbed/file.py"
        },
        "working_directory": "/testbed",  // optional
        "timeout": 30,                   // optional, seconds
        "async": false                   // optional, for async execution
    }
    
    Returns:
        JSON response with execution results
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        tool_name = data.get('tool_name')
        parameters = data.get('parameters', {})
        working_directory = data.get('working_directory')
        timeout = data.get('timeout', 30)
        is_async = data.get('async', False)
        
        if not tool_name:
            return jsonify({'error': 'tool_name is required'}), 400
        
        # Validate timeout
        if not isinstance(timeout, int) or timeout < 1 or timeout > 300:
            return jsonify({'error': 'timeout must be between 1 and 300 seconds'}), 400
        
        # For async execution, start the task and return immediately
        if is_async:
            # Generate a task ID
            task_id = f"task_{int(time.time() * 1000)}"
            
            # Start async execution (this would need proper task queue in production)
            asyncio.create_task(_execute_tool_async(task_id, tool_name, parameters, working_directory, timeout))
            
            return jsonify({
                'status': 'started',
                'task_id': task_id,
                'tool_name': tool_name,
                'message': 'Tool execution started asynchronously'
            }), 202
        
        # Synchronous execution
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(
                swe_tool_proxy.execute_tool(tool_name, parameters, working_directory, timeout)
            )
        finally:
            loop.close()
        
        # Convert result to JSON-serializable format
        response_data = {
            'status': 'completed',
            'success': result.success,
            'tool_name': result.tool_name,
            'output': result.output,
            'error': result.error,
            'exit_code': result.exit_code,
            'execution_time': result.execution_time,
            'parameters': result.parameters
        }
        
        status_code = 200 if result.success else 400
        return jsonify(response_data), status_code
        
    except Exception as e:
        logger.error(f"Error executing tool: {e}")
        return jsonify({'error': str(e)}), 500


async def _execute_tool_async(task_id: str, tool_name: str, parameters: Dict[str, Any],
                             working_directory: Optional[str], timeout: int):
    """Execute tool asynchronously and emit results via WebSocket."""
    try:
        # Emit start event
        # Note: In production, this would use proper WebSocket room management
        
        result = await swe_tool_proxy.execute_tool(tool_name, parameters, working_directory, timeout)
        
        # Emit completion event
        # socketio.emit('tool_execution_complete', {
        #     'task_id': task_id,
        #     'result': asdict(result)
        # })
        
    except Exception as e:
        logger.error(f"Error in async tool execution {task_id}: {e}")
        # Emit error event
        # socketio.emit('tool_execution_error', {
        #     'task_id': task_id,
        #     'error': str(e)
        # })


@swe_tools_bp.route('/health', methods=['GET'])
def tools_health():
    """Health check for SWE tools service."""
    try:
        tools = swe_tool_proxy.get_available_tools()
        
        return jsonify({
            'status': 'healthy',
            'tools_available': len(tools),
            'swe_env_available': swe_tool_proxy.has_swe_env,
            'tools_loaded': swe_tool_proxy.tools_loaded
        })
        
    except Exception as e:
        logger.error(f"SWE tools health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500


# WebSocket events for real-time tool execution
def setup_swe_tools_websocket(socketio: SocketIO):
    """Set up WebSocket events for real-time tool execution."""
    
    @socketio.on('tool_execute')
    def handle_tool_execute(data):
        """Handle tool execution request via WebSocket."""
        try:
            tool_name = data.get('tool_name')
            parameters = data.get('parameters', {})
            working_directory = data.get('working_directory')
            timeout = data.get('timeout', 30)
            request_id = data.get('request_id')
            
            if not tool_name:
                emit('tool_error', {
                    'request_id': request_id,
                    'error': 'tool_name is required'
                })
                return
            
            # Start async execution
            task_id = f"ws_task_{int(time.time() * 1000)}"
            asyncio.create_task(_execute_tool_websocket(
                task_id, request_id, tool_name, parameters, working_directory, timeout
            ))
            
            emit('tool_started', {
                'request_id': request_id,
                'task_id': task_id,
                'tool_name': tool_name
            })
            
        except Exception as e:
            logger.error(f"Error handling WebSocket tool execution: {e}")
            emit('tool_error', {
                'request_id': data.get('request_id'),
                'error': str(e)
            })
    
    @socketio.on('tool_validate')
    def handle_tool_validate(data):
        """Handle tool parameter validation via WebSocket."""
        try:
            tool_name = data.get('tool_name')
            parameters = data.get('parameters', {})
            request_id = data.get('request_id')
            
            if not tool_name:
                emit('validation_error', {
                    'request_id': request_id,
                    'error': 'tool_name is required'
                })
                return
            
            violations = swe_tool_proxy.validate_tool_parameters(tool_name, parameters)
            
            if violations:
                emit('validation_result', {
                    'request_id': request_id,
                    'valid': False,
                    'violations': [
                        {
                            'type': v.violation_type,
                            'message': v.message,
                            'parameter': v.parameter,
                            'value': v.value
                        }
                        for v in violations
                    ]
                })
            else:
                emit('validation_result', {
                    'request_id': request_id,
                    'valid': True,
                    'message': 'Parameters are valid'
                })
                
        except Exception as e:
            logger.error(f"Error handling WebSocket tool validation: {e}")
            emit('validation_error', {
                'request_id': data.get('request_id'),
                'error': str(e)
            })


async def _execute_tool_websocket(task_id: str, request_id: str, tool_name: str,
                                 parameters: Dict[str, Any], working_directory: Optional[str],
                                 timeout: int):
    """Execute tool and emit results via WebSocket."""
    try:
        result = await swe_tool_proxy.execute_tool(tool_name, parameters, working_directory, timeout)
        
        # Note: In production, this would use proper WebSocket emission
        # socketio.emit('tool_result', {
        #     'request_id': request_id,
        #     'task_id': task_id,
        #     'success': result.success,
        #     'output': result.output,
        #     'error': result.error,
        #     'execution_time': result.execution_time
        # })
        
    except Exception as e:
        logger.error(f"Error in WebSocket tool execution {task_id}: {e}")
        # socketio.emit('tool_error', {
        #     'request_id': request_id,
        #     'task_id': task_id,
        #     'error': str(e)
        # })
