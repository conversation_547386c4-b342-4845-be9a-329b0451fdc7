"""
API endpoints for terminal operations.
Provides terminal execution and session management capabilities for the AI Coding Agent.
"""

import json
import logging
import os
import subprocess
import threading
import time
import uuid
from typing import Dict, Any, List, Optional
from flask import Blueprint, request, jsonify
import pty
import select
import signal

from bridge.auth.session_auth import session_auth

logger = logging.getLogger(__name__)

# Create blueprint for terminal endpoints
terminal_bp = Blueprint('terminal', __name__, url_prefix='/api/terminal')

# Global terminal sessions storage
terminal_sessions: Dict[str, Dict[str, Any]] = {}
session_lock = threading.Lock()


class TerminalSession:
    """Manages a persistent terminal session."""
    
    def __init__(self, session_id: str, working_directory: str = None):
        self.session_id = session_id
        self.working_directory = working_directory or os.getcwd()
        self.process = None
        self.master_fd = None
        self.output_buffer = []
        self.is_active = False
        self.created_at = time.time()
        self.last_activity = time.time()
        self.lock = threading.Lock()
        
    def start(self):
        """Start the terminal session."""
        try:
            # Create a pseudo-terminal
            self.master_fd, slave_fd = pty.openpty()
            
            # Start bash process
            self.process = subprocess.Popen(
                ['/bin/bash'],
                stdin=slave_fd,
                stdout=slave_fd,
                stderr=slave_fd,
                cwd=self.working_directory,
                preexec_fn=os.setsid
            )
            
            # Close slave fd in parent process
            os.close(slave_fd)
            
            self.is_active = True
            
            # Start output reading thread
            self.output_thread = threading.Thread(target=self._read_output)
            self.output_thread.daemon = True
            self.output_thread.start()
            
            logger.info(f"Terminal session {self.session_id} started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start terminal session {self.session_id}: {e}")
            return False
    
    def _read_output(self):
        """Read output from the terminal in a separate thread."""
        while self.is_active and self.process and self.process.poll() is None:
            try:
                # Use select to check if data is available
                ready, _, _ = select.select([self.master_fd], [], [], 0.1)
                
                if ready:
                    data = os.read(self.master_fd, 1024)
                    if data:
                        output = data.decode('utf-8', errors='replace')
                        with self.lock:
                            self.output_buffer.append({
                                'timestamp': time.time(),
                                'data': output
                            })
                            self.last_activity = time.time()
                            
                            # Keep only last 1000 output entries
                            if len(self.output_buffer) > 1000:
                                self.output_buffer = self.output_buffer[-1000:]
                                
            except (OSError, ValueError):
                break
    
    def send_input(self, input_text: str):
        """Send input to the terminal."""
        if not self.is_active or not self.master_fd:
            return False
        
        try:
            # Ensure input ends with newline
            if not input_text.endswith('\n'):
                input_text += '\n'
            
            os.write(self.master_fd, input_text.encode('utf-8'))
            self.last_activity = time.time()
            return True
            
        except (OSError, ValueError):
            return False
    
    def get_output(self, since_timestamp: float = None) -> List[Dict[str, Any]]:
        """Get terminal output since a specific timestamp."""
        with self.lock:
            if since_timestamp is None:
                return self.output_buffer.copy()
            else:
                return [entry for entry in self.output_buffer if entry['timestamp'] > since_timestamp]
    
    def stop(self):
        """Stop the terminal session."""
        self.is_active = False
        
        if self.process:
            try:
                # Send SIGTERM to the process group
                os.killpg(os.getpgid(self.process.pid), signal.SIGTERM)
                
                # Wait for process to terminate
                self.process.wait(timeout=5)
                
            except (ProcessLookupError, subprocess.TimeoutExpired):
                # Force kill if it doesn't terminate gracefully
                try:
                    os.killpg(os.getpgid(self.process.pid), signal.SIGKILL)
                except ProcessLookupError:
                    pass
        
        if self.master_fd:
            try:
                os.close(self.master_fd)
            except OSError:
                pass
        
        logger.info(f"Terminal session {self.session_id} stopped")


@terminal_bp.route('/execute', methods=['POST'])
@session_auth.optional_auth
def execute_command():
    """
    Execute a single command and return the result.
    
    Expected JSON payload:
    {
        "command": "ls -la",
        "working_directory": "/path/to/directory",  // optional
        "timeout": 30,                              // optional, seconds
        "capture_output": true                      // optional, capture stdout/stderr
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        command = data.get('command')
        if not command:
            return jsonify({"error": "command is required"}), 400
        
        working_directory = data.get('working_directory', os.getcwd())
        timeout = data.get('timeout', 30)
        capture_output = data.get('capture_output', True)
        
        # Security check - basic command validation
        if not _is_safe_command(command):
            return jsonify({"error": "Command not allowed for security reasons"}), 403
        
        try:
            start_time = time.time()
            
            if capture_output:
                result = subprocess.run(
                    command,
                    shell=True,
                    cwd=working_directory,
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )
                
                execution_time = time.time() - start_time
                
                return jsonify({
                    "status": "success",
                    "command": command,
                    "return_code": result.returncode,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "execution_time": execution_time,
                    "working_directory": working_directory
                })
            else:
                # Execute without capturing output (for long-running commands)
                process = subprocess.Popen(
                    command,
                    shell=True,
                    cwd=working_directory
                )
                
                return jsonify({
                    "status": "success",
                    "command": command,
                    "process_id": process.pid,
                    "message": "Command started (output not captured)",
                    "working_directory": working_directory
                })
                
        except subprocess.TimeoutExpired:
            return jsonify({"error": f"Command timed out after {timeout} seconds"}), 408
        except subprocess.CalledProcessError as e:
            return jsonify({
                "status": "error",
                "command": command,
                "return_code": e.returncode,
                "error": str(e)
            }), 400
        except Exception as e:
            return jsonify({"error": f"Execution error: {str(e)}"}), 500
            
    except Exception as e:
        logger.error(f"Command execution error: {e}")
        return jsonify({"error": str(e)}), 500


def _is_safe_command(command: str) -> bool:
    """
    Check if a command is safe to execute.
    Prevents execution of potentially dangerous commands.
    """
    # List of dangerous commands/patterns
    dangerous_patterns = [
        'rm -rf /',
        'dd if=',
        'mkfs',
        'fdisk',
        'format',
        'shutdown',
        'reboot',
        'halt',
        'init 0',
        'init 6',
        'killall',
        'pkill -9',
        'chmod 777 /',
        'chown -R',
        '> /dev/',
        'curl | sh',
        'wget | sh',
        'eval',
        'exec',
        'sudo su',
        'su -'
    ]
    
    command_lower = command.lower().strip()
    
    # Check for dangerous patterns
    for pattern in dangerous_patterns:
        if pattern in command_lower:
            return False
    
    # Check for suspicious redirections
    if '>>' in command and ('/etc/' in command or '/bin/' in command or '/usr/' in command):
        return False
    
    return True


@terminal_bp.route('/sessions', methods=['POST'])
@session_auth.optional_auth
def create_terminal_session():
    """
    Create a new persistent terminal session.

    Expected JSON payload:
    {
        "working_directory": "/path/to/directory",  // optional
        "session_name": "my_session"                // optional
    }
    """
    try:
        data = request.get_json() or {}

        working_directory = data.get('working_directory', os.getcwd())
        session_name = data.get('session_name')

        # Generate session ID
        session_id = str(uuid.uuid4())

        # Create terminal session
        session = TerminalSession(session_id, working_directory)

        if session.start():
            with session_lock:
                terminal_sessions[session_id] = {
                    'session': session,
                    'name': session_name,
                    'created_at': session.created_at
                }

            return jsonify({
                "status": "success",
                "session_id": session_id,
                "working_directory": working_directory,
                "message": "Terminal session created successfully"
            })
        else:
            return jsonify({"error": "Failed to create terminal session"}), 500

    except Exception as e:
        logger.error(f"Terminal session creation error: {e}")
        return jsonify({"error": str(e)}), 500


@terminal_bp.route('/sessions/<session_id>/input', methods=['POST'])
@session_auth.optional_auth
def send_terminal_input(session_id: str):
    """
    Send input to a terminal session.

    Expected JSON payload:
    {
        "input": "ls -la"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        input_text = data.get('input')
        if input_text is None:
            return jsonify({"error": "input is required"}), 400

        with session_lock:
            if session_id not in terminal_sessions:
                return jsonify({"error": "Terminal session not found"}), 404

            session = terminal_sessions[session_id]['session']

        if session.send_input(input_text):
            return jsonify({
                "status": "success",
                "session_id": session_id,
                "message": "Input sent successfully"
            })
        else:
            return jsonify({"error": "Failed to send input to terminal session"}), 500

    except Exception as e:
        logger.error(f"Terminal input error: {e}")
        return jsonify({"error": str(e)}), 500


@terminal_bp.route('/sessions/<session_id>/output', methods=['GET'])
@session_auth.optional_auth
def get_terminal_output(session_id: str):
    """
    Get output from a terminal session.

    Query parameters:
    - since: timestamp to get output since (optional)
    - format: 'json' or 'text' (optional, defaults to 'json')
    """
    try:
        with session_lock:
            if session_id not in terminal_sessions:
                return jsonify({"error": "Terminal session not found"}), 404

            session = terminal_sessions[session_id]['session']

        since_timestamp = request.args.get('since')
        if since_timestamp:
            try:
                since_timestamp = float(since_timestamp)
            except ValueError:
                return jsonify({"error": "Invalid since timestamp"}), 400

        output_format = request.args.get('format', 'json')

        output_entries = session.get_output(since_timestamp)

        if output_format == 'text':
            # Return as plain text
            text_output = ''.join([entry['data'] for entry in output_entries])
            return text_output, 200, {'Content-Type': 'text/plain'}
        else:
            # Return as JSON
            return jsonify({
                "status": "success",
                "session_id": session_id,
                "output": output_entries,
                "count": len(output_entries),
                "is_active": session.is_active
            })

    except Exception as e:
        logger.error(f"Terminal output error: {e}")
        return jsonify({"error": str(e)}), 500


@terminal_bp.route('/sessions/<session_id>', methods=['DELETE'])
@session_auth.optional_auth
def delete_terminal_session(session_id: str):
    """
    Delete a terminal session.
    """
    try:
        with session_lock:
            if session_id not in terminal_sessions:
                return jsonify({"error": "Terminal session not found"}), 404

            session = terminal_sessions[session_id]['session']
            session.stop()

            del terminal_sessions[session_id]

        return jsonify({
            "status": "success",
            "session_id": session_id,
            "message": "Terminal session deleted successfully"
        })

    except Exception as e:
        logger.error(f"Terminal session deletion error: {e}")
        return jsonify({"error": str(e)}), 500


@terminal_bp.route('/sessions', methods=['GET'])
@session_auth.optional_auth
def list_terminal_sessions():
    """
    List all active terminal sessions.
    """
    try:
        with session_lock:
            sessions_info = []

            for session_id, session_data in terminal_sessions.items():
                session = session_data['session']
                sessions_info.append({
                    "session_id": session_id,
                    "name": session_data.get('name'),
                    "working_directory": session.working_directory,
                    "is_active": session.is_active,
                    "created_at": session.created_at,
                    "last_activity": session.last_activity
                })

        return jsonify({
            "status": "success",
            "sessions": sessions_info,
            "count": len(sessions_info)
        })

    except Exception as e:
        logger.error(f"Terminal sessions list error: {e}")
        return jsonify({"error": str(e)}), 500


@terminal_bp.route('/sessions/<session_id>/status', methods=['GET'])
@session_auth.optional_auth
def get_terminal_session_status(session_id: str):
    """
    Get status of a specific terminal session.
    """
    try:
        with session_lock:
            if session_id not in terminal_sessions:
                return jsonify({"error": "Terminal session not found"}), 404

            session_data = terminal_sessions[session_id]
            session = session_data['session']

        return jsonify({
            "status": "success",
            "session_id": session_id,
            "name": session_data.get('name'),
            "working_directory": session.working_directory,
            "is_active": session.is_active,
            "created_at": session.created_at,
            "last_activity": session.last_activity,
            "process_id": session.process.pid if session.process else None
        })

    except Exception as e:
        logger.error(f"Terminal session status error: {e}")
        return jsonify({"error": str(e)}), 500
