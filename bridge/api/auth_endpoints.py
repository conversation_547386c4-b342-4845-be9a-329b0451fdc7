"""
Authentication API endpoints for OAuth 2.0 and JWT token management.
Provides secure authentication flows and token management.
"""

import json
import logging
import secrets
from datetime import datetime
from typing import Dict, Any, Optional
from flask import Blueprint, request, jsonify, redirect, render_template_string, session
from urllib.parse import urlencode, parse_qs

from bridge.auth.oauth_provider import oauth_provider, GrantType
from bridge.auth.token_manager import token_manager
from bridge.auth.session_auth import session_auth

logger = logging.getLogger(__name__)

# Create blueprint for auth endpoints
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')


@auth_bp.route('/authorize', methods=['GET'])
def authorize():
    """
    OAuth 2.0 authorization endpoint.
    
    Query parameters:
    - response_type: "code" for authorization code flow
    - client_id: OAuth client ID
    - redirect_uri: Redirect URI after authorization
    - scope: Requested scopes (space-separated)
    - state: CSRF protection state parameter
    - code_challenge: PKCE code challenge
    - code_challenge_method: PKCE code challenge method
    """
    try:
        # Get parameters
        response_type = request.args.get('response_type')
        client_id = request.args.get('client_id')
        redirect_uri = request.args.get('redirect_uri')
        scope = request.args.get('scope', '').split()
        state = request.args.get('state')
        code_challenge = request.args.get('code_challenge')
        code_challenge_method = request.args.get('code_challenge_method')
        
        # Validate parameters
        if response_type != 'code':
            return jsonify({"error": "unsupported_response_type"}), 400
        
        if not client_id:
            return jsonify({"error": "invalid_request", "error_description": "client_id required"}), 400
        
        if not redirect_uri:
            return jsonify({"error": "invalid_request", "error_description": "redirect_uri required"}), 400
        
        # Validate client
        client = oauth_provider.get_client(client_id)
        if not client:
            return jsonify({"error": "invalid_client"}), 400
        
        # Validate redirect URI
        if redirect_uri not in client.redirect_uris:
            return jsonify({"error": "invalid_request", "error_description": "invalid redirect_uri"}), 400
        
        # Validate scope
        if not all(s in client.scope for s in scope):
            return jsonify({"error": "invalid_scope"}), 400
        
        # For development, auto-approve with default user
        user_id = "default_user"
        
        # Create authorization code
        auth_code = oauth_provider.create_authorization_code(
            client_id=client_id,
            user_id=user_id,
            redirect_uri=redirect_uri,
            scope=scope,
            code_challenge=code_challenge,
            code_challenge_method=code_challenge_method
        )
        
        # Build redirect URL
        params = {"code": auth_code}
        if state:
            params["state"] = state
        
        redirect_url = f"{redirect_uri}?{urlencode(params)}"
        
        logger.info(f"Authorization granted for client {client_id}, user {user_id}")
        return redirect(redirect_url)
        
    except Exception as e:
        logger.error(f"Authorization error: {e}")
        return jsonify({"error": "server_error"}), 500


@auth_bp.route('/token', methods=['POST'])
def token():
    """
    OAuth 2.0 token endpoint.
    
    Form parameters:
    - grant_type: "authorization_code", "client_credentials", "refresh_token", or device code
    - code: Authorization code (for authorization_code grant)
    - redirect_uri: Redirect URI (for authorization_code grant)
    - client_id: OAuth client ID
    - client_secret: OAuth client secret
    - refresh_token: Refresh token (for refresh_token grant)
    - scope: Requested scopes
    - code_verifier: PKCE code verifier
    """
    try:
        grant_type = request.form.get('grant_type')
        client_id = request.form.get('client_id')
        client_secret = request.form.get('client_secret')
        
        # Validate client
        if not oauth_provider.validate_client(client_id, client_secret):
            return jsonify({"error": "invalid_client"}), 401
        
        if grant_type == GrantType.AUTHORIZATION_CODE.value:
            return _handle_authorization_code_grant()
        elif grant_type == GrantType.CLIENT_CREDENTIALS.value:
            return _handle_client_credentials_grant()
        elif grant_type == GrantType.REFRESH_TOKEN.value:
            return _handle_refresh_token_grant()
        elif grant_type == "urn:ietf:params:oauth:grant-type:device_code":
            return _handle_device_code_grant()
        else:
            return jsonify({"error": "unsupported_grant_type"}), 400
            
    except Exception as e:
        logger.error(f"Token endpoint error: {e}")
        return jsonify({"error": "server_error"}), 500


@auth_bp.route('/device', methods=['POST'])
def device_authorization():
    """
    OAuth 2.0 device authorization endpoint.
    
    Form parameters:
    - client_id: OAuth client ID
    - scope: Requested scopes (space-separated)
    """
    try:
        client_id = request.form.get('client_id')
        scope = request.form.get('scope', '').split()
        
        # Validate client
        if not oauth_provider.validate_client(client_id):
            return jsonify({"error": "invalid_client"}), 400
        
        # Create device code
        device_code_obj = oauth_provider.create_device_code(client_id, scope)
        
        return jsonify({
            "device_code": device_code_obj.device_code,
            "user_code": device_code_obj.user_code,
            "verification_uri": device_code_obj.verification_uri,
            "verification_uri_complete": device_code_obj.verification_uri_complete,
            "expires_in": int(oauth_provider.device_code_lifetime.total_seconds()),
            "interval": device_code_obj.interval
        })
        
    except Exception as e:
        logger.error(f"Device authorization error: {e}")
        return jsonify({"error": "server_error"}), 500


@auth_bp.route('/device', methods=['GET'])
def device_verification():
    """Device verification page."""
    user_code = request.args.get('user_code', '')
    
    # Simple HTML form for device verification
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Device Verification - AI Coding Agent</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 500px; margin: 50px auto; padding: 20px; }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; }
            input[type="text"] { width: 100%; padding: 8px; font-size: 16px; }
            button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
            button:hover { background: #005a87; }
            .success { color: green; }
            .error { color: red; }
        </style>
    </head>
    <body>
        <h1>Device Verification</h1>
        <p>Enter the code displayed on your device:</p>
        
        <form method="post" action="/auth/device/verify">
            <div class="form-group">
                <label for="user_code">Device Code:</label>
                <input type="text" id="user_code" name="user_code" value="{{ user_code }}" required>
            </div>
            <button type="submit">Verify Device</button>
        </form>
    </body>
    </html>
    """
    
    return render_template_string(html, user_code=user_code)


@auth_bp.route('/device/verify', methods=['POST'])
def device_verify():
    """Verify device code."""
    try:
        user_code = request.form.get('user_code')
        if not user_code:
            return jsonify({"error": "user_code required"}), 400
        
        # For development, auto-approve with default user
        user_id = "default_user"
        
        success = oauth_provider.authorize_device_code(user_code, user_id)
        
        if success:
            return """
            <html>
            <body style="font-family: Arial, sans-serif; text-align: center; margin-top: 100px;">
                <h1 style="color: green;">✓ Device Authorized</h1>
                <p>Your device has been successfully authorized. You can now return to your application.</p>
            </body>
            </html>
            """
        else:
            return """
            <html>
            <body style="font-family: Arial, sans-serif; text-align: center; margin-top: 100px;">
                <h1 style="color: red;">✗ Authorization Failed</h1>
                <p>Invalid or expired device code. Please try again.</p>
                <a href="/auth/device">Try Again</a>
            </body>
            </html>
            """
            
    except Exception as e:
        logger.error(f"Device verification error: {e}")
        return jsonify({"error": "server_error"}), 500


@auth_bp.route('/userinfo', methods=['GET'])
@session_auth.require_auth(['read'])
def userinfo():
    """Get current user information."""
    try:
        user = session_auth.get_current_user()
        if not user:
            return jsonify({"error": "user_not_found"}), 404
        
        return jsonify({
            "sub": user.user_id,
            "username": user.username,
            "email": user.email,
            "name": user.full_name,
            "email_verified": True,
            "updated_at": user.last_login.isoformat() if user.last_login else None
        })
        
    except Exception as e:
        logger.error(f"Userinfo error: {e}")
        return jsonify({"error": "server_error"}), 500


@auth_bp.route('/revoke', methods=['POST'])
def revoke():
    """
    Token revocation endpoint.
    
    Form parameters:
    - token: Token to revoke
    - token_type_hint: "access_token" or "refresh_token"
    - client_id: OAuth client ID
    - client_secret: OAuth client secret
    """
    try:
        token_to_revoke = request.form.get('token')
        client_id = request.form.get('client_id')
        client_secret = request.form.get('client_secret')
        
        # Validate client
        if not oauth_provider.validate_client(client_id, client_secret):
            return jsonify({"error": "invalid_client"}), 401
        
        if not token_to_revoke:
            return jsonify({"error": "invalid_request"}), 400
        
        # Try to validate and revoke token
        claims = token_manager.validate_token(token_to_revoke)
        if claims:
            token_manager.revoke_token(claims.jti)
        
        # Always return success for security
        return '', 200
        
    except Exception as e:
        logger.error(f"Token revocation error: {e}")
        return '', 200  # Always return success for security


@auth_bp.route('/sessions', methods=['GET'])
@session_auth.require_auth(['read'])
def list_sessions():
    """List user sessions."""
    try:
        user_id = session_auth.get_current_user_id()
        sessions = session_auth.list_user_sessions(user_id)
        
        return jsonify({
            "sessions": sessions
        })
        
    except Exception as e:
        logger.error(f"List sessions error: {e}")
        return jsonify({"error": "server_error"}), 500


@auth_bp.route('/sessions/<token_id>', methods=['DELETE'])
@session_auth.require_auth(['write'])
def revoke_session(token_id: str):
    """Revoke specific session."""
    try:
        user_id = session_auth.get_current_user_id()
        success = session_auth.revoke_user_session(user_id, token_id)
        
        if success:
            return jsonify({"message": "Session revoked successfully"})
        else:
            return jsonify({"error": "session_not_found"}), 404
            
    except Exception as e:
        logger.error(f"Revoke session error: {e}")
        return jsonify({"error": "server_error"}), 500


def _handle_authorization_code_grant():
    """Handle authorization code grant."""
    code = request.form.get('code')
    redirect_uri = request.form.get('redirect_uri')
    client_id = request.form.get('client_id')
    client_secret = request.form.get('client_secret')
    code_verifier = request.form.get('code_verifier')
    
    if not all([code, redirect_uri, client_id]):
        return jsonify({"error": "invalid_request"}), 400
    
    try:
        access_token, refresh_token, expires_in = oauth_provider.exchange_authorization_code(
            code, client_id, client_secret, redirect_uri, code_verifier
        )
        
        return jsonify({
            "access_token": access_token,
            "token_type": "Bearer",
            "expires_in": expires_in,
            "refresh_token": refresh_token,
            "scope": "read write"
        })
        
    except ValueError as e:
        return jsonify({"error": "invalid_grant", "error_description": str(e)}), 400


def _handle_client_credentials_grant():
    """Handle client credentials grant."""
    client_id = request.form.get('client_id')
    scope = request.form.get('scope', 'read').split()
    
    # Create access token for client
    access_token = token_manager.create_access_token(client_id, client_id, scope)
    expires_in = int(token_manager.access_token_lifetime.total_seconds())
    
    return jsonify({
        "access_token": access_token,
        "token_type": "Bearer",
        "expires_in": expires_in,
        "scope": " ".join(scope)
    })


def _handle_refresh_token_grant():
    """Handle refresh token grant."""
    refresh_token_value = request.form.get('refresh_token')
    
    if not refresh_token_value:
        return jsonify({"error": "invalid_request"}), 400
    
    result = token_manager.refresh_access_token(refresh_token_value)
    if not result:
        return jsonify({"error": "invalid_grant"}), 400
    
    new_access_token, new_refresh_token = result
    expires_in = int(token_manager.access_token_lifetime.total_seconds())
    
    return jsonify({
        "access_token": new_access_token,
        "token_type": "Bearer",
        "expires_in": expires_in,
        "refresh_token": new_refresh_token
    })


def _handle_device_code_grant():
    """Handle device code grant."""
    device_code = request.form.get('device_code')
    client_id = request.form.get('client_id')
    
    if not all([device_code, client_id]):
        return jsonify({"error": "invalid_request"}), 400
    
    access_token, refresh_token, expires_in, error = oauth_provider.poll_device_code(device_code, client_id)
    
    if error:
        if error == "authorization_pending":
            return jsonify({"error": "authorization_pending"}), 400
        elif error == "expired_token":
            return jsonify({"error": "expired_token"}), 400
        else:
            return jsonify({"error": "invalid_grant"}), 400
    
    return jsonify({
        "access_token": access_token,
        "token_type": "Bearer",
        "expires_in": expires_in,
        "refresh_token": refresh_token
    })


# Health check for auth service
@auth_bp.route('/health', methods=['GET'])
def auth_health():
    """Health check for authentication service."""
    try:
        stats = session_auth.get_auth_stats()
        
        return jsonify({
            "status": "healthy",
            "stats": stats
        })
    except Exception as e:
        logger.error(f"Auth health check failed: {e}")
        return jsonify({"status": "unhealthy", "error": str(e)}), 500
