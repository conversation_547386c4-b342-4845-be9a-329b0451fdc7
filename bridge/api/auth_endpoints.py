"""
Enhanced Authentication API endpoints for OAuth 2.0 and JWT token management.
Provides secure authentication flows with multiple OAuth providers.
"""

import json
import logging
import secrets
import time
from datetime import datetime
from typing import Dict, Any, Optional
from flask import Blueprint, request, jsonify, redirect, render_template_string, session
from urllib.parse import urlencode, parse_qs

from bridge.auth.oauth_providers import oauth_manager, OAuthError
from bridge.auth.jwt_manager import jwt_manager
from bridge.auth.user_manager import user_manager, UserRole, AccountStatus
from bridge.auth.session_auth import session_auth

logger = logging.getLogger(__name__)

# Create blueprint for enhanced OAuth auth endpoints
oauth_auth_bp = Blueprint('oauth_auth', __name__, url_prefix='/api/auth')

# Store OAuth states temporarily (in production, use Redis)
oauth_states = {}


@oauth_auth_bp.route('/providers', methods=['GET'])
def get_oauth_providers():
    """Get available OAuth providers."""
    try:
        providers = oauth_manager.get_available_providers()

        provider_info = []
        for provider_name in providers:
            provider = oauth_manager.get_provider(provider_name)
            provider_info.append({
                "name": provider_name,
                "display_name": provider_name.title(),
                "available": True
            })

        return jsonify({
            "status": "success",
            "providers": provider_info
        })

    except Exception as e:
        logger.error(f"Get providers error: {e}")
        return jsonify({"error": "Failed to get providers"}), 500

@oauth_auth_bp.route('/oauth/<provider>/login', methods=['GET'])
def oauth_login(provider):
    """Initiate OAuth login flow."""
    try:
        oauth_provider = oauth_manager.get_provider(provider)
        if not oauth_provider:
            return jsonify({"error": f"Provider {provider} not available"}), 400

        # Generate state and PKCE challenge
        state = oauth_provider.generate_state()
        code_verifier, code_challenge = oauth_provider.generate_pkce_challenge()

        # Store state and code verifier
        oauth_states[state] = {
            "provider": provider,
            "code_verifier": code_verifier,
            "created_at": time.time(),
            "redirect_uri": request.args.get("redirect_uri", "")
        }

        # Generate authorization URL
        auth_url = oauth_provider.get_authorization_url(state, code_challenge)

        return jsonify({
            "status": "success",
            "auth_url": auth_url,
            "state": state
        })

    except Exception as e:
        logger.error(f"OAuth login error for {provider}: {e}")
        return jsonify({"error": "OAuth login failed"}), 500


@oauth_auth_bp.route('/oauth/<provider>/callback', methods=['GET'])
def oauth_callback(provider):
    """Handle OAuth callback."""
    try:
        code = request.args.get('code')
        state = request.args.get('state')
        error = request.args.get('error')

        if error:
            logger.warning(f"OAuth error for {provider}: {error}")
            return jsonify({"error": f"OAuth error: {error}"}), 400

        if not code or not state:
            return jsonify({"error": "Missing authorization code or state"}), 400

        # Validate state
        state_data = oauth_states.get(state)
        if not state_data or state_data["provider"] != provider:
            return jsonify({"error": "Invalid state parameter"}), 400

        # Check state expiration (5 minutes)
        if time.time() - state_data["created_at"] > 300:
            oauth_states.pop(state, None)
            return jsonify({"error": "State expired"}), 400

        oauth_provider = oauth_manager.get_provider(provider)
        if not oauth_provider:
            return jsonify({"error": f"Provider {provider} not available"}), 400

        # Exchange code for token
        oauth_token = oauth_provider.exchange_code_for_token(
            code, state_data["code_verifier"]
        )

        # Get user info
        oauth_user = oauth_provider.get_user_info(oauth_token)

        # Find or create user
        user = user_manager.get_user_by_oauth(oauth_user.provider, oauth_user.provider_id)

        if not user:
            # Check if user exists by email
            user = user_manager.get_user_by_email(oauth_user.email)
            if user:
                # Link OAuth account to existing user
                user_manager.link_oauth_account(user.user_id, oauth_user)
            else:
                # Create new user
                user = user_manager.create_user(
                    email=oauth_user.email,
                    name=oauth_user.name,
                    username=oauth_user.username,
                    oauth_user=oauth_user
                )

        # Check account status
        if user.status != AccountStatus.ACTIVE:
            return jsonify({"error": "Account is not active"}), 403

        # Create session
        session = user_manager.create_session(
            user_id=user.user_id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

        # Generate JWT tokens
        user_data = {
            "email": user.email,
            "name": user.name,
            "username": user.username,
            "role": user.role.value,
            "avatar_url": user.avatar_url
        }

        scopes = ["read", "write"]
        if user.role in [UserRole.ADMIN, UserRole.PREMIUM]:
            scopes.extend(["admin", "premium"])

        tokens = jwt_manager.generate_token_pair(
            user_id=user.user_id,
            user_data=user_data,
            session_id=session.session_id,
            ip_address=request.remote_addr,
            scopes=scopes
        )

        # Clean up state
        oauth_states.pop(state, None)

        # Handle redirect
        redirect_uri = state_data.get("redirect_uri")
        if redirect_uri:
            # Redirect to frontend with tokens
            params = urlencode({
                "access_token": tokens["access_token"],
                "refresh_token": tokens["refresh_token"],
                "expires_in": tokens["expires_in"]
            })
            return redirect(f"{redirect_uri}?{params}")

        return jsonify({
            "status": "success",
            "user": {
                "user_id": user.user_id,
                "email": user.email,
                "name": user.name,
                "username": user.username,
                "role": user.role.value,
                "avatar_url": user.avatar_url
            },
            "tokens": tokens
        })

    except OAuthError as e:
        logger.error(f"OAuth callback error for {provider}: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logger.error(f"OAuth callback error for {provider}: {e}")
        return jsonify({"error": "OAuth callback failed"}), 500


@oauth_auth_bp.route('/token/refresh', methods=['POST'])
def refresh_token():
    """Refresh access token using refresh token."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        refresh_token = data.get('refresh_token')
        if not refresh_token:
            return jsonify({"error": "Refresh token required"}), 400

        # Refresh token
        tokens = jwt_manager.refresh_access_token(refresh_token)
        if not tokens:
            return jsonify({"error": "Invalid or expired refresh token"}), 401

        return jsonify({
            "status": "success",
            "tokens": tokens
        })

    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        return jsonify({"error": "Token refresh failed"}), 500

@oauth_auth_bp.route('/token/revoke', methods=['POST'])
@session_auth.require_auth
def revoke_token():
    """Revoke access token."""
    try:
        data = request.get_json()
        token = data.get('token') if data else None

        # Get token from header if not in body
        if not token:
            auth_header = request.headers.get('Authorization', '')
            if auth_header.startswith('Bearer '):
                token = auth_header[7:]

        if not token:
            return jsonify({"error": "Token required"}), 400

        # Revoke token
        success = jwt_manager.revoke_token(token)

        return jsonify({
            "status": "success" if success else "error",
            "message": "Token revoked" if success else "Failed to revoke token"
        })

    except Exception as e:
        logger.error(f"Token revocation error: {e}")
        return jsonify({"error": "Token revocation failed"}), 500


# Removed old device endpoints - OAuth 2.0 flow handles authentication
# @oauth_auth_bp.route('/device', methods=['GET'])
# OAuth 2.0 device flow endpoints removed - using standard OAuth flow instead


@oauth_auth_bp.route('/user', methods=['GET'])
@session_auth.require_auth
def get_current_user():
    """Get current user information."""
    try:
        user_id = session_auth.get_current_user_id()
        user = user_manager.get_user(user_id)

        if not user:
            return jsonify({"error": "User not found"}), 404

        return jsonify({
            "status": "success",
            "user": {
                "user_id": user.user_id,
                "email": user.email,
                "name": user.name,
                "username": user.username,
                "role": user.role.value,
                "status": user.status.value,
                "avatar_url": user.avatar_url,
                "preferences": user.preferences.__dict__,
                "created_at": user.created_at,
                "last_login_at": user.last_login_at,
                "email_verified": user.email_verified,
                "mfa_enabled": user.mfa_enabled,
                "oauth_accounts": [
                    {
                        "provider": acc["provider"],
                        "username": acc.get("username"),
                        "linked_at": acc["linked_at"]
                    }
                    for acc in user.oauth_accounts
                ]
            }
        })

    except Exception as e:
        logger.error(f"Get user error: {e}")
        return jsonify({"error": "Failed to get user"}), 500


@oauth_auth_bp.route('/user/preferences', methods=['PUT'])
@session_auth.require_auth
def update_user_preferences():
    """Update user preferences."""
    try:
        user_id = session_auth.get_current_user_id()
        data = request.get_json()

        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Update user preferences
        success = user_manager.update_user(user_id, {"preferences": data})

        if success:
            return jsonify({
                "status": "success",
                "message": "Preferences updated"
            })
        else:
            return jsonify({"error": "Failed to update preferences"}), 500

    except Exception as e:
        logger.error(f"Update preferences error: {e}")
        return jsonify({"error": "Failed to update preferences"}), 500


@oauth_auth_bp.route('/stats', methods=['GET'])
@session_auth.require_auth
def get_auth_stats():
    """Get authentication system statistics."""
    try:
        user_id = session_auth.get_current_user_id()
        user = user_manager.get_user(user_id)

        # Only admins can see system stats
        if user.role != UserRole.ADMIN:
            return jsonify({"error": "Insufficient permissions"}), 403

        # Get system statistics
        user_stats = user_manager.get_system_stats()
        jwt_stats = jwt_manager.get_stats()

        return jsonify({
            "status": "success",
            "stats": {
                "users": user_stats,
                "tokens": jwt_stats,
                "oauth_providers": len(oauth_manager.get_available_providers())
            }
        })

    except Exception as e:
        logger.error(f"Get auth stats error: {e}")
        return jsonify({"error": "Failed to get stats"}), 500


# Legacy endpoint removed
# @oauth_auth_bp.route('/userinfo', methods=['GET'])
@session_auth.require_auth(['read'])
def userinfo():
    """Get current user information."""
    try:
        user = session_auth.get_current_user()
        if not user:
            return jsonify({"error": "user_not_found"}), 404
        
        return jsonify({
            "sub": user.user_id,
            "username": user.username,
            "email": user.email,
            "name": user.full_name,
            "email_verified": True,
            "updated_at": user.last_login.isoformat() if user.last_login else None
        })
        
    except Exception as e:
        logger.error(f"Userinfo error: {e}")
        return jsonify({"error": "server_error"}), 500


# Legacy endpoint removed
# @oauth_auth_bp.route('/revoke', methods=['POST'])
def revoke():
    """
    Token revocation endpoint.
    
    Form parameters:
    - token: Token to revoke
    - token_type_hint: "access_token" or "refresh_token"
    - client_id: OAuth client ID
    - client_secret: OAuth client secret
    """
    try:
        token_to_revoke = request.form.get('token')
        client_id = request.form.get('client_id')
        client_secret = request.form.get('client_secret')
        
        # Note: Client validation would be implemented here
        # For now, we'll skip client validation in the enhanced OAuth system
        
        if not token_to_revoke:
            return jsonify({"error": "invalid_request"}), 400
        
        # Try to validate and revoke token using JWT manager
        claims = jwt_manager.validate_token(token_to_revoke)
        if claims:
            jwt_manager.revoke_token(token_to_revoke)
        
        # Always return success for security
        return '', 200
        
    except Exception as e:
        logger.error(f"Token revocation error: {e}")
        return '', 200  # Always return success for security


# Legacy endpoint removed
# @oauth_auth_bp.route('/sessions', methods=['GET'])
@session_auth.require_auth(['read'])
def list_sessions():
    """List user sessions."""
    try:
        user_id = session_auth.get_current_user_id()
        sessions = session_auth.list_user_sessions(user_id)
        
        return jsonify({
            "sessions": sessions
        })
        
    except Exception as e:
        logger.error(f"List sessions error: {e}")
        return jsonify({"error": "server_error"}), 500


# Legacy endpoint removed
# @oauth_auth_bp.route('/sessions/<token_id>', methods=['DELETE'])
@session_auth.require_auth(['write'])
def revoke_session(token_id: str):
    """Revoke specific session."""
    try:
        user_id = session_auth.get_current_user_id()
        success = session_auth.revoke_user_session(user_id, token_id)
        
        if success:
            return jsonify({"message": "Session revoked successfully"})
        else:
            return jsonify({"error": "session_not_found"}), 404
            
    except Exception as e:
        logger.error(f"Revoke session error: {e}")
        return jsonify({"error": "server_error"}), 500


# Health check for enhanced auth service
@oauth_auth_bp.route('/health', methods=['GET'])
def auth_health():
    """Health check for enhanced authentication service."""
    try:
        # Get system statistics
        user_stats = user_manager.get_system_stats()
        jwt_stats = jwt_manager.get_stats()
        oauth_providers = oauth_manager.get_available_providers()

        return jsonify({
            "status": "healthy",
            "oauth_providers": len(oauth_providers),
            "available_providers": oauth_providers,
            "user_stats": user_stats,
            "jwt_stats": jwt_stats,
            "features": {
                "oauth_authentication": True,
                "jwt_tokens": True,
                "user_management": True,
                "session_management": True,
                "multi_provider_support": True
            }
        })
    except Exception as e:
        logger.error(f"Auth health check failed: {e}")
        return jsonify({"status": "unhealthy", "error": str(e)}), 500
