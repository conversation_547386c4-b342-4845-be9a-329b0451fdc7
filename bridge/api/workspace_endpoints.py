"""
API endpoints for enhanced workspace features.
Provides project analysis, file navigation, refactoring, and code quality endpoints.
"""

import json
import logging
import os
from typing import Dict, Any, Optional
from flask import Blueprint, request, jsonify

from bridge.workspace.project_analyzer import project_analyzer
from bridge.workspace.file_navigator import file_navigator
from bridge.workspace.refactoring_assistant import refactoring_assistant
from bridge.workspace.code_quality import code_quality_analyzer
from bridge.auth.session_auth import session_auth

logger = logging.getLogger(__name__)

# Create blueprint for workspace endpoints
workspace_bp = Blueprint('workspace', __name__, url_prefix='/api/workspace')


@workspace_bp.route('/projects/analyze', methods=['POST'])
@session_auth.optional_auth
def analyze_project():
    """
    Analyze a project for comprehensive insights.
    
    Expected JSON payload:
    {
        "project_path": "/path/to/project",
        "force_refresh": false
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        project_path = data.get('project_path')
        if not project_path:
            return jsonify({"error": "project_path is required"}), 400
        
        if not os.path.exists(project_path):
            return jsonify({"error": "Project path does not exist"}), 400
        
        force_refresh = data.get('force_refresh', False)
        
        # Analyze project
        analysis = project_analyzer.analyze_project(project_path, force_refresh)
        
        return jsonify({
            "status": "success",
            "analysis": analysis.to_dict()
        })
        
    except Exception as e:
        logger.error(f"Project analysis error: {e}")
        return jsonify({"error": str(e)}), 500


@workspace_bp.route('/projects/<path:project_path>/structure', methods=['GET'])
@session_auth.optional_auth
def get_project_structure(project_path: str):
    """Get project directory structure."""
    try:
        # Decode path
        project_path = project_path.replace('%2F', '/')
        
        if not os.path.exists(project_path):
            return jsonify({"error": "Project path does not exist"}), 400
        
        analysis = project_analyzer.get_project_analysis(project_path)
        if not analysis:
            analysis = project_analyzer.analyze_project(project_path)
        
        return jsonify({
            "status": "success",
            "structure": analysis.structure.to_dict()
        })
        
    except Exception as e:
        logger.error(f"Get project structure error: {e}")
        return jsonify({"error": str(e)}), 500


@workspace_bp.route('/navigation/index', methods=['POST'])
@session_auth.optional_auth
def index_project():
    """
    Index a project for navigation.
    
    Expected JSON payload:
    {
        "project_path": "/path/to/project"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        project_path = data.get('project_path')
        if not project_path:
            return jsonify({"error": "project_path is required"}), 400
        
        success = file_navigator.index_project(project_path)
        
        if success:
            return jsonify({
                "status": "success",
                "message": "Project indexed successfully"
            })
        else:
            return jsonify({"error": "Project indexing failed"}), 500
        
    except Exception as e:
        logger.error(f"Project indexing error: {e}")
        return jsonify({"error": str(e)}), 500


@workspace_bp.route('/navigation/search/files', methods=['POST'])
@session_auth.optional_auth
def search_files():
    """
    Search for files in a project.
    
    Expected JSON payload:
    {
        "project_path": "/path/to/project",
        "query": "search_term",
        "max_results": 20
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        project_path = data.get('project_path')
        query = data.get('query')
        
        if not project_path or not query:
            return jsonify({"error": "project_path and query are required"}), 400
        
        max_results = data.get('max_results', 20)
        
        matches = file_navigator.search_files(project_path, query, max_results)
        
        return jsonify({
            "status": "success",
            "matches": [match.to_dict() for match in matches]
        })
        
    except Exception as e:
        logger.error(f"File search error: {e}")
        return jsonify({"error": str(e)}), 500


@workspace_bp.route('/navigation/search/symbols', methods=['POST'])
@session_auth.optional_auth
def search_symbols():
    """
    Search for symbols in a project.
    
    Expected JSON payload:
    {
        "project_path": "/path/to/project",
        "query": "symbol_name",
        "symbol_type": "function",  // optional
        "max_results": 20
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        project_path = data.get('project_path')
        query = data.get('query')
        
        if not project_path or not query:
            return jsonify({"error": "project_path and query are required"}), 400
        
        symbol_type = data.get('symbol_type')
        max_results = data.get('max_results', 20)
        
        matches = file_navigator.search_symbols(project_path, query, symbol_type, max_results)
        
        return jsonify({
            "status": "success",
            "matches": [match.to_dict() for match in matches]
        })
        
    except Exception as e:
        logger.error(f"Symbol search error: {e}")
        return jsonify({"error": str(e)}), 500


@workspace_bp.route('/navigation/definition', methods=['POST'])
@session_auth.optional_auth
def find_definition():
    """
    Find definition of symbol at cursor position.
    
    Expected JSON payload:
    {
        "project_path": "/path/to/project",
        "file_path": "relative/path/to/file.py",
        "line": 10,
        "column": 5
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        project_path = data.get('project_path')
        file_path = data.get('file_path')
        line = data.get('line')
        column = data.get('column')
        
        if not all([project_path, file_path, line is not None, column is not None]):
            return jsonify({"error": "project_path, file_path, line, and column are required"}), 400
        
        suggestion = file_navigator.find_definition(project_path, file_path, line, column)
        
        return jsonify({
            "status": "success",
            "definition": suggestion.to_dict() if suggestion else None
        })
        
    except Exception as e:
        logger.error(f"Find definition error: {e}")
        return jsonify({"error": str(e)}), 500


@workspace_bp.route('/navigation/references', methods=['POST'])
@session_auth.optional_auth
def find_references():
    """
    Find references to a symbol.
    
    Expected JSON payload:
    {
        "project_path": "/path/to/project",
        "symbol_name": "function_name",
        "symbol_type": "function"  // optional
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        project_path = data.get('project_path')
        symbol_name = data.get('symbol_name')
        
        if not project_path or not symbol_name:
            return jsonify({"error": "project_path and symbol_name are required"}), 400
        
        symbol_type = data.get('symbol_type')
        
        references = file_navigator.find_references(project_path, symbol_name, symbol_type)
        
        return jsonify({
            "status": "success",
            "references": [ref.to_dict() for ref in references]
        })
        
    except Exception as e:
        logger.error(f"Find references error: {e}")
        return jsonify({"error": str(e)}), 500


@workspace_bp.route('/navigation/suggestions', methods=['POST'])
@session_auth.optional_auth
def get_navigation_suggestions():
    """
    Get navigation suggestions for current context.
    
    Expected JSON payload:
    {
        "project_path": "/path/to/project",
        "file_path": "relative/path/to/file.py",
        "context": "optional context"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        project_path = data.get('project_path')
        file_path = data.get('file_path')
        
        if not project_path or not file_path:
            return jsonify({"error": "project_path and file_path are required"}), 400
        
        context = data.get('context')
        
        suggestions = file_navigator.get_navigation_suggestions(project_path, file_path, context)
        
        return jsonify({
            "status": "success",
            "suggestions": [suggestion.to_dict() for suggestion in suggestions]
        })
        
    except Exception as e:
        logger.error(f"Navigation suggestions error: {e}")
        return jsonify({"error": str(e)}), 500


@workspace_bp.route('/refactoring/analyze', methods=['POST'])
@session_auth.optional_auth
def analyze_refactoring():
    """
    Analyze file for refactoring opportunities.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "content": "file content",
        "language": "python"  // optional
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        file_path = data.get('file_path')
        content = data.get('content')
        
        if not file_path or not content:
            return jsonify({"error": "file_path and content are required"}), 400
        
        language = data.get('language')
        
        report = refactoring_assistant.analyze_file(file_path, content, language)
        
        return jsonify({
            "status": "success",
            "report": report.to_dict()
        })
        
    except Exception as e:
        logger.error(f"Refactoring analysis error: {e}")
        return jsonify({"error": str(e)}), 500


@workspace_bp.route('/refactoring/rules', methods=['GET'])
@session_auth.optional_auth
def get_refactoring_rules():
    """Get available refactoring rules."""
    try:
        language = request.args.get('language')
        
        rules = refactoring_assistant.get_refactoring_rules(language)
        
        return jsonify({
            "status": "success",
            "rules": [rule.to_dict() for rule in rules]
        })
        
    except Exception as e:
        logger.error(f"Get refactoring rules error: {e}")
        return jsonify({"error": str(e)}), 500


@workspace_bp.route('/quality/analyze', methods=['POST'])
@session_auth.optional_auth
def analyze_code_quality():
    """
    Analyze code quality.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "content": "file content",
        "language": "python"  // optional
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        file_path = data.get('file_path')
        content = data.get('content')
        
        if not file_path or not content:
            return jsonify({"error": "file_path and content are required"}), 400
        
        language = data.get('language')
        
        report = code_quality_analyzer.analyze_file(file_path, content, language)
        
        return jsonify({
            "status": "success",
            "report": report.to_dict()
        })
        
    except Exception as e:
        logger.error(f"Code quality analysis error: {e}")
        return jsonify({"error": str(e)}), 500


@workspace_bp.route('/outline', methods=['POST'])
@session_auth.optional_auth
def get_file_outline():
    """
    Get file outline with symbols.
    
    Expected JSON payload:
    {
        "project_path": "/path/to/project",
        "file_path": "relative/path/to/file.py"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        project_path = data.get('project_path')
        file_path = data.get('file_path')
        
        if not project_path or not file_path:
            return jsonify({"error": "project_path and file_path are required"}), 400
        
        outline = file_navigator.get_file_outline(project_path, file_path)
        
        return jsonify({
            "status": "success",
            "outline": outline
        })
        
    except Exception as e:
        logger.error(f"File outline error: {e}")
        return jsonify({"error": str(e)}), 500


# Health check for workspace service
@workspace_bp.route('/health', methods=['GET'])
def workspace_health():
    """Health check for workspace service."""
    try:
        project_stats = project_analyzer.get_project_stats()
        navigation_stats = file_navigator.get_navigation_stats()
        
        return jsonify({
            "status": "healthy",
            "project_stats": project_stats,
            "navigation_stats": navigation_stats
        })
    except Exception as e:
        logger.error(f"Workspace health check failed: {e}")
        return jsonify({"status": "unhealthy", "error": str(e)}), 500
