"""
API endpoints for file operations.
Provides comprehensive file management capabilities for the AI Coding Agent.
"""

import json
import logging
import os
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional
from flask import Blueprint, request, jsonify

from bridge.auth.session_auth import session_auth

logger = logging.getLogger(__name__)

# Create blueprint for file endpoints
file_bp = Blueprint('files', __name__, url_prefix='/api/files')


def _is_safe_path(file_path: str) -> bool:
    """
    Check if the file path is safe to access.
    Prevents access to system files and directories outside the workspace.
    """
    # Convert to absolute path
    abs_path = os.path.abspath(file_path)
    
    # Define forbidden paths
    forbidden_paths = [
        '/etc', '/bin', '/sbin', '/usr/bin', '/usr/sbin',
        '/root', '/boot', '/dev', '/proc', '/sys'
    ]
    
    # Check if path starts with any forbidden path
    for forbidden in forbidden_paths:
        if abs_path.startswith(forbidden):
            return False
    
    # Allow access to user directories and common workspace locations
    allowed_prefixes = [
        os.path.expanduser('~'),  # User home directory
        '/tmp',                   # Temporary directory
        '/var/tmp',              # Temporary directory
        '/workspace',            # Common workspace directory
        '/project',              # Common project directory
        '/code',                 # Common code directory
    ]
    
    # Check if path starts with any allowed prefix
    for allowed in allowed_prefixes:
        if abs_path.startswith(allowed):
            return True
    
    # Allow relative paths within current working directory
    cwd = os.getcwd()
    if abs_path.startswith(cwd):
        return True
    
    return False


@file_bp.route('/read', methods=['POST'])
@session_auth.optional_auth
def read_file():
    """
    Read file content.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "encoding": "utf-8"  // optional, defaults to utf-8
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        file_path = data.get('file_path')
        if not file_path:
            return jsonify({"error": "file_path is required"}), 400
        
        # Security check - ensure path is within allowed directories
        file_path = os.path.abspath(file_path)
        if not _is_safe_path(file_path):
            return jsonify({"error": "Access denied to this path"}), 403
        
        if not os.path.exists(file_path):
            return jsonify({"error": "File not found"}), 404
        
        if not os.path.isfile(file_path):
            return jsonify({"error": "Path is not a file"}), 400
        
        encoding = data.get('encoding', 'utf-8')
        
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            # Get file stats
            stat = os.stat(file_path)
            
            return jsonify({
                "status": "success",
                "content": content,
                "file_path": file_path,
                "size": stat.st_size,
                "modified": stat.st_mtime,
                "encoding": encoding
            })
            
        except UnicodeDecodeError:
            return jsonify({"error": f"Cannot decode file with {encoding} encoding"}), 400
        except PermissionError:
            return jsonify({"error": "Permission denied"}), 403
            
    except Exception as e:
        logger.error(f"File read error: {e}")
        return jsonify({"error": str(e)}), 500


@file_bp.route('/write', methods=['POST'])
@session_auth.optional_auth
def write_file():
    """
    Write content to a file.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "content": "file content",
        "encoding": "utf-8",  // optional, defaults to utf-8
        "create_dirs": true   // optional, create parent directories if needed
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        file_path = data.get('file_path')
        content = data.get('content')
        
        if not file_path:
            return jsonify({"error": "file_path is required"}), 400
        
        if content is None:
            return jsonify({"error": "content is required"}), 400
        
        # Security check - ensure path is within allowed directories
        file_path = os.path.abspath(file_path)
        if not _is_safe_path(file_path):
            return jsonify({"error": "Access denied to this path"}), 403
        
        encoding = data.get('encoding', 'utf-8')
        create_dirs = data.get('create_dirs', False)
        
        # Create parent directories if requested
        if create_dirs:
            parent_dir = os.path.dirname(file_path)
            if parent_dir and not os.path.exists(parent_dir):
                os.makedirs(parent_dir, exist_ok=True)
        
        try:
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            # Get file stats
            stat = os.stat(file_path)
            
            return jsonify({
                "status": "success",
                "file_path": file_path,
                "size": stat.st_size,
                "modified": stat.st_mtime,
                "message": "File written successfully"
            })
            
        except PermissionError:
            return jsonify({"error": "Permission denied"}), 403
        except OSError as e:
            return jsonify({"error": f"OS error: {str(e)}"}), 500
            
    except Exception as e:
        logger.error(f"File write error: {e}")
        return jsonify({"error": str(e)}), 500


@file_bp.route('/create', methods=['POST'])
@session_auth.optional_auth
def create_file():
    """
    Create a new file.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "content": "initial content",  // optional, defaults to empty
        "encoding": "utf-8",           // optional, defaults to utf-8
        "create_dirs": true,           // optional, create parent directories if needed
        "overwrite": false             // optional, whether to overwrite existing file
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        file_path = data.get('file_path')
        if not file_path:
            return jsonify({"error": "file_path is required"}), 400
        
        # Security check - ensure path is within allowed directories
        file_path = os.path.abspath(file_path)
        if not _is_safe_path(file_path):
            return jsonify({"error": "Access denied to this path"}), 403
        
        content = data.get('content', '')
        encoding = data.get('encoding', 'utf-8')
        create_dirs = data.get('create_dirs', False)
        overwrite = data.get('overwrite', False)
        
        # Check if file already exists
        if os.path.exists(file_path) and not overwrite:
            return jsonify({"error": "File already exists"}), 409
        
        # Create parent directories if requested
        if create_dirs:
            parent_dir = os.path.dirname(file_path)
            if parent_dir and not os.path.exists(parent_dir):
                os.makedirs(parent_dir, exist_ok=True)
        
        try:
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            # Get file stats
            stat = os.stat(file_path)
            
            return jsonify({
                "status": "success",
                "file_path": file_path,
                "size": stat.st_size,
                "modified": stat.st_mtime,
                "message": "File created successfully"
            })
            
        except PermissionError:
            return jsonify({"error": "Permission denied"}), 403
        except OSError as e:
            return jsonify({"error": f"OS error: {str(e)}"}), 500
            
    except Exception as e:
        logger.error(f"File create error: {e}")
        return jsonify({"error": str(e)}), 500


@file_bp.route('/delete', methods=['POST'])
@session_auth.optional_auth
def delete_file():
    """
    Delete a file or directory.

    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "recursive": false  // optional, for directories
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        file_path = data.get('file_path')
        if not file_path:
            return jsonify({"error": "file_path is required"}), 400

        # Security check - ensure path is within allowed directories
        file_path = os.path.abspath(file_path)
        if not _is_safe_path(file_path):
            return jsonify({"error": "Access denied to this path"}), 403

        if not os.path.exists(file_path):
            return jsonify({"error": "File or directory not found"}), 404

        recursive = data.get('recursive', False)

        try:
            if os.path.isfile(file_path):
                os.remove(file_path)
                message = "File deleted successfully"
            elif os.path.isdir(file_path):
                if recursive:
                    shutil.rmtree(file_path)
                    message = "Directory deleted successfully"
                else:
                    os.rmdir(file_path)  # Only works for empty directories
                    message = "Directory deleted successfully"
            else:
                return jsonify({"error": "Path is neither a file nor a directory"}), 400

            return jsonify({
                "status": "success",
                "file_path": file_path,
                "message": message
            })

        except PermissionError:
            return jsonify({"error": "Permission denied"}), 403
        except OSError as e:
            if e.errno == 39:  # Directory not empty
                return jsonify({"error": "Directory not empty. Use recursive=true to delete."}), 400
            return jsonify({"error": f"OS error: {str(e)}"}), 500

    except Exception as e:
        logger.error(f"File delete error: {e}")
        return jsonify({"error": str(e)}), 500


@file_bp.route('/rename', methods=['POST'])
@session_auth.optional_auth
def rename_file():
    """
    Rename or move a file/directory.

    Expected JSON payload:
    {
        "old_path": "/path/to/old_file.py",
        "new_path": "/path/to/new_file.py",
        "create_dirs": false  // optional, create parent directories if needed
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        old_path = data.get('old_path')
        new_path = data.get('new_path')

        if not old_path or not new_path:
            return jsonify({"error": "old_path and new_path are required"}), 400

        # Security check - ensure both paths are within allowed directories
        old_path = os.path.abspath(old_path)
        new_path = os.path.abspath(new_path)

        if not _is_safe_path(old_path) or not _is_safe_path(new_path):
            return jsonify({"error": "Access denied to one or both paths"}), 403

        if not os.path.exists(old_path):
            return jsonify({"error": "Source file or directory not found"}), 404

        if os.path.exists(new_path):
            return jsonify({"error": "Destination already exists"}), 409

        create_dirs = data.get('create_dirs', False)

        # Create parent directories if requested
        if create_dirs:
            parent_dir = os.path.dirname(new_path)
            if parent_dir and not os.path.exists(parent_dir):
                os.makedirs(parent_dir, exist_ok=True)

        try:
            shutil.move(old_path, new_path)

            # Get file stats
            stat = os.stat(new_path)

            return jsonify({
                "status": "success",
                "old_path": old_path,
                "new_path": new_path,
                "size": stat.st_size,
                "modified": stat.st_mtime,
                "message": "File renamed/moved successfully"
            })

        except PermissionError:
            return jsonify({"error": "Permission denied"}), 403
        except OSError as e:
            return jsonify({"error": f"OS error: {str(e)}"}), 500

    except Exception as e:
        logger.error(f"File rename error: {e}")
        return jsonify({"error": str(e)}), 500


@file_bp.route('/list', methods=['POST'])
@session_auth.optional_auth
def list_directory():
    """
    List directory contents.

    Expected JSON payload:
    {
        "directory_path": "/path/to/directory",
        "include_hidden": false,  // optional, include hidden files
        "recursive": false,       // optional, recursive listing
        "max_depth": 3           // optional, maximum recursion depth
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        directory_path = data.get('directory_path')
        if not directory_path:
            return jsonify({"error": "directory_path is required"}), 400

        # Security check - ensure path is within allowed directories
        directory_path = os.path.abspath(directory_path)
        if not _is_safe_path(directory_path):
            return jsonify({"error": "Access denied to this path"}), 403

        if not os.path.exists(directory_path):
            return jsonify({"error": "Directory not found"}), 404

        if not os.path.isdir(directory_path):
            return jsonify({"error": "Path is not a directory"}), 400

        include_hidden = data.get('include_hidden', False)
        recursive = data.get('recursive', False)
        max_depth = data.get('max_depth', 3)

        def _list_directory_recursive(path: str, current_depth: int = 0) -> List[Dict[str, Any]]:
            items = []

            if current_depth >= max_depth:
                return items

            try:
                for item_name in os.listdir(path):
                    if not include_hidden and item_name.startswith('.'):
                        continue

                    item_path = os.path.join(path, item_name)

                    try:
                        stat = os.stat(item_path)
                        is_dir = os.path.isdir(item_path)

                        item_info = {
                            "name": item_name,
                            "path": item_path,
                            "type": "directory" if is_dir else "file",
                            "size": stat.st_size if not is_dir else None,
                            "modified": stat.st_mtime,
                            "permissions": oct(stat.st_mode)[-3:]
                        }

                        if recursive and is_dir:
                            item_info["children"] = _list_directory_recursive(item_path, current_depth + 1)

                        items.append(item_info)

                    except (PermissionError, OSError):
                        # Skip items we can't access
                        continue

            except PermissionError:
                pass

            return sorted(items, key=lambda x: (x["type"] == "file", x["name"].lower()))

        items = _list_directory_recursive(directory_path)

        return jsonify({
            "status": "success",
            "directory_path": directory_path,
            "items": items,
            "count": len(items)
        })

    except Exception as e:
        logger.error(f"Directory list error: {e}")
        return jsonify({"error": str(e)}), 500
