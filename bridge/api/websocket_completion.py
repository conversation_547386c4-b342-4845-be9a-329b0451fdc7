"""
WebSocket completion handler for real-time code completion streaming.
Provides optimized WebSocket communication for VS Code completion requests.
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, List, Optional, AsyncGenerator
from flask_socketio import SocketIO, emit, disconnect
from flask import request

from bridge.integrations.swe_tool_proxy import swe_tool_proxy
from bridge.core.tool_security import tool_security
from bridge.api.completion_endpoints import (
    get_intelligent_completions,
    get_swe_tool_completions,
    get_language_completions,
    get_context_completions
)

logger = logging.getLogger(__name__)

class CompletionWebSocketHandler:
    """WebSocket handler for real-time completion streaming."""
    
    def __init__(self, socketio: SocketIO):
        self.socketio = socketio
        self.active_requests: Dict[str, Dict[str, Any]] = {}
        self.client_sessions: Dict[str, Dict[str, Any]] = {}
        
        # Register WebSocket event handlers
        self.register_handlers()
    
    def register_handlers(self):
        """Register WebSocket event handlers."""
        
        @self.socketio.on('connect', namespace='/ws/completion')
        def handle_connect():
            """Handle client connection."""
            client_id = request.sid
            self.client_sessions[client_id] = {
                'connected_at': time.time(),
                'requests_count': 0,
                'last_activity': time.time()
            }
            
            logger.info(f"Completion WebSocket client connected: {client_id}")
            
            # Send connection status
            emit('connection_status', {
                'type': 'connection_status',
                'id': f'conn_{int(time.time())}',
                'status': 'connected',
                'server_info': self.get_server_info(),
                'timestamp': time.time() * 1000
            })
        
        @self.socketio.on('disconnect', namespace='/ws/completion')
        def handle_disconnect():
            """Handle client disconnection."""
            client_id = request.sid
            
            # Cancel any active requests for this client
            requests_to_cancel = [
                req_id for req_id, req_info in self.active_requests.items()
                if req_info.get('client_id') == client_id
            ]
            
            for req_id in requests_to_cancel:
                self.cancel_request(req_id)
            
            # Remove client session
            if client_id in self.client_sessions:
                del self.client_sessions[client_id]
            
            logger.info(f"Completion WebSocket client disconnected: {client_id}")
        
        @self.socketio.on('completion_request', namespace='/ws/completion')
        def handle_completion_request(data):
            """Handle completion request."""
            client_id = request.sid
            
            try:
                # Validate message
                if not self.validate_completion_request(data):
                    emit('completion_error', {
                        'type': 'completion_error',
                        'id': data.get('id', 'unknown'),
                        'error': {
                            'code': 'INVALID_REQUEST',
                            'message': 'Invalid completion request format',
                            'recoverable': False
                        },
                        'timestamp': time.time() * 1000
                    })
                    return
                
                request_id = data['id']
                context = data['context']
                options = data.get('options', {})
                
                # Security validation
                violations = tool_security.validate_completion_context(context)
                if violations:
                    emit('completion_error', {
                        'type': 'completion_error',
                        'id': request_id,
                        'error': {
                            'code': 'SECURITY_VIOLATION',
                            'message': 'Security violations detected',
                            'details': [str(v) for v in violations],
                            'recoverable': False
                        },
                        'timestamp': time.time() * 1000
                    })
                    return
                
                # Update client session
                if client_id in self.client_sessions:
                    self.client_sessions[client_id]['requests_count'] += 1
                    self.client_sessions[client_id]['last_activity'] = time.time()
                
                # Process completion request
                self.process_completion_request(client_id, request_id, context, options)
                
            except Exception as e:
                logger.error(f"Error handling completion request: {e}")
                emit('completion_error', {
                    'type': 'completion_error',
                    'id': data.get('id', 'unknown'),
                    'error': {
                        'code': 'INTERNAL_ERROR',
                        'message': str(e),
                        'recoverable': True,
                        'retry_after_ms': 1000
                    },
                    'timestamp': time.time() * 1000
                })
        
        @self.socketio.on('completion_cancel', namespace='/ws/completion')
        def handle_completion_cancel(data):
            """Handle completion cancellation."""
            request_id = data.get('id')
            if request_id:
                self.cancel_request(request_id)
        
        @self.socketio.on('health_check', namespace='/ws/completion')
        def handle_health_check(data):
            """Handle health check request."""
            emit('health_response', {
                'type': 'health_response',
                'id': data.get('id', f'health_{int(time.time())}'),
                'status': 'healthy',
                'server_info': self.get_server_info(),
                'performance_stats': self.get_performance_stats(),
                'timestamp': time.time() * 1000
            })
    
    def process_completion_request(self, client_id: str, request_id: str, context: Dict[str, Any], options: Dict[str, Any]):
        """Process completion request with streaming support."""
        
        # Store active request
        self.active_requests[request_id] = {
            'client_id': client_id,
            'start_time': time.time(),
            'context': context,
            'options': options,
            'cancelled': False
        }
        
        # Start async processing
        self.socketio.start_background_task(
            self._process_completion_async,
            client_id, request_id, context, options
        )
    
    async def _process_completion_async(self, client_id: str, request_id: str, context: Dict[str, Any], options: Dict[str, Any]):
        """Async completion processing with streaming."""
        
        try:
            start_time = time.time()
            streaming = options.get('streaming', True)
            use_swe_tools = options.get('use_swe_tools', True)
            
            # Initialize progress tracking
            total_stages = 5
            current_stage = 0
            
            def emit_progress(stage: str, completions: List[Dict[str, Any]] = None):
                nonlocal current_stage
                current_stage += 1
                
                if streaming and not self.is_request_cancelled(request_id):
                    self.socketio.emit('completion_chunk', {
                        'type': 'completion_chunk',
                        'id': request_id,
                        'completions': completions or [],
                        'partial': True,
                        'progress': {
                            'current': current_stage,
                            'total': total_stages,
                            'percentage': (current_stage / total_stages) * 100,
                            'estimated_remaining_ms': max(0, (time.time() - start_time) * (total_stages - current_stage) / current_stage * 1000) if current_stage > 0 else None
                        },
                        'stage': stage,
                        'timestamp': time.time() * 1000
                    }, namespace='/ws/completion', room=client_id)
            
            # Stage 1: Context Analysis
            emit_progress('context_analysis')
            if self.is_request_cancelled(request_id):
                return
            
            context_start = time.time()
            # Perform context analysis here
            await asyncio.sleep(0.05)  # Simulate processing time
            context_time = (time.time() - context_start) * 1000
            
            # Stage 2: SWE Tools Execution
            emit_progress('swe_tools_execution')
            if self.is_request_cancelled(request_id):
                return
            
            swe_start = time.time()
            swe_completions = []
            tools_used = []
            
            if use_swe_tools and swe_tool_proxy.tools_loaded:
                swe_completions = get_swe_tool_completions(context)
                tools_used = ['filemap', 'search_file'] if swe_completions else []
                
                # Emit partial results if streaming
                if streaming and swe_completions:
                    emit_progress('swe_tools_execution', swe_completions[:3])
            
            swe_time = (time.time() - swe_start) * 1000
            
            # Stage 3: AI Processing
            emit_progress('ai_processing')
            if self.is_request_cancelled(request_id):
                return
            
            ai_start = time.time()
            language = context.get('language', 'text')
            cursor_line = context.get('cursor_line', 0)
            cursor_column = context.get('cursor_column', 0)
            content = context.get('content', '')
            
            lines = content.split('\n')
            current_line = lines[cursor_line] if cursor_line < len(lines) else ''
            before_cursor = current_line[:cursor_column]
            
            # Get language-specific completions
            lang_completions = get_language_completions(language, before_cursor, context)
            
            # Get context-aware completions
            context_completions = get_context_completions(context)
            
            ai_time = (time.time() - ai_start) * 1000
            
            # Stage 4: Result Formatting
            emit_progress('result_formatting')
            if self.is_request_cancelled(request_id):
                return
            
            format_start = time.time()
            
            # Combine all completions
            all_completions = []
            all_completions.extend(swe_completions)
            all_completions.extend(lang_completions)
            all_completions.extend(context_completions)
            
            # Remove duplicates and limit results
            unique_completions = self.remove_duplicate_completions(all_completions)
            max_completions = options.get('max_completions', 5)
            final_completions = unique_completions[:max_completions]
            
            format_time = (time.time() - format_start) * 1000
            
            # Stage 5: Complete
            total_time = (time.time() - start_time) * 1000
            
            # Check if request was cancelled
            if self.is_request_cancelled(request_id):
                return
            
            # Emit final result
            self.socketio.emit('completion_complete', {
                'type': 'completion_complete',
                'id': request_id,
                'completions': final_completions,
                'metadata': {
                    'response_time_ms': total_time,
                    'tools_used': tools_used,
                    'cache_hit': False,  # TODO: Implement cache checking
                    'context_analyzed': True,
                    'performance_metrics': {
                        'context_analysis_ms': context_time,
                        'swe_tools_execution_ms': swe_time,
                        'ai_processing_ms': ai_time,
                        'result_formatting_ms': format_time,
                        'total_processing_ms': total_time,
                        'memory_usage_mb': 0,  # TODO: Implement memory tracking
                        'cache_operations': {
                            'reads': 0,
                            'writes': 0,
                            'hits': 0,
                            'misses': 1
                        }
                    },
                    'quality_score': min(100, len(final_completions) * 20)  # Simple quality score
                },
                'timestamp': time.time() * 1000
            }, namespace='/ws/completion', room=client_id)
            
            logger.info(f"Completion request {request_id} completed in {total_time:.1f}ms with {len(final_completions)} results")
            
        except Exception as e:
            logger.error(f"Error processing completion request {request_id}: {e}")
            
            if not self.is_request_cancelled(request_id):
                self.socketio.emit('completion_error', {
                    'type': 'completion_error',
                    'id': request_id,
                    'error': {
                        'code': 'PROCESSING_ERROR',
                        'message': str(e),
                        'recoverable': True,
                        'retry_after_ms': 1000
                    },
                    'timestamp': time.time() * 1000
                }, namespace='/ws/completion', room=client_id)
        
        finally:
            # Clean up active request
            if request_id in self.active_requests:
                del self.active_requests[request_id]
    
    def cancel_request(self, request_id: str):
        """Cancel an active completion request."""
        if request_id in self.active_requests:
            self.active_requests[request_id]['cancelled'] = True
            logger.info(f"Cancelled completion request: {request_id}")
    
    def is_request_cancelled(self, request_id: str) -> bool:
        """Check if a request has been cancelled."""
        return self.active_requests.get(request_id, {}).get('cancelled', False)
    
    def validate_completion_request(self, data: Dict[str, Any]) -> bool:
        """Validate completion request format."""
        required_fields = ['id', 'context']
        for field in required_fields:
            if field not in data:
                return False
        
        context = data['context']
        context_required = ['file_path', 'content', 'cursor_line', 'cursor_column', 'language']
        for field in context_required:
            if field not in context:
                return False
        
        return True
    
    def remove_duplicate_completions(self, completions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate completions based on text."""
        seen = set()
        unique = []
        
        for completion in completions:
            text = completion.get('text', '')
            if text and text not in seen:
                seen.add(text)
                unique.append(completion)
        
        return unique
    
    def get_server_info(self) -> Dict[str, Any]:
        """Get server information."""
        return {
            'version': '2.0.0',
            'swe_tools_available': len(swe_tool_proxy.tools_cache) if swe_tool_proxy else 0,
            'swe_tools_loaded': list(swe_tool_proxy.tools_cache.keys()) if swe_tool_proxy else [],
            'performance_mode': 'development',
            'cache_enabled': True,
            'max_concurrent_requests': 10
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return {
            'avg_response_time_ms': 250,  # TODO: Calculate from actual metrics
            'cache_hit_rate': 0.6,
            'error_rate': 0.02,
            'active_connections': len(self.client_sessions)
        }

# Global WebSocket handler instance
completion_ws_handler: Optional[CompletionWebSocketHandler] = None

def initialize_completion_websocket(socketio: SocketIO):
    """Initialize the completion WebSocket handler."""
    global completion_ws_handler
    completion_ws_handler = CompletionWebSocketHandler(socketio)
    logger.info("Completion WebSocket handler initialized")

def get_completion_websocket_handler() -> Optional[CompletionWebSocketHandler]:
    """Get the completion WebSocket handler instance."""
    return completion_ws_handler
