"""
API endpoints for code completion functionality.
Provides RESTful and WebSocket endpoints for intelligent code completion.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional
from flask import Blueprint, request, jsonify
from flask_socketio import Socket<PERSON>, emit, disconnect
from dataclasses import asdict

from bridge.integrations.code_completion import (
    completion_engine,
    CompletionContext,
    CompletionResponse
)
from bridge.core.context_manager import context_analyzer
from bridge.auth.session_auth import session_auth
from bridge.integrations.swe_tool_proxy import swe_tool_proxy
from bridge.core.tool_security import tool_security

logger = logging.getLogger(__name__)

# Create blueprint for completion endpoints
completion_bp = Blueprint('completion', __name__, url_prefix='/api/completion')


@completion_bp.route('/complete', methods=['POST'])
@session_auth.optional_auth
def complete_code():
    """
    RESTful endpoint for code completion.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "content": "file content",
        "cursor_line": 10,
        "cursor_column": 5,
        "language": "python",
        "project_root": "/path/to/project",
        "selection": "optional selected text"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        required_fields = ['file_path', 'content', 'cursor_line', 'cursor_column']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Create completion context
        context = CompletionContext(
            file_path=data['file_path'],
            content=data['content'],
            cursor_line=data['cursor_line'],
            cursor_column=data['cursor_column'],
            language=data.get('language', 'text'),
            project_root=data.get('project_root'),
            selection=data.get('selection'),
            surrounding_context=data.get('surrounding_context')
        )
        
        # Get completions asynchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            response = loop.run_until_complete(completion_engine.get_completions(context))
        finally:
            loop.close()
        
        # Convert response to JSON
        response_dict = asdict(response)
        
        logger.info(f"Completion request for {context.file_path}:{context.cursor_line}:{context.cursor_column} - {len(response.items)} suggestions")
        
        return jsonify(response_dict)
        
    except Exception as e:
        logger.error(f"Error in completion endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@completion_bp.route('/analyze', methods=['POST'])
@session_auth.optional_auth
def analyze_file():
    """
    Analyze a file for context information.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "content": "file content",
        "language": "python"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        required_fields = ['file_path', 'content']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Analyze file
        file_context = context_analyzer.analyze_file(
            file_path=data['file_path'],
            content=data['content'],
            language=data.get('language')
        )
        
        # Convert to JSON-serializable format
        response = {
            "file_path": file_context.file_path,
            "language": file_context.language,
            "symbols": [asdict(symbol) for symbol in file_context.symbols],
            "imports": file_context.imports,
            "dependencies": file_context.dependencies,
            "project_root": file_context.project_root
        }
        
        logger.info(f"File analysis for {file_context.file_path} - {len(file_context.symbols)} symbols found")
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error in analyze endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@completion_bp.route('/context', methods=['POST'])
def get_context_at_position():
    """
    Get context information at a specific position.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "line": 10,
        "column": 5
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        required_fields = ['file_path', 'line', 'column']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Get context at position
        symbol = context_analyzer.get_context_at_position(
            file_path=data['file_path'],
            line=data['line'],
            column=data['column']
        )
        
        if symbol:
            response = asdict(symbol)
        else:
            response = None
        
        return jsonify({"symbol": response})
        
    except Exception as e:
        logger.error(f"Error in context endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@completion_bp.route('/symbols', methods=['POST'])
def get_symbols_in_scope():
    """
    Get symbols visible at a specific line.
    
    Expected JSON payload:
    {
        "file_path": "/path/to/file.py",
        "line": 10
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        required_fields = ['file_path', 'line']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Get symbols in scope
        symbols = context_analyzer.get_symbols_in_scope(
            file_path=data['file_path'],
            line=data['line']
        )
        
        response = {
            "symbols": [asdict(symbol) for symbol in symbols]
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error in symbols endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@completion_bp.route('/cache/clear', methods=['POST'])
def clear_completion_cache():
    """Clear the completion cache."""
    try:
        completion_engine.clear_cache()
        return jsonify({"status": "success", "message": "Completion cache cleared"})
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        return jsonify({"error": str(e)}), 500


# WebSocket events for real-time completion
def setup_completion_websocket(socketio: SocketIO):
    """Set up WebSocket events for real-time completion."""
    
    @socketio.on('completion_request')
    def handle_completion_request(data):
        """Handle real-time completion request via WebSocket."""
        try:
            # Validate data
            required_fields = ['file_path', 'content', 'cursor_line', 'cursor_column']
            for field in required_fields:
                if field not in data:
                    emit('completion_error', {"error": f"Missing required field: {field}"})
                    return
            
            # Create completion context
            context = CompletionContext(
                file_path=data['file_path'],
                content=data['content'],
                cursor_line=data['cursor_line'],
                cursor_column=data['cursor_column'],
                language=data.get('language', 'text'),
                project_root=data.get('project_root'),
                selection=data.get('selection'),
                surrounding_context=data.get('surrounding_context')
            )
            
            # Get completions asynchronously
            async def get_completions_async():
                try:
                    response = await completion_engine.get_completions(context)
                    
                    # Emit response
                    socketio.emit('completion_response', {
                        "context_id": data.get('context_id'),
                        "response": asdict(response)
                    })
                    
                except Exception as e:
                    logger.error(f"Error in async completion: {e}")
                    socketio.emit('completion_error', {
                        "context_id": data.get('context_id'),
                        "error": str(e)
                    })
            
            # Run async function
            asyncio.create_task(get_completions_async())
            
        except Exception as e:
            logger.error(f"Error in WebSocket completion request: {e}")
            emit('completion_error', {"error": str(e)})
    
    @socketio.on('completion_cancel')
    def handle_completion_cancel(data):
        """Handle completion cancellation."""
        context_id = data.get('context_id')
        if context_id:
            # Cancel any ongoing completion for this context
            logger.info(f"Completion cancelled for context {context_id}")
            emit('completion_cancelled', {"context_id": context_id})
    
    @socketio.on('file_analysis_request')
    def handle_file_analysis(data):
        """Handle file analysis request via WebSocket."""
        try:
            # Validate data
            required_fields = ['file_path', 'content']
            for field in required_fields:
                if field not in data:
                    emit('analysis_error', {"error": f"Missing required field: {field}"})
                    return
            
            # Analyze file
            file_context = context_analyzer.analyze_file(
                file_path=data['file_path'],
                content=data['content'],
                language=data.get('language')
            )
            
            # Emit response
            emit('analysis_response', {
                "request_id": data.get('request_id'),
                "file_path": file_context.file_path,
                "language": file_context.language,
                "symbols": [asdict(symbol) for symbol in file_context.symbols],
                "imports": file_context.imports,
                "dependencies": file_context.dependencies
            })
            
        except Exception as e:
            logger.error(f"Error in WebSocket file analysis: {e}")
            emit('analysis_error', {
                "request_id": data.get('request_id'),
                "error": str(e)
            })
    
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection."""
        logger.info("Completion client connected")
        emit('connected', {"status": "connected"})
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection."""
        logger.info("Completion client disconnected")


# Enhanced completion endpoints for Phase 2: SWE-Agent integration

@completion_bp.route('/intelligent', methods=['POST'])
@session_auth.optional_auth
def intelligent_completion():
    """
    Provide intelligent code completions using SWE-Agent tools and AI analysis.

    Request format:
    {
        "context": {
            "file_path": "/workspace/file.py",
            "content": "def calculate_area(radius):\n    return math.pi * radius",
            "cursor_line": 1,
            "cursor_column": 25,
            "language": "python",
            "workspace_path": "/workspace"
        },
        "options": {
            "max_completions": 5,
            "use_swe_tools": true,
            "include_context": true
        }
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'status': 'error', 'error': 'No JSON data provided'}), 400

        context = data.get('context', {})
        options = data.get('options', {})

        # Validate required context fields
        required_fields = ['file_path', 'content', 'cursor_line', 'cursor_column', 'language']
        for field in required_fields:
            if field not in context:
                return jsonify({'status': 'error', 'error': f'Missing required field: {field}'}), 400

        # Security validation
        try:
            violations = tool_security.validate_completion_context(context)
            if violations:
                return jsonify({
                    'status': 'error',
                    'error': 'Security violations detected',
                    'violations': [str(v) for v in violations]
                }), 400
        except Exception as e:
            logger.error(f"Security validation error: {e}")
            return jsonify({'status': 'error', 'error': 'Security validation failed'}), 500

        # Get intelligent completions
        import time
        start_time = time.time()
        completions = get_intelligent_completions(context, options)
        response_time = (time.time() - start_time) * 1000  # Convert to milliseconds

        return jsonify({
            'status': 'success',
            'completions': completions,
            'response_time_ms': response_time,
            'context_analyzed': True
        })

    except Exception as e:
        logger.error(f"Intelligent completion error: {e}")
        return jsonify({'status': 'error', 'error': str(e)}), 500


@completion_bp.route('/swe-analyze', methods=['POST'])
@session_auth.optional_auth
def swe_analyze_completion():
    """
    Analyze code context using SWE-Agent tools for completion suggestions.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'status': 'error', 'error': 'No JSON data provided'}), 400

        file_path = data.get('file_path')
        content = data.get('content')
        cursor_position = data.get('cursor_position', {})
        tools = data.get('tools', ['filemap', 'search_file'])

        if not file_path or not content:
            return jsonify({'status': 'error', 'error': 'Missing file_path or content'}), 400

        # Security validation
        try:
            violations = tool_security.validate_file_path(file_path)
            if violations:
                return jsonify({
                    'status': 'error',
                    'error': 'Security violations detected',
                    'violations': [str(v) for v in violations]
                }), 400
        except Exception as e:
            logger.error(f"Security validation error: {e}")
            return jsonify({'status': 'error', 'error': 'Security validation failed'}), 500

        # Analyze using SWE-Agent tools
        analysis_result = analyze_with_swe_tools(file_path, content, cursor_position, tools)

        return jsonify({
            'status': 'success',
            'analysis': analysis_result,
            'tools_used': tools
        })

    except Exception as e:
        logger.error(f"SWE analyze completion error: {e}")
        return jsonify({'status': 'error', 'error': str(e)}), 500


# Helper functions for intelligent completion

def get_intelligent_completions(context, options):
    """Get intelligent completions using SWE-Agent tools and AI analysis."""
    completions = []
    max_completions = options.get('max_completions', 5)
    use_swe_tools = options.get('use_swe_tools', True)

    try:
        # Extract context information
        file_path = context['file_path']
        content = context['content']
        cursor_line = context['cursor_line']
        cursor_column = context['cursor_column']
        language = context['language']

        # Get line at cursor
        lines = content.split('\n')
        current_line = lines[cursor_line] if cursor_line < len(lines) else ''
        before_cursor = current_line[:cursor_column]

        # Use SWE-Agent tools if enabled and available
        if use_swe_tools and swe_tool_proxy.tools_loaded:
            swe_completions = get_swe_tool_completions(context)
            completions.extend(swe_completions)

        # Add language-specific completions
        lang_completions = get_language_completions(language, before_cursor, context)
        completions.extend(lang_completions)

        # Add context-aware completions
        context_completions = get_context_completions(context)
        completions.extend(context_completions)

        # Remove duplicates and limit results
        unique_completions = remove_duplicate_completions(completions)
        return unique_completions[:max_completions]

    except Exception as e:
        logger.error(f"Error getting intelligent completions: {e}")
        return []


def get_swe_tool_completions(context):
    """Get completions using SWE-Agent tools."""
    completions = []
    file_path = context['file_path']
    language = context['language']

    try:
        # Use filemap for Python files
        if language == 'python':
            filemap_result = swe_tool_proxy.execute_tool('filemap', {'file_path': file_path})
            if filemap_result.success:
                completions.extend(parse_filemap_completions(filemap_result.output))

        # Use search_file for finding patterns
        search_terms = extract_search_terms_from_context(context)
        for term in search_terms[:3]:  # Limit to 3 searches
            search_result = swe_tool_proxy.execute_tool('search_file', {
                'search_term': term,
                'file': file_path
            })
            if search_result.success:
                completions.extend(parse_search_completions(search_result.output, term))

    except Exception as e:
        logger.error(f"Error getting SWE tool completions: {e}")

    return completions


def get_language_completions(language, before_cursor, context):
    """Get language-specific completions."""
    completions = []

    if language == 'python':
        completions.extend(get_python_completions(before_cursor, context))
    elif language in ['javascript', 'typescript']:
        completions.extend(get_javascript_completions(before_cursor, context))
    elif language == 'java':
        completions.extend(get_java_completions(before_cursor, context))

    return completions


def get_python_completions(before_cursor, context):
    """Get Python-specific completions."""
    completions = []

    # Math module completions
    if 'math.' in before_cursor:
        math_methods = ['pi', 'sqrt', 'sin', 'cos', 'tan', 'log', 'exp', 'floor', 'ceil']
        for method in math_methods:
            completions.append({
                'text': method,
                'kind': 'property',
                'detail': f'math.{method}',
                'documentation': f'Math module property: {method}'
            })

    return completions


def get_javascript_completions(before_cursor, context):
    """Get JavaScript/TypeScript-specific completions."""
    completions = []

    # Console completions
    if 'console.' in before_cursor:
        console_methods = ['log', 'error', 'warn', 'info', 'debug']
        for method in console_methods:
            completions.append({
                'text': method,
                'kind': 'method',
                'detail': f'console.{method}',
                'documentation': f'Console method: {method}'
            })

    return completions


def get_java_completions(before_cursor, context):
    """Get Java-specific completions."""
    completions = []

    # System completions
    if 'System.' in before_cursor:
        system_methods = ['out.println', 'err.println', 'currentTimeMillis']
        for method in system_methods:
            completions.append({
                'text': method,
                'kind': 'method',
                'detail': f'System.{method}',
                'documentation': f'System method: {method}'
            })

    return completions


def get_context_completions(context):
    """Get context-aware completions based on surrounding code."""
    completions = []
    content = context['content']

    # Extract variables and functions from content
    lines = content.split('\n')
    for line in lines:
        # Find function definitions
        if 'def ' in line:
            func_match = line.split('def ')[1].split('(')[0].strip()
            if func_match:
                completions.append({
                    'text': func_match,
                    'kind': 'function',
                    'detail': 'Function from current file',
                    'documentation': f'Function defined in current file: {func_match}'
                })

    return completions


def analyze_with_swe_tools(file_path, content, cursor_position, tools):
    """Analyze code using specified SWE-Agent tools."""
    analysis = {
        'file_structure': None,
        'search_results': [],
        'code_analysis': None
    }

    try:
        if 'filemap' in tools and file_path.endswith('.py'):
            filemap_result = swe_tool_proxy.execute_tool('filemap', {'file_path': file_path})
            if filemap_result.success:
                analysis['file_structure'] = parse_filemap_output(filemap_result.output)

    except Exception as e:
        logger.error(f"Error analyzing with SWE tools: {e}")

    return analysis


def extract_search_terms_from_context(context):
    """Extract search terms from completion context."""
    terms = []
    content = context['content']
    cursor_line = context['cursor_line']
    cursor_column = context['cursor_column']

    lines = content.split('\n')
    if cursor_line < len(lines):
        current_line = lines[cursor_line]
        before_cursor = current_line[:cursor_column]

        # Extract word before cursor
        words = before_cursor.split()
        if words:
            terms.append(words[-1])

    return list(set(terms))  # Remove duplicates


def parse_filemap_completions(output):
    """Parse filemap output to extract completion suggestions."""
    completions = []
    lines = output.split('\n')

    for line in lines:
        if 'def ' in line:
            func_match = line.split('def ')[1].split('(')[0].strip()
            if func_match:
                completions.append({
                    'text': func_match,
                    'kind': 'function',
                    'detail': 'Function from filemap',
                    'documentation': f'Function found in file structure: {func_match}'
                })

    return completions


def parse_filemap_output(output):
    """Parse filemap output to extract file structure."""
    structure = {
        'functions': [],
        'classes': [],
        'imports': []
    }

    lines = output.split('\n')
    for line in lines:
        if 'def ' in line:
            func_match = line.split('def ')[1].split('(')[0].strip()
            if func_match:
                structure['functions'].append(func_match)

    return structure


def parse_search_completions(output, search_term):
    """Parse search output to extract completion suggestions."""
    completions = []
    lines = output.split('\n')

    for line in lines:
        if search_term in line:
            completions.append({
                'text': search_term,
                'kind': 'text',
                'detail': f'Found in search for "{search_term}"',
                'documentation': f'Search result: {line.strip()}'
            })

    return completions


def remove_duplicate_completions(completions):
    """Remove duplicate completions based on text."""
    seen = set()
    unique = []

    for completion in completions:
        text = completion.get('text', '')
        if text and text not in seen:
            seen.add(text)
            unique.append(completion)

    return unique


# Health check for completion service
@completion_bp.route('/health', methods=['GET'])
def completion_health():
    """Health check for completion service."""
    try:
        # Check if completion engine is working
        cache_size = len(completion_engine.cache.cache)
        swe_tools_available = swe_tool_proxy.tools_loaded if swe_tool_proxy else False

        return jsonify({
            "status": "healthy",
            "cache_size": cache_size,
            "active_sessions": len(completion_engine.active_sessions),
            "swe_tools_available": swe_tools_available,
            "swe_tools_count": len(swe_tool_proxy.tools_cache) if swe_tool_proxy else 0
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({"status": "unhealthy", "error": str(e)}), 500
