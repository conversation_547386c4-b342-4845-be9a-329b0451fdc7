"""
Code completion bridge for intelligent code suggestions.
Integrates with SWE-Agent capabilities to provide context-aware completions.
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib

from bridge.core.session_manager import session_manager, SessionConfig
from bridge.integrations.swe_agent_interface import SWEAgentInterface

logger = logging.getLogger(__name__)


@dataclass
class CompletionContext:
    """Context information for code completion."""
    file_path: str
    content: str
    cursor_line: int
    cursor_column: int
    language: str
    project_root: Optional[str] = None
    selection: Optional[str] = None
    surrounding_context: Optional[str] = None


@dataclass
class CompletionItem:
    """A single completion suggestion."""
    text: str
    kind: str  # function, variable, class, keyword, etc.
    detail: Optional[str] = None
    documentation: Optional[str] = None
    insert_text: Optional[str] = None
    priority: int = 0
    source: str = "swe-agent"


@dataclass
class CompletionResponse:
    """Response containing completion suggestions."""
    items: List[CompletionItem]
    is_incomplete: bool = False
    context_id: Optional[str] = None
    processing_time_ms: int = 0


class CompletionCache:
    """Cache for completion results to improve performance."""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 300):
        self.cache: Dict[str, Tuple[CompletionResponse, float]] = {}
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
    
    def _generate_key(self, context: CompletionContext) -> str:
        """Generate cache key from completion context."""
        content_hash = hashlib.md5(context.content.encode()).hexdigest()[:8]
        return f"{context.file_path}:{context.cursor_line}:{context.cursor_column}:{content_hash}"
    
    def get(self, context: CompletionContext) -> Optional[CompletionResponse]:
        """Get cached completion response."""
        key = self._generate_key(context)
        if key in self.cache:
            response, timestamp = self.cache[key]
            if time.time() - timestamp < self.ttl_seconds:
                return response
            else:
                del self.cache[key]
        return None
    
    def set(self, context: CompletionContext, response: CompletionResponse):
        """Cache completion response."""
        if len(self.cache) >= self.max_size:
            # Remove oldest entry
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
            del self.cache[oldest_key]
        
        key = self._generate_key(context)
        self.cache[key] = (response, time.time())
    
    def clear(self):
        """Clear all cached entries."""
        self.cache.clear()


class CodeCompletionEngine:
    """Core engine for intelligent code completion."""
    
    def __init__(self):
        self.swe_agent = SWEAgentInterface()
        self.cache = CompletionCache()
        self.active_sessions: Dict[str, str] = {}  # context_id -> session_id
        
    async def get_completions(self, context: CompletionContext) -> CompletionResponse:
        """
        Get code completion suggestions for the given context.
        
        Args:
            context: Completion context with file and cursor information.
            
        Returns:
            Completion response with suggestions.
        """
        start_time = time.time()
        
        try:
            # Check cache first
            cached_response = self.cache.get(context)
            if cached_response:
                logger.debug(f"Cache hit for completion at {context.file_path}:{context.cursor_line}")
                return cached_response
            
            # Generate context ID for this completion request
            context_id = self._generate_context_id(context)
            
            # Analyze context and generate completions
            completions = await self._generate_completions(context, context_id)
            
            # Create response
            response = CompletionResponse(
                items=completions,
                context_id=context_id,
                processing_time_ms=int((time.time() - start_time) * 1000)
            )
            
            # Cache the response
            self.cache.set(context, response)
            
            logger.info(f"Generated {len(completions)} completions in {response.processing_time_ms}ms")
            return response
            
        except Exception as e:
            logger.error(f"Error generating completions: {e}")
            return CompletionResponse(
                items=[],
                processing_time_ms=int((time.time() - start_time) * 1000)
            )
    
    async def _generate_completions(self, context: CompletionContext, context_id: str) -> List[CompletionItem]:
        """Generate completion suggestions using SWE-Agent."""
        completions = []
        
        try:
            # Extract surrounding context for better understanding
            surrounding_lines = self._extract_surrounding_context(context)
            
            # Create a focused problem statement for SWE-Agent
            problem_statement = self._create_completion_prompt(context, surrounding_lines)
            
            # Use SWE-Agent for intelligent suggestions
            suggestions = await self._query_swe_agent(problem_statement, context)
            
            # Parse and format suggestions
            completions = self._parse_swe_suggestions(suggestions, context)
            
            # Add language-specific completions
            lang_completions = self._get_language_completions(context)
            completions.extend(lang_completions)
            
            # Sort by priority and relevance
            completions.sort(key=lambda x: x.priority, reverse=True)
            
        except Exception as e:
            logger.error(f"Error in completion generation: {e}")
        
        return completions[:20]  # Limit to top 20 suggestions
    
    def _extract_surrounding_context(self, context: CompletionContext) -> str:
        """Extract relevant surrounding code context."""
        lines = context.content.split('\n')
        start_line = max(0, context.cursor_line - 10)
        end_line = min(len(lines), context.cursor_line + 10)
        
        surrounding = '\n'.join(lines[start_line:end_line])
        return surrounding
    
    def _create_completion_prompt(self, context: CompletionContext, surrounding: str) -> str:
        """Create a focused prompt for SWE-Agent completion."""
        cursor_line_content = context.content.split('\n')[context.cursor_line] if context.cursor_line < len(context.content.split('\n')) else ""
        
        prompt = f"""
Provide intelligent code completion suggestions for the following context:

File: {context.file_path}
Language: {context.language}
Current line: {cursor_line_content}
Cursor position: line {context.cursor_line + 1}, column {context.cursor_column + 1}

Surrounding context:
```{context.language}
{surrounding}
```

Please suggest appropriate code completions considering:
1. Variable names and functions in scope
2. Language-specific syntax and patterns
3. Common programming patterns
4. Type information if available

Focus on practical, contextually relevant suggestions.
"""
        return prompt
    
    async def _query_swe_agent(self, prompt: str, context: CompletionContext) -> str:
        """Query SWE-Agent for completion suggestions."""
        try:
            # Create a temporary session for completion
            session_config = SessionConfig(
                model_name="claude-3-haiku-20240307",  # Use faster model for completions
                repo_path=context.project_root or str(Path(context.file_path).parent),
                problem_statement=prompt
            )
            
            session_id = session_manager.create_session(session_config)
            
            # Run SWE-Agent with a short timeout for responsiveness
            result = await asyncio.wait_for(
                self._run_swe_agent_async(session_id),
                timeout=5.0  # 5 second timeout for completions
            )
            
            return result
            
        except asyncio.TimeoutError:
            logger.warning("SWE-Agent completion timed out")
            return ""
        except Exception as e:
            logger.error(f"Error querying SWE-Agent: {e}")
            return ""
    
    async def _run_swe_agent_async(self, session_id: str) -> str:
        """Run SWE-Agent asynchronously."""
        # This would integrate with the actual SWE-Agent execution
        # For now, return a placeholder
        await asyncio.sleep(0.1)  # Simulate processing time
        return "# SWE-Agent completion suggestions would be here"
    
    def _parse_swe_suggestions(self, suggestions: str, context: CompletionContext) -> List[CompletionItem]:
        """Parse SWE-Agent suggestions into completion items."""
        items = []
        
        # Parse the suggestions and create completion items
        # This is a simplified implementation
        if suggestions and suggestions.strip():
            items.append(CompletionItem(
                text=suggestions.strip(),
                kind="suggestion",
                detail="AI-generated suggestion",
                documentation="Intelligent completion from SWE-Agent",
                priority=10
            ))
        
        return items
    
    def _get_language_completions(self, context: CompletionContext) -> List[CompletionItem]:
        """Get language-specific completion suggestions."""
        completions = []
        
        # Python-specific completions
        if context.language.lower() == "python":
            completions.extend(self._get_python_completions(context))
        
        # JavaScript/TypeScript completions
        elif context.language.lower() in ["javascript", "typescript"]:
            completions.extend(self._get_js_completions(context))
        
        return completions
    
    def _get_python_completions(self, context: CompletionContext) -> List[CompletionItem]:
        """Get Python-specific completions."""
        completions = []
        
        # Common Python keywords and patterns
        python_keywords = [
            "def", "class", "if", "elif", "else", "for", "while", "try", "except", "finally",
            "import", "from", "return", "yield", "with", "as", "lambda", "global", "nonlocal"
        ]
        
        current_line = context.content.split('\n')[context.cursor_line] if context.cursor_line < len(context.content.split('\n')) else ""
        
        for keyword in python_keywords:
            if keyword.startswith(current_line.split()[-1] if current_line.split() else ""):
                completions.append(CompletionItem(
                    text=keyword,
                    kind="keyword",
                    detail=f"Python keyword",
                    priority=5
                ))
        
        return completions
    
    def _get_js_completions(self, context: CompletionContext) -> List[CompletionItem]:
        """Get JavaScript/TypeScript-specific completions."""
        completions = []
        
        # Common JS keywords and patterns
        js_keywords = [
            "function", "const", "let", "var", "if", "else", "for", "while", "try", "catch",
            "finally", "return", "class", "extends", "import", "export", "async", "await"
        ]
        
        current_line = context.content.split('\n')[context.cursor_line] if context.cursor_line < len(context.content.split('\n')) else ""
        
        for keyword in js_keywords:
            if keyword.startswith(current_line.split()[-1] if current_line.split() else ""):
                completions.append(CompletionItem(
                    text=keyword,
                    kind="keyword",
                    detail=f"JavaScript keyword",
                    priority=5
                ))
        
        return completions
    
    def _generate_context_id(self, context: CompletionContext) -> str:
        """Generate unique context ID for completion request."""
        content_hash = hashlib.md5(f"{context.file_path}:{context.cursor_line}:{context.cursor_column}".encode()).hexdigest()[:8]
        return f"completion_{content_hash}_{int(time.time())}"
    
    def clear_cache(self):
        """Clear completion cache."""
        self.cache.clear()
        logger.info("Completion cache cleared")


# Global completion engine instance
completion_engine = CodeCompletionEngine()
