"""
Multi-turn chat manager for conversational interface with context preservation.
Integrates with <PERSON><PERSON>E-Agent for code-related queries and maintains conversation history.
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass, asdict
from enum import Enum
import threading

from bridge.core.session_manager import session_manager, SessionConfig
from bridge.integrations.swe_agent_interface import SWEAgentInterface
from bridge.core.context_manager import context_analyzer
from bridge.core.agent_tools import agent_tools

logger = logging.getLogger(__name__)


class MessageRole(Enum):
    """Message roles in conversation."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class ChatStatus(Enum):
    """Chat session status."""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class ChatMessage:
    """A single message in a chat conversation."""
    id: str
    role: MessageRole
    content: str
    timestamp: datetime
    context: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        data = asdict(self)
        data['role'] = self.role.value
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class ChatContext:
    """Context information for a chat session."""
    file_path: Optional[str] = None
    project_root: Optional[str] = None
    current_selection: Optional[str] = None
    cursor_position: Optional[Dict[str, int]] = None
    language: Optional[str] = None
    symbols_in_scope: Optional[List[Dict[str, Any]]] = None
    recent_changes: Optional[List[Dict[str, Any]]] = None


@dataclass
class ChatSession:
    """A chat conversation session."""
    session_id: str
    title: str
    messages: List[ChatMessage]
    context: ChatContext
    status: ChatStatus
    created_at: datetime
    updated_at: datetime
    swe_session_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary."""
        data = asdict(self)
        data['status'] = self.status.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        data['messages'] = [msg.to_dict() for msg in self.messages]
        return data


class ChatManager:
    """Manages multi-turn chat conversations with context preservation."""
    
    def __init__(self):
        self.sessions: Dict[str, ChatSession] = {}
        self.swe_agent = SWEAgentInterface()
        self.lock = threading.RLock()
        self.active_streams: Dict[str, bool] = {}
    
    def create_session(self, title: str = None, context: ChatContext = None) -> str:
        """
        Create a new chat session.
        
        Args:
            title: Optional title for the session.
            context: Initial context for the session.
            
        Returns:
            Session ID.
        """
        session_id = str(uuid.uuid4())
        
        if title is None:
            title = f"Chat Session {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        if context is None:
            context = ChatContext()
        
        with self.lock:
            session = ChatSession(
                session_id=session_id,
                title=title,
                messages=[],
                context=context,
                status=ChatStatus.ACTIVE,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            self.sessions[session_id] = session
        
        logger.info(f"Created chat session {session_id}: {title}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get a chat session by ID."""
        with self.lock:
            return self.sessions.get(session_id)
    
    def list_sessions(self, status: Optional[ChatStatus] = None) -> List[ChatSession]:
        """List all chat sessions, optionally filtered by status."""
        with self.lock:
            sessions = list(self.sessions.values())
        
        if status:
            sessions = [s for s in sessions if s.status == status]
        
        return sorted(sessions, key=lambda s: s.updated_at, reverse=True)
    
    def add_message(self, session_id: str, role: MessageRole, content: str, 
                   context: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Add a message to a chat session.
        
        Args:
            session_id: Chat session ID.
            role: Message role (user, assistant, system).
            content: Message content.
            context: Optional context information.
            
        Returns:
            Message ID if successful, None otherwise.
        """
        with self.lock:
            session = self.sessions.get(session_id)
            if not session:
                return None
            
            message_id = str(uuid.uuid4())
            message = ChatMessage(
                id=message_id,
                role=role,
                content=content,
                timestamp=datetime.now(),
                context=context
            )
            
            session.messages.append(message)
            session.updated_at = datetime.now()
        
        logger.debug(f"Added {role.value} message to session {session_id}")
        return message_id
    
    def update_context(self, session_id: str, context: ChatContext) -> bool:
        """Update the context for a chat session."""
        with self.lock:
            session = self.sessions.get(session_id)
            if not session:
                return False
            
            session.context = context
            session.updated_at = datetime.now()
        
        return True
    
    async def send_message(self, session_id: str, message: str, 
                          context: Optional[Dict[str, Any]] = None) -> AsyncGenerator[str, None]:
        """
        Send a message and get streaming response.
        
        Args:
            session_id: Chat session ID.
            message: User message.
            context: Optional context information.
            
        Yields:
            Response chunks as they are generated.
        """
        session = self.get_session(session_id)
        if not session:
            yield json.dumps({"error": "Session not found"})
            return
        
        # Add user message
        self.add_message(session_id, MessageRole.USER, message, context)
        
        try:
            # Update context if provided
            if context:
                await self._update_session_context(session_id, context)
            
            # Generate response using SWE-Agent
            response_chunks = []
            async for chunk in self._generate_response(session_id, message):
                response_chunks.append(chunk)
                yield chunk
            
            # Add assistant response
            full_response = "".join(response_chunks)
            self.add_message(session_id, MessageRole.ASSISTANT, full_response)
            
        except Exception as e:
            error_msg = f"Error generating response: {str(e)}"
            logger.error(error_msg)
            self.add_message(session_id, MessageRole.ASSISTANT, error_msg)
            yield json.dumps({"error": error_msg})
    
    async def _update_session_context(self, session_id: str, context: Dict[str, Any]):
        """Update session context with current file and cursor information."""
        session = self.get_session(session_id)
        if not session:
            return
        
        # Extract context information
        file_path = context.get('file_path')
        cursor_line = context.get('cursor_line')
        cursor_column = context.get('cursor_column')
        selection = context.get('selection')
        
        # Analyze current file if provided
        if file_path and context.get('content'):
            try:
                file_context = context_analyzer.analyze_file(
                    file_path, 
                    context['content'], 
                    context.get('language')
                )
                
                # Get symbols in scope
                symbols = []
                if cursor_line is not None:
                    symbols = context_analyzer.get_symbols_in_scope(file_path, cursor_line)
                
                # Update session context
                chat_context = ChatContext(
                    file_path=file_path,
                    project_root=context.get('project_root'),
                    current_selection=selection,
                    cursor_position={'line': cursor_line, 'column': cursor_column} if cursor_line is not None else None,
                    language=file_context.language,
                    symbols_in_scope=[asdict(symbol) for symbol in symbols]
                )
                
                self.update_context(session_id, chat_context)
                
            except Exception as e:
                logger.error(f"Error updating session context: {e}")
    
    async def _generate_response(self, session_id: str, message: str) -> AsyncGenerator[str, None]:
        """Generate response using direct LLM client with conversation context and tool capabilities."""
        session = self.get_session(session_id)
        if not session:
            return

        try:
            # Check if the message requires tool usage
            tool_response = await self._handle_tool_request(session, message)
            if tool_response:
                yield tool_response
                return

            # Import the LLM client
            from bridge.integrations.llm_client import llm_client

            # Build context-aware prompt with tool capabilities
            prompt = self._build_contextual_prompt_with_tools(session, message)

            # Prepare context for the LLM
            llm_context = {
                "project_root": session.context.project_root,
                "current_file": session.context.file_path,
                "current_selection": session.context.current_selection,
                "symbols_in_scope": getattr(session.context, 'symbols_in_scope', []),
                "available_tools": agent_tools.get_available_tools()
            }

            # Stream response from LLM
            async for chunk in llm_client.generate_response_stream(prompt, llm_context):
                yield chunk

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            yield f"I apologize, but I encountered an error: {str(e)}"
    
    def _build_contextual_prompt(self, session: ChatSession, message: str) -> str:
        """Build a context-aware prompt for SWE-Agent."""
        prompt_parts = []
        
        # Add conversation history
        if session.messages:
            prompt_parts.append("Previous conversation:")
            for msg in session.messages[-5:]:  # Last 5 messages for context
                role_prefix = "User" if msg.role == MessageRole.USER else "Assistant"
                prompt_parts.append(f"{role_prefix}: {msg.content}")
            prompt_parts.append("")
        
        # Add current context
        if session.context.file_path:
            prompt_parts.append(f"Current file: {session.context.file_path}")
        
        if session.context.language:
            prompt_parts.append(f"Language: {session.context.language}")
        
        if session.context.current_selection:
            prompt_parts.append(f"Selected code:\n```\n{session.context.current_selection}\n```")
        
        if session.context.symbols_in_scope:
            symbols = [s.get('name', '') for s in session.context.symbols_in_scope[:10]]
            prompt_parts.append(f"Available symbols: {', '.join(symbols)}")
        
        # Add current message
        prompt_parts.append(f"Current question: {message}")
        
        # Add instructions
        prompt_parts.append("""
Please provide a helpful response considering:
1. The conversation history and context
2. The current file and code selection
3. Available symbols and functions in scope
4. Best practices for the programming language

Be concise but thorough, and provide code examples when helpful.
""")
        
        return "\n".join(prompt_parts)

    def _build_contextual_prompt_with_tools(self, session: ChatSession, message: str) -> str:
        """Build a context-aware prompt that includes available tools."""
        prompt_parts = []

        # Add system message about capabilities
        prompt_parts.append("""You are an AI coding assistant with access to powerful tools for file operations, terminal execution, and workspace navigation. You can:

1. **File Operations**: Read, write, create, delete, and list files/directories
2. **Terminal Execution**: Run commands and manage terminal sessions
3. **Workspace Navigation**: Search files, analyze projects, and get file outlines

Available tools:""")

        # Add tool descriptions
        for tool_name, tool_info in agent_tools.get_available_tools().items():
            prompt_parts.append(f"- **{tool_name}**: {tool_info['description']}")

        prompt_parts.append("")

        # Add conversation history
        if session.messages:
            prompt_parts.append("Previous conversation:")
            for msg in session.messages[-5:]:  # Last 5 messages for context
                role_prefix = "User" if msg.role == MessageRole.USER else "Assistant"
                prompt_parts.append(f"{role_prefix}: {msg.content}")
            prompt_parts.append("")

        # Add current context
        if session.context.file_path:
            prompt_parts.append(f"Current file: {session.context.file_path}")

        if session.context.project_root:
            prompt_parts.append(f"Project root: {session.context.project_root}")

        if session.context.language:
            prompt_parts.append(f"Language: {session.context.language}")

        if session.context.current_selection:
            prompt_parts.append(f"Selected code:\n```\n{session.context.current_selection}\n```")

        if session.context.symbols_in_scope:
            symbols = [s.get('name', '') for s in session.context.symbols_in_scope[:10]]
            prompt_parts.append(f"Available symbols: {', '.join(symbols)}")

        # Add current message
        prompt_parts.append(f"Current request: {message}")

        # Add instructions
        prompt_parts.append("""
When responding:
1. If the user asks you to perform file operations, terminal commands, or workspace analysis, use the appropriate tools
2. Provide clear explanations of what you're doing
3. Show the results of tool executions
4. Offer helpful suggestions and best practices
5. Be concise but thorough

If you need to use tools, indicate which tool you would use and what parameters you would pass.
""")

        return "\n".join(prompt_parts)

    async def _handle_tool_request(self, session: ChatSession, message: str) -> Optional[str]:
        """Handle requests that require tool usage."""
        message_lower = message.lower()

        # File operation requests
        if any(keyword in message_lower for keyword in ['read file', 'show file', 'open file', 'file content']):
            return await self._handle_file_read_request(session, message)

        if any(keyword in message_lower for keyword in ['write file', 'save file', 'create file']):
            return await self._handle_file_write_request(session, message)

        if any(keyword in message_lower for keyword in ['list files', 'show directory', 'ls', 'dir']):
            return await self._handle_directory_list_request(session, message)

        # Terminal operation requests
        if any(keyword in message_lower for keyword in ['run command', 'execute', 'terminal', 'bash', 'shell']):
            return await self._handle_terminal_request(session, message)

        # Workspace navigation requests
        if any(keyword in message_lower for keyword in ['search files', 'find files', 'search project']):
            return await self._handle_file_search_request(session, message)

        if any(keyword in message_lower for keyword in ['analyze project', 'project structure', 'project analysis']):
            return await self._handle_project_analysis_request(session, message)

        return None

    async def _handle_file_read_request(self, session: ChatSession, message: str) -> str:
        """Handle file reading requests."""
        try:
            # Try to extract file path from message or use current file
            file_path = self._extract_file_path(message) or session.context.file_path

            if not file_path:
                return "Please specify a file path or open a file in the editor first."

            # Use agent tools to read the file
            result = agent_tools.execute_tool('read_file', file_path=file_path)

            if result.success:
                content = result.data['content']
                file_size = result.data['size']

                # Truncate very large files
                if len(content) > 5000:
                    content = content[:5000] + "\n... (file truncated, showing first 5000 characters)"

                return f"""I've read the file `{file_path}` (size: {file_size} bytes):

```
{content}
```

The file has been successfully loaded. How can I help you with this file?"""
            else:
                return f"I couldn't read the file `{file_path}`: {result.error}"

        except Exception as e:
            return f"Error reading file: {str(e)}"

    async def _handle_file_write_request(self, session: ChatSession, message: str) -> str:
        """Handle file writing requests."""
        try:
            # This is a simplified implementation - in practice, you'd want more sophisticated parsing
            return """I can help you write files! Please provide:
1. The file path where you want to write
2. The content you want to write

For example: "Write the following code to src/main.py: [your code here]"

Or if you want to create a new file, just let me know the path and content."""

        except Exception as e:
            return f"Error handling file write request: {str(e)}"

    async def _handle_directory_list_request(self, session: ChatSession, message: str) -> str:
        """Handle directory listing requests."""
        try:
            # Try to extract directory path from message or use project root
            dir_path = self._extract_file_path(message) or session.context.project_root or "."

            # Use agent tools to list directory
            result = agent_tools.execute_tool('list_directory', directory_path=dir_path, max_depth=2)

            if result.success:
                items = result.data['items']

                response = f"Directory listing for `{dir_path}`:\n\n"
                for item in items[:20]:  # Limit to first 20 items
                    icon = "📁" if item['type'] == 'directory' else "📄"
                    size_info = f" ({item['size']} bytes)" if item['size'] else ""
                    response += f"{icon} {item['name']}{size_info}\n"

                if len(items) > 20:
                    response += f"\n... and {len(items) - 20} more items"

                return response
            else:
                return f"I couldn't list the directory `{dir_path}`: {result.error}"

        except Exception as e:
            return f"Error listing directory: {str(e)}"

    async def _handle_terminal_request(self, session: ChatSession, message: str) -> str:
        """Handle terminal execution requests."""
        try:
            # Extract command from message
            command = self._extract_command(message)

            if not command:
                return "Please specify the command you want to execute. For example: 'run ls -la' or 'execute npm install'"

            # Use agent tools to execute command
            result = agent_tools.execute_tool('execute_command', command=command,
                                            working_directory=session.context.project_root)

            if result.success:
                data = result.data
                response = f"Executed command: `{command}`\n\n"

                if data['return_code'] == 0:
                    response += "✅ Command completed successfully\n\n"
                else:
                    response += f"❌ Command failed with exit code {data['return_code']}\n\n"

                if data['stdout']:
                    response += f"**Output:**\n```\n{data['stdout']}\n```\n"

                if data['stderr']:
                    response += f"**Errors:**\n```\n{data['stderr']}\n```\n"

                response += f"Execution time: {data['execution_time']:.2f} seconds"

                return response
            else:
                return f"I couldn't execute the command: {result.error}"

        except Exception as e:
            return f"Error executing command: {str(e)}"

    async def _handle_file_search_request(self, session: ChatSession, message: str) -> str:
        """Handle file search requests."""
        try:
            # Extract search query from message
            query = self._extract_search_query(message)

            if not query:
                return "Please specify what files you want to search for. For example: 'search for .py files' or 'find files containing main'"

            project_root = session.context.project_root
            if not project_root:
                return "No project root available. Please open a workspace folder first."

            # Use agent tools to search files
            result = agent_tools.execute_tool('search_files', project_path=project_root, query=query, max_results=15)

            if result.success:
                matches = result.data.get('matches', [])

                if not matches:
                    return f"No files found matching '{query}'"

                response = f"Found {len(matches)} files matching '{query}':\n\n"
                for match in matches:
                    response += f"📄 {match['file_path']} (score: {match['score']:.2f})\n"

                return response
            else:
                return f"I couldn't search for files: {result.error}"

        except Exception as e:
            return f"Error searching files: {str(e)}"

    async def _handle_project_analysis_request(self, session: ChatSession, message: str) -> str:
        """Handle project analysis requests."""
        try:
            project_root = session.context.project_root
            if not project_root:
                return "No project root available. Please open a workspace folder first."

            # Use agent tools to analyze project
            result = agent_tools.execute_tool('analyze_project', project_path=project_root)

            if result.success:
                analysis = result.data.get('analysis', {})

                response = f"Project Analysis for `{project_root}`:\n\n"
                response += f"**Project Type:** {analysis.get('project_type', 'Unknown')}\n"
                response += f"**Total Files:** {analysis.get('total_files', 0)}\n"
                response += f"**Languages:** {', '.join(analysis.get('languages', []))}\n"

                if analysis.get('dependencies'):
                    response += f"**Dependencies:** {len(analysis['dependencies'])} found\n"

                if analysis.get('entry_points'):
                    response += f"**Entry Points:** {', '.join(analysis['entry_points'])}\n"

                return response
            else:
                return f"I couldn't analyze the project: {result.error}"

        except Exception as e:
            return f"Error analyzing project: {str(e)}"

    def _extract_file_path(self, message: str) -> Optional[str]:
        """Extract file path from message."""
        # Simple implementation - could be enhanced with regex
        words = message.split()
        for i, word in enumerate(words):
            if '/' in word or '\\' in word or '.' in word:
                # Looks like a file path
                return word.strip('"\'')
        return None

    def _extract_command(self, message: str) -> Optional[str]:
        """Extract command from message."""
        # Look for common patterns
        patterns = [
            'run ', 'execute ', 'command ', 'bash ', 'shell '
        ]

        message_lower = message.lower()
        for pattern in patterns:
            if pattern in message_lower:
                start_idx = message_lower.find(pattern) + len(pattern)
                return message[start_idx:].strip()

        # If no pattern found, assume the whole message is a command
        return message.strip()

    def _extract_search_query(self, message: str) -> Optional[str]:
        """Extract search query from message."""
        # Look for common patterns
        patterns = [
            'search for ', 'find ', 'look for ', 'search '
        ]

        message_lower = message.lower()
        for pattern in patterns:
            if pattern in message_lower:
                start_idx = message_lower.find(pattern) + len(pattern)
                return message[start_idx:].strip()

        return None
    
    async def _query_swe_agent(self, prompt: str, context: ChatContext) -> str:
        """Query LLM for chat response using direct LLM client."""
        try:
            # Import the LLM client
            from bridge.integrations.llm_client import llm_client

            # Prepare context for the LLM
            llm_context = {
                "project_root": context.project_root,
                "current_file": context.file_path,  # Fix: use file_path instead of current_file
                "current_selection": context.current_selection,
                "symbols_in_scope": getattr(context, 'symbols_in_scope', [])  # Safe access
            }

            # Generate response using the LLM
            response = await llm_client.generate_response(prompt, llm_context)

            return response

        except Exception as e:
            logger.error(f"Error querying LLM: {e}")
            return f"I apologize, but I encountered an error while processing your request: {str(e)}"

    async def _handle_code_generation(self, prompt: str, context: ChatContext) -> str:
        """Handle code generation requests."""
        try:
            # Detect programming language from context or prompt
            language = self._detect_language(prompt, context)

            if "hello world" in prompt.lower() or "helloworld" in prompt.lower():
                return self._generate_hello_world(language)

            # For other code generation, provide a template
            return f"I can help you generate {language} code. Here's a basic template:\n\n```{language}\n// Your code here\n```\n\nCould you provide more specific requirements?"

        except Exception as e:
            logger.error(f"Error in code generation: {e}")
            return f"I encountered an error while generating code: {str(e)}"

    def _detect_language(self, prompt: str, context: ChatContext) -> str:
        """Detect programming language from prompt and context."""
        # Check context first
        if context and context.language:
            return context.language

        # Check prompt for language keywords
        prompt_lower = prompt.lower()
        if "java" in prompt_lower:
            return "java"
        elif "python" in prompt_lower:
            return "python"
        elif "javascript" in prompt_lower or "js" in prompt_lower:
            return "javascript"
        elif "typescript" in prompt_lower or "ts" in prompt_lower:
            return "typescript"
        elif "c++" in prompt_lower or "cpp" in prompt_lower:
            return "cpp"
        elif "c#" in prompt_lower or "csharp" in prompt_lower:
            return "csharp"
        elif "go" in prompt_lower and "golang" in prompt_lower:
            return "go"
        elif "rust" in prompt_lower:
            return "rust"

        # Default to Java if no language detected
        return "java"

    def _generate_hello_world(self, language: str) -> str:
        """Generate Hello World program for the specified language."""
        templates = {
            "java": '''Here's a Hello World program in Java:

```java
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}
```

This is a basic Java program that:
1. Defines a public class named `HelloWorld`
2. Contains a `main` method as the entry point
3. Prints "Hello, World!" to the console

To run this program:
1. Save it as `HelloWorld.java`
2. Compile with: `javac HelloWorld.java`
3. Run with: `java HelloWorld`''',

            "python": '''Here's a Hello World program in Python:

```python
def main():
    print("Hello, World!")

if __name__ == "__main__":
    main()
```

This Python program:
1. Defines a `main` function that prints "Hello, World!"
2. Uses the `if __name__ == "__main__":` guard to run the main function
3. Can be executed directly with: `python hello_world.py`''',

            "javascript": '''Here's a Hello World program in JavaScript:

```javascript
function main() {
    console.log("Hello, World!");
}

main();
```

This JavaScript program:
1. Defines a function that logs "Hello, World!" to the console
2. Calls the function immediately
3. Can be run with Node.js: `node hello_world.js`''',

            "typescript": '''Here's a Hello World program in TypeScript:

```typescript
function main(): void {
    console.log("Hello, World!");
}

main();
```

This TypeScript program:
1. Defines a typed function that logs "Hello, World!"
2. Uses explicit return type annotation
3. Compile with: `tsc hello_world.ts` then run: `node hello_world.js`''',
        }

        return templates.get(language, templates["java"])
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a chat session."""
        with self.lock:
            if session_id in self.sessions:
                del self.sessions[session_id]
                logger.info(f"Deleted chat session {session_id}")
                return True
        return False
    
    def clear_session_messages(self, session_id: str) -> bool:
        """Clear all messages from a chat session."""
        with self.lock:
            session = self.sessions.get(session_id)
            if session:
                session.messages.clear()
                session.updated_at = datetime.now()
                return True
        return False
    
    def get_session_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get a summary of a chat session."""
        session = self.get_session(session_id)
        if not session:
            return None
        
        return {
            "session_id": session.session_id,
            "title": session.title,
            "status": session.status.value,
            "message_count": len(session.messages),
            "created_at": session.created_at.isoformat(),
            "updated_at": session.updated_at.isoformat(),
            "context": asdict(session.context) if session.context else None
        }


# Global chat manager instance
chat_manager = ChatManager()
