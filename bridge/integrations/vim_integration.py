"""
Enhanced integration module for the Vim plugin.
Supports code completion, chat, and advanced workspace features.
"""

import asyncio
import json
import socket
import threading
import logging
import time
from typing import Dict, Any, Optional, List
from dataclasses import asdict

from bridge.integrations.swe_agent_interface import SWEAgentInterface
from bridge.integrations.code_completion import completion_engine, CompletionContext
from bridge.core.context_manager import context_analyzer
from bridge.integrations.chat_manager import chat_manager, MessageRole, ChatContext
from bridge.workspace.project_analyzer import project_analyzer
from bridge.workspace.file_navigator import file_navigator
from bridge.workspace.refactoring_assistant import refactoring_assistant
from bridge.workspace.code_quality import code_quality_analyzer

logger = logging.getLogger(__name__)


class VimIntegration:
    """Enhanced integration with the Vim plugin."""

    def __init__(self):
        """Initialize the Vim integration."""
        self.swe_agent = SWEAgentInterface()
        self.socket = None
        self.server_thread = None
        self.running = False
        self.active_sessions: Dict[str, Any] = {}
        self.completion_cache: Dict[str, Any] = {}
    
    def start_server(self, host: str = "localhost", port: int = 8081):
        """
        Start the server for Vim plugin communication.
        
        Args:
            host: Host to bind to.
            port: Port to bind to.
        """
        if self.running:
            logger.warning("Server is already running")
            return
        
        self.running = True
        self.server_thread = threading.Thread(
            target=self._run_server,
            args=(host, port),
            daemon=True
        )
        self.server_thread.start()
        logger.info(f"Vim integration server started on {host}:{port}")
    
    def stop_server(self):
        """Stop the server."""
        self.running = False
        if self.socket:
            try:
                self.socket.close()
            except Exception as e:
                logger.error(f"Error closing socket: {e}")
        
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=1)
        
        logger.info("Vim integration server stopped")
    
    def _run_server(self, host: str, port: int):
        """
        Run the server.
        
        Args:
            host: Host to bind to.
            port: Port to bind to.
        """
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind((host, port))
            self.socket.listen(5)
            
            while self.running:
                try:
                    client_socket, address = self.socket.accept()
                    logger.info(f"Connection from {address}")
                    
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket,),
                        daemon=True
                    )
                    client_thread.start()
                
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.running:
                        logger.error(f"Error accepting connection: {e}")
        
        except Exception as e:
            logger.error(f"Error starting server: {e}")
        
        finally:
            if self.socket:
                self.socket.close()
    
    def _handle_client(self, client_socket: socket.socket):
        """
        Handle a client connection.
        
        Args:
            client_socket: Client socket.
        """
        try:
            # Set a timeout to avoid blocking forever
            client_socket.settimeout(60)
            
            # Receive data
            data = b""
            while True:
                chunk = client_socket.recv(4096)
                if not chunk:
                    break
                data += chunk
                
                # Check if we have received a complete message
                if data.endswith(b"\n"):
                    break
            
            if not data:
                logger.warning("No data received from client")
                return
            
            # Parse the request
            try:
                request = json.loads(data.decode("utf-8"))
                response = self._handle_request(request)
                
                # Send the response
                client_socket.sendall(json.dumps(response).encode("utf-8"))
            
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON received: {data.decode('utf-8')}")
                client_socket.sendall(json.dumps({
                    "status": "error",
                    "message": "Invalid JSON"
                }).encode("utf-8"))
        
        except Exception as e:
            logger.error(f"Error handling client: {e}")
        
        finally:
            client_socket.close()
    
    def _handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle a request from the Vim plugin.

        Args:
            request: Request from the Vim plugin.

        Returns:
            Response to send back to the Vim plugin.
        """
        command = request.get("command")
        if not command:
            return {"status": "error", "message": "No command specified"}

        try:
            if command == "run":
                return self._handle_run_command(request)
            elif command == "stop":
                return self._handle_stop_command(request)
            elif command == "ping":
                return {"status": "success", "message": "pong"}
            elif command == "complete":
                return self._handle_completion_command(request)
            elif command == "analyze":
                return self._handle_analyze_command(request)
            elif command == "context":
                return self._handle_context_command(request)
            elif command == "chat":
                return self._handle_chat_command(request)
            elif command == "chat_create":
                return self._handle_chat_create_command(request)
            elif command == "chat_list":
                return self._handle_chat_list_command(request)
            elif command == "chat_history":
                return self._handle_chat_history_command(request)
            elif command == "project_analyze":
                return self._handle_project_analyze_command(request)
            elif command == "file_search":
                return self._handle_file_search_command(request)
            elif command == "symbol_search":
                return self._handle_symbol_search_command(request)
            elif command == "find_definition":
                return self._handle_find_definition_command(request)
            elif command == "find_references":
                return self._handle_find_references_command(request)
            elif command == "refactor_analyze":
                return self._handle_refactor_analyze_command(request)
            elif command == "quality_analyze":
                return self._handle_quality_analyze_command(request)
            elif command == "file_outline":
                return self._handle_file_outline_command(request)
            else:
                return {"status": "error", "message": f"Unknown command: {command}"}
        except Exception as e:
            logger.error(f"Error handling command {command}: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_run_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle the run command."""
        problem_statement = request.get("problem_statement")
        if not problem_statement:
            return {"status": "error", "message": "No problem statement provided"}

        repo_path = request.get("repo_path")
        if not repo_path:
            return {"status": "error", "message": "No repository path provided"}

        model_name = request.get("model_name", "gpt-4")

        result = self.swe_agent.run_agent(
            problem_statement=problem_statement,
            repo_path=repo_path,
            model_name=model_name
        )

        return {"status": result}

    def _handle_stop_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle the stop command."""
        result = self.swe_agent.stop_agent()
        return {"status": "success" if result else "error"}

    def _handle_completion_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle code completion request."""
        try:
            # Validate required fields
            required_fields = ['file_path', 'content', 'cursor_line', 'cursor_column']
            for field in required_fields:
                if field not in request:
                    return {"status": "error", "message": f"Missing required field: {field}"}

            # Create completion context
            context = CompletionContext(
                file_path=request['file_path'],
                content=request['content'],
                cursor_line=request['cursor_line'],
                cursor_column=request['cursor_column'],
                language=request.get('language', 'text'),
                project_root=request.get('project_root'),
                selection=request.get('selection')
            )

            # Get completions synchronously for Vim
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                response = loop.run_until_complete(completion_engine.get_completions(context))
                return {
                    "status": "success",
                    "completions": asdict(response)
                }
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"Error in completion: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_analyze_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle file analysis request."""
        try:
            # Validate required fields
            required_fields = ['file_path', 'content']
            for field in required_fields:
                if field not in request:
                    return {"status": "error", "message": f"Missing required field: {field}"}

            # Analyze file
            file_context = context_analyzer.analyze_file(
                file_path=request['file_path'],
                content=request['content'],
                language=request.get('language')
            )

            return {
                "status": "success",
                "analysis": {
                    "file_path": file_context.file_path,
                    "language": file_context.language,
                    "symbols": [asdict(symbol) for symbol in file_context.symbols],
                    "imports": file_context.imports,
                    "dependencies": file_context.dependencies
                }
            }

        except Exception as e:
            logger.error(f"Error in analysis: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_context_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle context request."""
        try:
            # Validate required fields
            required_fields = ['file_path', 'line', 'column']
            for field in required_fields:
                if field not in request:
                    return {"status": "error", "message": f"Missing required field: {field}"}

            # Get context at position
            symbol = context_analyzer.get_context_at_position(
                file_path=request['file_path'],
                line=request['line'],
                column=request['column']
            )

            return {
                "status": "success",
                "context": asdict(symbol) if symbol else None
            }

        except Exception as e:
            logger.error(f"Error getting context: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_chat_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle chat message request."""
        try:
            # Validate required fields
            required_fields = ['session_id', 'message']
            for field in required_fields:
                if field not in request:
                    return {"status": "error", "message": f"Missing required field: {field}"}

            session_id = request['session_id']
            message = request['message']
            context = request.get('context', {})

            # Create chat context
            chat_context = ChatContext(
                file_path=context.get('file_path'),
                project_root=context.get('project_root'),
                current_selection=context.get('selection'),
                cursor_position=context.get('cursor_position'),
                language=context.get('language')
            )

            # Send message and get response synchronously
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                # Collect all response chunks
                response_chunks = []
                async def collect_response():
                    async for chunk in chat_manager.send_message(session_id, message, context):
                        response_chunks.append(chunk)

                loop.run_until_complete(collect_response())
                full_response = "".join(response_chunks)

                return {
                    "status": "success",
                    "response": full_response,
                    "session_id": session_id
                }
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"Error in chat: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_chat_create_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle chat session creation request."""
        try:
            title = request.get('title')
            context_data = request.get('context', {})

            # Create chat context
            context = ChatContext(
                file_path=context_data.get('file_path'),
                project_root=context_data.get('project_root'),
                language=context_data.get('language'),
                current_selection=context_data.get('selection')
            )

            # Create session
            session_id = chat_manager.create_session(title, context)

            return {
                "status": "success",
                "session_id": session_id,
                "message": "Chat session created successfully"
            }

        except Exception as e:
            logger.error(f"Error creating chat session: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_chat_list_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle chat session list request."""
        try:
            sessions = chat_manager.list_sessions()

            # Convert to simple format for Vim
            session_list = []
            for session in sessions:
                session_list.append({
                    "session_id": session.session_id,
                    "title": session.title,
                    "status": session.status.value,
                    "message_count": len(session.messages),
                    "created_at": session.created_at.isoformat(),
                    "updated_at": session.updated_at.isoformat()
                })

            return {
                "status": "success",
                "sessions": session_list
            }

        except Exception as e:
            logger.error(f"Error listing chat sessions: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_chat_history_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle chat history request."""
        try:
            session_id = request.get('session_id')
            if not session_id:
                return {"status": "error", "message": "Missing session_id"}

            session = chat_manager.get_session(session_id)
            if not session:
                return {"status": "error", "message": "Session not found"}

            # Convert messages to simple format
            messages = []
            for msg in session.messages:
                messages.append({
                    "id": msg.id,
                    "role": msg.role.value,
                    "content": msg.content,
                    "timestamp": msg.timestamp.isoformat()
                })

            return {
                "status": "success",
                "session_id": session_id,
                "messages": messages
            }

        except Exception as e:
            logger.error(f"Error getting chat history: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_project_analyze_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle project analysis request."""
        try:
            project_path = request.get('project_path')
            if not project_path:
                return {"status": "error", "message": "Missing project_path"}

            force_refresh = request.get('force_refresh', False)

            analysis = project_analyzer.analyze_project(project_path, force_refresh)

            return {
                "status": "success",
                "analysis": analysis.to_dict()
            }

        except Exception as e:
            logger.error(f"Error in project analysis: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_file_search_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle file search request."""
        try:
            project_path = request.get('project_path')
            query = request.get('query')

            if not project_path or not query:
                return {"status": "error", "message": "Missing project_path or query"}

            max_results = request.get('max_results', 20)

            matches = file_navigator.search_files(project_path, query, max_results)

            return {
                "status": "success",
                "matches": [match.to_dict() for match in matches]
            }

        except Exception as e:
            logger.error(f"Error in file search: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_symbol_search_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle symbol search request."""
        try:
            project_path = request.get('project_path')
            query = request.get('query')

            if not project_path or not query:
                return {"status": "error", "message": "Missing project_path or query"}

            symbol_type = request.get('symbol_type')
            max_results = request.get('max_results', 20)

            matches = file_navigator.search_symbols(project_path, query, symbol_type, max_results)

            return {
                "status": "success",
                "matches": [match.to_dict() for match in matches]
            }

        except Exception as e:
            logger.error(f"Error in symbol search: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_find_definition_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle find definition request."""
        try:
            project_path = request.get('project_path')
            file_path = request.get('file_path')
            line = request.get('line')
            column = request.get('column')

            if not all([project_path, file_path, line is not None, column is not None]):
                return {"status": "error", "message": "Missing required parameters"}

            suggestion = file_navigator.find_definition(project_path, file_path, line, column)

            return {
                "status": "success",
                "definition": suggestion.to_dict() if suggestion else None
            }

        except Exception as e:
            logger.error(f"Error finding definition: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_find_references_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle find references request."""
        try:
            project_path = request.get('project_path')
            symbol_name = request.get('symbol_name')

            if not project_path or not symbol_name:
                return {"status": "error", "message": "Missing project_path or symbol_name"}

            symbol_type = request.get('symbol_type')

            references = file_navigator.find_references(project_path, symbol_name, symbol_type)

            return {
                "status": "success",
                "references": [ref.to_dict() for ref in references]
            }

        except Exception as e:
            logger.error(f"Error finding references: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_refactor_analyze_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle refactoring analysis request."""
        try:
            file_path = request.get('file_path')
            content = request.get('content')

            if not file_path or not content:
                return {"status": "error", "message": "Missing file_path or content"}

            language = request.get('language')

            report = refactoring_assistant.analyze_file(file_path, content, language)

            return {
                "status": "success",
                "report": report.to_dict()
            }

        except Exception as e:
            logger.error(f"Error in refactoring analysis: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_quality_analyze_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle code quality analysis request."""
        try:
            file_path = request.get('file_path')
            content = request.get('content')

            if not file_path or not content:
                return {"status": "error", "message": "Missing file_path or content"}

            language = request.get('language')

            report = code_quality_analyzer.analyze_file(file_path, content, language)

            return {
                "status": "success",
                "report": report.to_dict()
            }

        except Exception as e:
            logger.error(f"Error in quality analysis: {e}")
            return {"status": "error", "message": str(e)}

    def _handle_file_outline_command(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle file outline request."""
        try:
            project_path = request.get('project_path')
            file_path = request.get('file_path')

            if not project_path or not file_path:
                return {"status": "error", "message": "Missing project_path or file_path"}

            outline = file_navigator.get_file_outline(project_path, file_path)

            return {
                "status": "success",
                "outline": outline
            }

        except Exception as e:
            logger.error(f"Error getting file outline: {e}")
            return {"status": "error", "message": str(e)}


# Singleton instance
vim_integration = VimIntegration()
