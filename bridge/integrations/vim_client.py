"""
Client for communicating with the Vim plugin.
"""

import json
import socket
import argparse
import sys
from typing import Dict, Any, Optional


def send_command(
    command: str,
    host: str = "localhost",
    port: int = 8081,
    **kwargs
) -> Dict[str, Any]:
    """
    Send a command to the bridge server.
    
    Args:
        command: Command to send.
        host: Host to connect to.
        port: Port to connect to.
        **kwargs: Additional arguments to include in the request.
        
    Returns:
        Response from the server.
    """
    # Create the request
    request = {"command": command, **kwargs}
    
    # Connect to the server
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(10)  # 10 second timeout
            sock.connect((host, port))
            
            # Send the request
            sock.sendall(json.dumps(request).encode("utf-8") + b"\n")
            
            # Receive the response
            data = b""
            while True:
                chunk = sock.recv(4096)
                if not chunk:
                    break
                data += chunk
            
            # Parse the response
            if data:
                return json.loads(data.decode("utf-8"))
            else:
                return {"status": "error", "message": "No response from server"}
    
    except socket.timeout:
        return {"status": "error", "message": "Connection timed out"}
    except ConnectionRefusedError:
        return {"status": "error", "message": "Connection refused"}
    except Exception as e:
        return {"status": "error", "message": str(e)}


def main():
    """Main entry point for the Vim client."""
    parser = argparse.ArgumentParser(description="AI Coding Agent Vim Client")
    parser.add_argument(
        "command",
        choices=["run", "stop", "ping", "complete", "analyze", "context", "chat", "chat_create", "chat_list", "chat_history",
                "project_analyze", "file_search", "symbol_search", "find_definition", "find_references",
                "refactor_analyze", "quality_analyze", "file_outline"],
        help="Command to send to the server"
    )
    parser.add_argument(
        "--host",
        default="localhost",
        help="Host to connect to"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8081,
        help="Port to connect to"
    )
    parser.add_argument(
        "--problem-statement",
        help="Problem statement for the 'run' command"
    )
    parser.add_argument(
        "--repo-path",
        help="Repository path for the 'run' command"
    )
    parser.add_argument(
        "--model-name",
        default="gpt-4",
        help="Model name for the 'run' command"
    )
    parser.add_argument(
        "--file-path",
        help="File path for completion and analysis commands"
    )
    parser.add_argument(
        "--content",
        help="File content for completion and analysis commands"
    )
    parser.add_argument(
        "--cursor-line",
        type=int,
        help="Cursor line for completion commands"
    )
    parser.add_argument(
        "--cursor-column",
        type=int,
        help="Cursor column for completion commands"
    )
    parser.add_argument(
        "--language",
        help="Programming language for analysis"
    )
    parser.add_argument(
        "--line",
        type=int,
        help="Line number for context commands"
    )
    parser.add_argument(
        "--column",
        type=int,
        help="Column number for context commands"
    )
    parser.add_argument(
        "--session-id",
        help="Chat session ID for chat commands"
    )
    parser.add_argument(
        "--message",
        help="Message content for chat commands"
    )
    parser.add_argument(
        "--title",
        help="Title for chat session creation"
    )
    parser.add_argument(
        "--project-path",
        help="Project path for workspace commands"
    )
    parser.add_argument(
        "--query",
        help="Search query for file/symbol search"
    )
    parser.add_argument(
        "--symbol-name",
        help="Symbol name for find references"
    )
    parser.add_argument(
        "--symbol-type",
        help="Symbol type filter"
    )
    parser.add_argument(
        "--max-results",
        type=int,
        default=20,
        help="Maximum number of search results"
    )
    parser.add_argument(
        "--force-refresh",
        action="store_true",
        help="Force refresh for project analysis"
    )

    args = parser.parse_args()
    
    # Build the kwargs based on the command
    kwargs = {}
    if args.command == "run":
        if not args.problem_statement:
            print("Error: --problem-statement is required for the 'run' command")
            sys.exit(1)
        if not args.repo_path:
            print("Error: --repo-path is required for the 'run' command")
            sys.exit(1)

        kwargs = {
            "problem_statement": args.problem_statement,
            "repo_path": args.repo_path,
            "model_name": args.model_name
        }

    elif args.command == "complete":
        if not args.file_path:
            print("Error: --file-path is required for the 'complete' command")
            sys.exit(1)
        if not args.content:
            print("Error: --content is required for the 'complete' command")
            sys.exit(1)
        if args.cursor_line is None:
            print("Error: --cursor-line is required for the 'complete' command")
            sys.exit(1)
        if args.cursor_column is None:
            print("Error: --cursor-column is required for the 'complete' command")
            sys.exit(1)

        kwargs = {
            "file_path": args.file_path,
            "content": args.content,
            "cursor_line": args.cursor_line,
            "cursor_column": args.cursor_column,
            "language": args.language
        }

    elif args.command == "analyze":
        if not args.file_path:
            print("Error: --file-path is required for the 'analyze' command")
            sys.exit(1)
        if not args.content:
            print("Error: --content is required for the 'analyze' command")
            sys.exit(1)

        kwargs = {
            "file_path": args.file_path,
            "content": args.content,
            "language": args.language
        }

    elif args.command == "context":
        if not args.file_path:
            print("Error: --file-path is required for the 'context' command")
            sys.exit(1)
        if args.line is None:
            print("Error: --line is required for the 'context' command")
            sys.exit(1)
        if args.column is None:
            print("Error: --column is required for the 'context' command")
            sys.exit(1)

        kwargs = {
            "file_path": args.file_path,
            "line": args.line,
            "column": args.column
        }

    elif args.command == "chat":
        if not args.session_id:
            print("Error: --session-id is required for the 'chat' command")
            sys.exit(1)
        if not args.message:
            print("Error: --message is required for the 'chat' command")
            sys.exit(1)

        kwargs = {
            "session_id": args.session_id,
            "message": args.message,
            "context": {
                "file_path": args.file_path,
                "content": args.content,
                "language": args.language
            }
        }

    elif args.command == "chat_create":
        kwargs = {
            "title": args.title,
            "context": {
                "file_path": args.file_path,
                "language": args.language
            }
        }

    elif args.command == "chat_history":
        if not args.session_id:
            print("Error: --session-id is required for the 'chat_history' command")
            sys.exit(1)

        kwargs = {
            "session_id": args.session_id
        }

    elif args.command == "project_analyze":
        if not args.project_path:
            print("Error: --project-path is required for the 'project_analyze' command")
            sys.exit(1)

        kwargs = {
            "project_path": args.project_path,
            "force_refresh": args.force_refresh
        }

    elif args.command == "file_search":
        if not args.project_path or not args.query:
            print("Error: --project-path and --query are required for the 'file_search' command")
            sys.exit(1)

        kwargs = {
            "project_path": args.project_path,
            "query": args.query,
            "max_results": args.max_results
        }

    elif args.command == "symbol_search":
        if not args.project_path or not args.query:
            print("Error: --project-path and --query are required for the 'symbol_search' command")
            sys.exit(1)

        kwargs = {
            "project_path": args.project_path,
            "query": args.query,
            "symbol_type": args.symbol_type,
            "max_results": args.max_results
        }

    elif args.command == "find_definition":
        if not all([args.project_path, args.file_path, args.line is not None, args.column is not None]):
            print("Error: --project-path, --file-path, --line, and --column are required for 'find_definition'")
            sys.exit(1)

        kwargs = {
            "project_path": args.project_path,
            "file_path": args.file_path,
            "line": args.line,
            "column": args.column
        }

    elif args.command == "find_references":
        if not args.project_path or not args.symbol_name:
            print("Error: --project-path and --symbol-name are required for 'find_references'")
            sys.exit(1)

        kwargs = {
            "project_path": args.project_path,
            "symbol_name": args.symbol_name,
            "symbol_type": args.symbol_type
        }

    elif args.command in ["refactor_analyze", "quality_analyze"]:
        if not args.file_path or not args.content:
            print(f"Error: --file-path and --content are required for '{args.command}'")
            sys.exit(1)

        kwargs = {
            "file_path": args.file_path,
            "content": args.content,
            "language": args.language
        }

    elif args.command == "file_outline":
        if not args.project_path or not args.file_path:
            print("Error: --project-path and --file-path are required for 'file_outline'")
            sys.exit(1)

        kwargs = {
            "project_path": args.project_path,
            "file_path": args.file_path
        }
    
    # Send the command
    response = send_command(
        command=args.command,
        host=args.host,
        port=args.port,
        **kwargs
    )
    
    # Print the response
    print(json.dumps(response, indent=2))


if __name__ == "__main__":
    main()
