"""
Module for managing the SWE-Agent server.
"""

import os
import sys
import time
import signal
import subprocess
from pathlib import Path
from typing import Optional

from bridge.core.config import config, SWE_AGENT_PATH


def start_swe_agent_server() -> Optional[subprocess.Popen]:
    """
    Start the SWE-Agent server.
    
    Returns:
        Subprocess object if the server was started, None otherwise.
    """
    # Check if server is already running
    api_port = config.get("swe_agent", "api_port", default=8000)
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', api_port))
        sock.close()
        if result == 0:
            print(f"SWE-Agent server is already running on port {api_port}")
            return None
    except Exception as e:
        print(f"Error checking if server is running: {e}")
    
    # Start the server
    server_script = SWE_AGENT_PATH / "sweagent" / "api" / "server.py"
    if not server_script.exists():
        print(f"Server script not found at {server_script}")
        print("SWE-Agent server not available - continuing without it")
        return None
    
    try:
        # Create logs directory if it doesn't exist
        logs_dir = Path(config.get("logging", "file", default="logs/bridge.log")).parent
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        # Use the Python 3.11 virtual environment
        project_root = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        python_executable = project_root / "swe_venv" / "bin" / "python"
        
        if not python_executable.exists():
            print(f"Python 3.11 virtual environment not found at {python_executable}")
            print("Using system Python instead")
            python_executable = sys.executable
        
        # Start the server process
        env = os.environ.copy()
        
        # Set API keys in the environment
        anthropic_key = config.get("api_keys", "anthropic")
        openai_key = config.get("api_keys", "openai")
        
        if anthropic_key:
            env["ANTHROPIC_API_KEY"] = anthropic_key
        if openai_key:
            env["OPENAI_API_KEY"] = openai_key
            
        server_process = subprocess.Popen(
            [str(python_executable), str(server_script)],
            env=env,
            cwd=str(SWE_AGENT_PATH),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Give it a moment to start
        time.sleep(2)
        
        # Check if process is still running
        if server_process.poll() is not None:
            stdout, stderr = server_process.communicate()
            print(f"Server failed to start: {stderr}")
            return None
        
        print(f"SWE-Agent server started on port {api_port}")
        return server_process
    
    except Exception as e:
        print(f"Error starting SWE-Agent server: {e}")
        return None


def stop_swe_agent_server(server_process: Optional[subprocess.Popen] = None) -> bool:
    """
    Stop the SWE-Agent server.
    
    Args:
        server_process: Server process to stop. If None, will try to find and stop
                       the server process by port.
    
    Returns:
        True if the server was stopped, False otherwise.
    """
    if server_process:
        try:
            server_process.terminate()
            server_process.wait(timeout=5)
            print("SWE-Agent server stopped")
            return True
        except subprocess.TimeoutExpired:
            server_process.kill()
            print("SWE-Agent server killed")
            return True
        except Exception as e:
            print(f"Error stopping SWE-Agent server: {e}")
            return False
    
    # Try to find and stop the server process by port
    api_port = config.get("swe_agent", "api_port", default=8000)
    try:
        import psutil
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info.get('cmdline', [])
                if len(cmdline) >= 2 and 'python' in cmdline[0] and 'server.py' in cmdline[1]:
                    proc_pid = proc.info['pid']
                    os.kill(proc_pid, signal.SIGTERM)
                    print(f"SWE-Agent server (PID {proc_pid}) stopped")
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
    except ImportError:
        print("psutil not installed, cannot find server process by port")
    
    print("Could not find SWE-Agent server process to stop")
    return False
