"""
Integration components for the AI Coding Agent bridge.

This package contains integrations with external systems like SWE-Agent and Vim.
"""

from .swe_agent_interface import SWEAgentInterface
from .vim_integration import vim_integration
from .server_manager import start_swe_agent_server, stop_swe_agent_server

# Phase 2 components
from .github_integration import (
    GitHubIntegration,
    GitHubRepository,
    GitHubBranch,
    GitHubCommit,
    RepositoryManager
)

__all__ = [
    # Phase 1 components
    'SWEAgentInterface',
    'vim_integration',
    'start_swe_agent_server',
    'stop_swe_agent_server',

    # Phase 2 components
    'GitHubIntegration',
    'GitHubRepository',
    'GitHubBranch',
    'GitHubCommit',
    'RepositoryManager'
]
