"""
GitHub repository integration for the AI Coding Agent bridge.
Provides comprehensive GitHub repository management capabilities.
"""

import os
import json
import shutil
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import requests
import logging

from bridge.utils.env_loader import get_env

logger = logging.getLogger(__name__)


@dataclass
class GitHubRepository:
    """Represents a GitHub repository."""
    owner: str
    name: str
    full_name: str
    clone_url: str
    ssh_url: str
    default_branch: str
    description: Optional[str] = None
    language: Optional[str] = None
    topics: List[str] = None
    
    def __post_init__(self):
        if self.topics is None:
            self.topics = []


@dataclass
class GitHubBranch:
    """Represents a GitHub branch."""
    name: str
    sha: str
    protected: bool = False


@dataclass
class GitHubCommit:
    """Represents a GitHub commit."""
    sha: str
    message: str
    author: str
    date: str
    url: str


class GitHubIntegration:
    """GitHub integration for repository management."""
    
    def __init__(self, token: Optional[str] = None, api_base: str = "https://api.github.com"):
        """
        Initialize GitHub integration.
        
        Args:
            token: GitHub API token. If None, will try to get from environment.
            api_base: GitHub API base URL.
        """
        self.token = token or get_env("GITHUB_TOKEN") or get_env("GITHUB_API_TOKEN")
        self.api_base = api_base.rstrip('/')
        self.session = requests.Session()
        
        if self.token:
            self.session.headers.update({
                'Authorization': f'token {self.token}',
                'Accept': 'application/vnd.github.v3+json'
            })
        else:
            logger.warning("No GitHub token provided. API rate limits will be lower.")
    
    def get_repository(self, owner: str, repo: str) -> Optional[GitHubRepository]:
        """
        Get repository information.
        
        Args:
            owner: Repository owner.
            repo: Repository name.
            
        Returns:
            GitHubRepository object or None if not found.
        """
        try:
            url = f"{self.api_base}/repos/{owner}/{repo}"
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                return GitHubRepository(
                    owner=data['owner']['login'],
                    name=data['name'],
                    full_name=data['full_name'],
                    clone_url=data['clone_url'],
                    ssh_url=data['ssh_url'],
                    default_branch=data['default_branch'],
                    description=data.get('description'),
                    language=data.get('language'),
                    topics=data.get('topics', [])
                )
            elif response.status_code == 404:
                logger.error(f"Repository {owner}/{repo} not found")
                return None
            else:
                logger.error(f"Error fetching repository: {response.status_code} {response.text}")
                return None
                
        except Exception as e:
            logger.exception(f"Error fetching repository {owner}/{repo}: {e}")
            return None
    
    def get_branches(self, owner: str, repo: str) -> List[GitHubBranch]:
        """
        Get repository branches.
        
        Args:
            owner: Repository owner.
            repo: Repository name.
            
        Returns:
            List of GitHubBranch objects.
        """
        try:
            url = f"{self.api_base}/repos/{owner}/{repo}/branches"
            response = self.session.get(url)
            
            if response.status_code == 200:
                branches = []
                for branch_data in response.json():
                    branches.append(GitHubBranch(
                        name=branch_data['name'],
                        sha=branch_data['commit']['sha'],
                        protected=branch_data.get('protected', False)
                    ))
                return branches
            else:
                logger.error(f"Error fetching branches: {response.status_code} {response.text}")
                return []
                
        except Exception as e:
            logger.exception(f"Error fetching branches for {owner}/{repo}: {e}")
            return []
    
    def get_commits(self, owner: str, repo: str, branch: str = None, limit: int = 10) -> List[GitHubCommit]:
        """
        Get repository commits.
        
        Args:
            owner: Repository owner.
            repo: Repository name.
            branch: Branch name (optional).
            limit: Maximum number of commits to fetch.
            
        Returns:
            List of GitHubCommit objects.
        """
        try:
            url = f"{self.api_base}/repos/{owner}/{repo}/commits"
            params = {'per_page': limit}
            if branch:
                params['sha'] = branch
            
            response = self.session.get(url, params=params)
            
            if response.status_code == 200:
                commits = []
                for commit_data in response.json():
                    commits.append(GitHubCommit(
                        sha=commit_data['sha'],
                        message=commit_data['commit']['message'],
                        author=commit_data['commit']['author']['name'],
                        date=commit_data['commit']['author']['date'],
                        url=commit_data['html_url']
                    ))
                return commits
            else:
                logger.error(f"Error fetching commits: {response.status_code} {response.text}")
                return []
                
        except Exception as e:
            logger.exception(f"Error fetching commits for {owner}/{repo}: {e}")
            return []
    
    def clone_repository(self, 
                        owner: str, 
                        repo: str, 
                        target_dir: Path,
                        branch: Optional[str] = None,
                        depth: Optional[int] = None,
                        timeout: int = 300) -> bool:
        """
        Clone a GitHub repository.
        
        Args:
            owner: Repository owner.
            repo: Repository name.
            target_dir: Target directory for cloning.
            branch: Specific branch to clone.
            depth: Clone depth (for shallow clones).
            timeout: Clone timeout in seconds.
            
        Returns:
            True if successful, False otherwise.
        """
        try:
            # Get repository information
            repo_info = self.get_repository(owner, repo)
            if not repo_info:
                return False
            
            # Prepare clone command
            clone_url = repo_info.clone_url
            cmd = ['git', 'clone']
            
            if branch:
                cmd.extend(['--branch', branch])
            
            if depth:
                cmd.extend(['--depth', str(depth)])
            
            cmd.extend([clone_url, str(target_dir)])
            
            # Execute clone
            logger.info(f"Cloning {owner}/{repo} to {target_dir}")
            result = subprocess.run(
                cmd,
                timeout=timeout,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info(f"Successfully cloned {owner}/{repo}")
                return True
            else:
                logger.error(f"Clone failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"Clone timeout after {timeout} seconds")
            return False
        except Exception as e:
            logger.exception(f"Error cloning repository {owner}/{repo}: {e}")
            return False
    
    def setup_repository(self, repo_dir: Path, setup_commands: List[str]) -> bool:
        """
        Run setup commands in the repository directory.
        
        Args:
            repo_dir: Repository directory.
            setup_commands: List of setup commands to run.
            
        Returns:
            True if all commands succeeded, False otherwise.
        """
        try:
            original_dir = os.getcwd()
            os.chdir(repo_dir)
            
            for command in setup_commands:
                logger.info(f"Running setup command: {command}")
                result = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=300
                )
                
                if result.returncode != 0:
                    logger.error(f"Setup command failed: {command}")
                    logger.error(f"Error: {result.stderr}")
                    return False
                else:
                    logger.info(f"Setup command succeeded: {command}")
            
            return True
            
        except Exception as e:
            logger.exception(f"Error running setup commands: {e}")
            return False
        finally:
            os.chdir(original_dir)
    
    def parse_github_url(self, url: str) -> Optional[Tuple[str, str]]:
        """
        Parse GitHub URL to extract owner and repository name.
        
        Args:
            url: GitHub URL.
            
        Returns:
            Tuple of (owner, repo) or None if parsing fails.
        """
        try:
            # Handle different GitHub URL formats
            if url.startswith('https://github.com/'):
                path = url.replace('https://github.com/', '').rstrip('/')
            elif url.startswith('**************:'):
                path = url.replace('**************:', '').rstrip('/')
                if path.endswith('.git'):
                    path = path[:-4]
            else:
                return None
            
            parts = path.split('/')
            if len(parts) >= 2:
                return parts[0], parts[1]
            
            return None
            
        except Exception as e:
            logger.exception(f"Error parsing GitHub URL {url}: {e}")
            return None
    
    def get_rate_limit(self) -> Dict[str, Any]:
        """
        Get current GitHub API rate limit status.
        
        Returns:
            Rate limit information.
        """
        try:
            url = f"{self.api_base}/rate_limit"
            response = self.session.get(url)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Failed to get rate limit: {response.status_code}"}
                
        except Exception as e:
            logger.exception(f"Error getting rate limit: {e}")
            return {"error": str(e)}


class RepositoryManager:
    """High-level repository management interface."""
    
    def __init__(self, workspace_dir: Optional[Path] = None):
        """
        Initialize repository manager.
        
        Args:
            workspace_dir: Base directory for repository operations.
        """
        self.workspace_dir = workspace_dir or Path.cwd() / "workspace"
        self.workspace_dir.mkdir(parents=True, exist_ok=True)
        self.github = GitHubIntegration()
    
    def prepare_repository(self, 
                          repo_config: Dict[str, Any],
                          session_id: str) -> Optional[Path]:
        """
        Prepare a repository for SWE-Agent execution.
        
        Args:
            repo_config: Repository configuration.
            session_id: Session ID for workspace isolation.
            
        Returns:
            Path to prepared repository or None if failed.
        """
        try:
            repo_type = repo_config.get('type', 'auto')
            session_workspace = self.workspace_dir / session_id
            session_workspace.mkdir(parents=True, exist_ok=True)
            
            if repo_type == 'local' or (repo_type == 'auto' and 'path' in repo_config):
                return self._prepare_local_repository(repo_config, session_workspace)
            elif repo_type == 'github' or (repo_type == 'auto' and 'url' in repo_config):
                return self._prepare_github_repository(repo_config, session_workspace)
            elif repo_type == 'preexisting':
                return self._prepare_preexisting_repository(repo_config, session_workspace)
            else:
                logger.error(f"Unknown repository type: {repo_type}")
                return None
                
        except Exception as e:
            logger.exception(f"Error preparing repository: {e}")
            return None
    
    def _prepare_local_repository(self, repo_config: Dict[str, Any], workspace: Path) -> Optional[Path]:
        """Prepare a local repository."""
        repo_path = Path(repo_config['path'])
        if not repo_path.exists():
            logger.error(f"Local repository path does not exist: {repo_path}")
            return None
        
        # Copy to workspace
        target_path = workspace / "repository"
        shutil.copytree(repo_path, target_path)
        
        # Run setup commands if provided
        setup_commands = repo_config.get('setup_commands', [])
        if setup_commands:
            if not self.github.setup_repository(target_path, setup_commands):
                return None
        
        return target_path
    
    def _prepare_github_repository(self, repo_config: Dict[str, Any], workspace: Path) -> Optional[Path]:
        """Prepare a GitHub repository."""
        url = repo_config['url']
        parsed = self.github.parse_github_url(url)
        if not parsed:
            logger.error(f"Invalid GitHub URL: {url}")
            return None
        
        owner, repo = parsed
        target_path = workspace / "repository"
        
        # Clone repository
        success = self.github.clone_repository(
            owner=owner,
            repo=repo,
            target_dir=target_path,
            branch=repo_config.get('branch'),
            depth=repo_config.get('clone_depth'),
            timeout=repo_config.get('clone_timeout', 300)
        )
        
        if not success:
            return None
        
        # Run setup commands if provided
        setup_commands = repo_config.get('setup_commands', [])
        if setup_commands:
            if not self.github.setup_repository(target_path, setup_commands):
                return None
        
        return target_path
    
    def _prepare_preexisting_repository(self, repo_config: Dict[str, Any], workspace: Path) -> Optional[Path]:
        """Prepare a preexisting repository."""
        # For preexisting repositories, just return the configured path
        repo_path = Path(repo_config['path'])
        if not repo_path.exists():
            logger.error(f"Preexisting repository path does not exist: {repo_path}")
            return None
        
        return repo_path
    
    def cleanup_repository(self, session_id: str) -> bool:
        """
        Clean up repository workspace for a session.
        
        Args:
            session_id: Session ID.
            
        Returns:
            True if successful, False otherwise.
        """
        try:
            session_workspace = self.workspace_dir / session_id
            if session_workspace.exists():
                shutil.rmtree(session_workspace)
                logger.info(f"Cleaned up workspace for session {session_id}")
            return True
        except Exception as e:
            logger.exception(f"Error cleaning up workspace for session {session_id}: {e}")
            return False
