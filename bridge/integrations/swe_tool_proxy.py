"""
SWE-Agent Tool Proxy for executing individual SWE-Agent tools through the bridge layer.
Provides secure tool execution while maintaining the bridge architecture pattern.
"""

import asyncio
import json
import logging
import os
import subprocess
import tempfile
import time
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict

from bridge.core.config import config, PROJECT_ROOT
# Override SWE_AGENT_PATH to point to the correct location
SWE_AGENT_PATH = Path("/u/Arun Dev/Python Projects/AI-Coding-Agent/swe-agent")
from bridge.core.tool_security import tool_security, SecurityViolation

logger = logging.getLogger(__name__)


@dataclass
class ToolResult:
    """Result of tool execution."""
    success: bool
    output: str = ""
    error: str = ""
    exit_code: int = 0
    execution_time: float = 0.0
    tool_name: str = ""
    parameters: Dict[str, Any] = None


@dataclass
class ToolDefinition:
    """Definition of a SWE-Agent tool."""
    name: str
    signature: str
    docstring: str
    arguments: List[Dict[str, Any]]
    bundle_path: str
    state_command: Optional[str] = None
    end_name: Optional[str] = None  # For multi-line commands


class SWEToolProxy:
    """Proxy for executing SWE-Agent tools individually."""
    
    def __init__(self):
        """Initialize the tool proxy."""
        self.swe_venv_path = PROJECT_ROOT / "swe_venv"
        self.swe_venv_python = self.swe_venv_path / "bin" / "python"
        self.tools_cache: Dict[str, ToolDefinition] = {}
        self.tools_loaded = False
        
        # Check if SWE environment is available
        self.has_swe_env = self.swe_venv_python.exists()
        if not self.has_swe_env:
            logger.warning(f"SWE Python environment not found at {self.swe_venv_path}")
        
        # Load available tools
        self._load_available_tools()
    
    def _load_available_tools(self) -> None:
        """Load all available SWE-Agent tools from tool bundles."""
        if self.tools_loaded:
            return

        tools_dir = SWE_AGENT_PATH / "tools"
        if not tools_dir.exists():
            logger.warning(f"SWE-Agent tools directory not found: {tools_dir}")
            logger.info("Creating minimal fallback tools")
            self._create_minimal_fallback_tools()
            self.tools_loaded = True
            return

        logger.info("Loading SWE-Agent tools from real tool bundles...")

        # Load tools from all available bundles
        bundle_count = 0
        for bundle_dir in tools_dir.iterdir():
            if not bundle_dir.is_dir():
                continue

            config_file = bundle_dir / "config.yaml"
            if not config_file.exists():
                logger.debug(f"No config.yaml found in {bundle_dir.name}, skipping")
                continue

            try:
                tools_loaded = self._load_bundle_tools(bundle_dir, config_file)
                if tools_loaded > 0:
                    bundle_count += 1
                    logger.info(f"Loaded {tools_loaded} tools from bundle: {bundle_dir.name}")
            except Exception as e:
                logger.error(f"Error loading tools from bundle {bundle_dir.name}: {e}")

        # If no tools were loaded from bundles, create minimal mock tools as fallback
        if len(self.tools_cache) == 0:
            logger.warning("No real SWE-Agent tools found, creating minimal fallback tools")
            self._create_minimal_fallback_tools()

        self.tools_loaded = True
        logger.info(f"Successfully loaded {len(self.tools_cache)} SWE-Agent tools from {bundle_count} bundles")

    def _create_minimal_fallback_tools(self) -> None:
        """Create minimal fallback tools when real SWE-Agent tools are not available."""
        fallback_tools = [
            {
                "name": "str_replace_editor",
                "signature": "str_replace_editor <command> <path> [<file_text>] [<view_range>] [<old_str>] [<new_str>] [<insert_line>]",
                "docstring": "Custom editing tool for viewing, creating and editing files",
                "arguments": [
                    {"name": "command", "type": "string", "description": "The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`.", "required": True, "enum": ["view", "create", "str_replace", "insert", "undo_edit"]},
                    {"name": "path", "type": "string", "description": "Absolute path to file or directory", "required": True},
                    {"name": "file_text", "type": "string", "description": "Required parameter of `create` command", "required": False},
                    {"name": "old_str", "type": "string", "description": "Required parameter of `str_replace` command", "required": False},
                    {"name": "new_str", "type": "string", "description": "Optional parameter of `str_replace` command", "required": False},
                    {"name": "insert_line", "type": "integer", "description": "Required parameter of `insert` command", "required": False},
                    {"name": "view_range", "type": "array", "items": {"type": "integer"}, "description": "Optional parameter of `view` command", "required": False}
                ]
            },
            {
                "name": "find_file",
                "signature": "find_file <file_name> [<dir>]",
                "docstring": "finds all files with the given name or pattern in dir",
                "arguments": [
                    {"name": "file_name", "type": "string", "description": "the name of the file or pattern to search for", "required": True},
                    {"name": "dir", "type": "string", "description": "the directory to search in", "required": False}
                ]
            },
            {
                "name": "search_file",
                "signature": "search_file <search_term> [<file>]",
                "docstring": "searches for search_term in file",
                "arguments": [
                    {"name": "search_term", "type": "string", "description": "the term to search for", "required": True},
                    {"name": "file", "type": "string", "description": "the file to search in", "required": False}
                ]
            },
            {
                "name": "open",
                "signature": "open <path> [<line_number>]",
                "docstring": "opens the file at the given path in the editor",
                "arguments": [
                    {"name": "path", "type": "string", "description": "the path to the file to open", "required": True},
                    {"name": "line_number", "type": "integer", "description": "the line number to move the window to", "required": False}
                ]
            },
            {
                "name": "create",
                "signature": "create <filename>",
                "docstring": "creates and opens a new file with the given name",
                "arguments": [
                    {"name": "filename", "type": "string", "description": "the name of the file to create", "required": True}
                ]
            },
            {
                "name": "goto",
                "signature": "goto <line_number>",
                "docstring": "moves the window to show <line_number>",
                "arguments": [
                    {"name": "line_number", "type": "integer", "description": "the line number to move the window to", "required": True}
                ]
            }
        ]

        for tool_data in fallback_tools:
            tool_def = ToolDefinition(
                name=tool_data["name"],
                signature=tool_data["signature"],
                docstring=tool_data["docstring"],
                arguments=tool_data["arguments"],
                bundle_path="fallback",
                state_command=None
            )
            self.tools_cache[tool_data["name"]] = tool_def
            logger.debug(f"Created fallback tool: {tool_data['name']}")

        logger.info(f"Created {len(fallback_tools)} fallback SWE-Agent tools")
    
    def _load_bundle_tools(self, bundle_dir: Path, config_file: Path) -> int:
        """Load tools from a specific bundle. Returns number of tools loaded."""
        tools_loaded = 0
        try:
            with open(config_file, 'r') as f:
                bundle_config = yaml.safe_load(f)

            tools_config = bundle_config.get('tools', {})
            state_command = bundle_config.get('state_command')

            # Skip empty tool bundles (like registry)
            if not tools_config:
                logger.debug(f"Bundle {bundle_dir.name} has no tools defined")
                return 0

            for tool_name, tool_config in tools_config.items():
                try:
                    # Validate that the tool executable exists
                    tool_executable = bundle_dir / "bin" / tool_name
                    if not tool_executable.exists():
                        logger.warning(f"Tool executable not found: {tool_executable}")
                        continue

                    # Parse tool configuration
                    signature = tool_config.get('signature', f"{tool_name}")
                    docstring = tool_config.get('docstring', f"Execute {tool_name} tool")
                    arguments = tool_config.get('arguments', [])
                    end_name = tool_config.get('end_name')  # For multi-line commands

                    # Clean up signature formatting (remove multi-line YAML formatting)
                    if isinstance(signature, str):
                        signature = ' '.join(signature.split())

                    tool_def = ToolDefinition(
                        name=tool_name,
                        signature=signature,
                        docstring=docstring,
                        arguments=arguments,
                        bundle_path=str(bundle_dir),
                        state_command=state_command
                    )

                    # Add end_name for multi-line commands
                    if end_name:
                        tool_def.end_name = end_name

                    self.tools_cache[tool_name] = tool_def
                    tools_loaded += 1
                    logger.debug(f"Loaded tool: {tool_name} from bundle {bundle_dir.name}")

                except Exception as e:
                    logger.error(f"Error loading tool {tool_name} from {bundle_dir.name}: {e}")

        except Exception as e:
            logger.error(f"Error reading bundle config {config_file}: {e}")

        return tools_loaded
    
    def get_available_tools(self) -> Dict[str, ToolDefinition]:
        """Get all available tools."""
        if not self.tools_loaded:
            self._load_available_tools()
        return self.tools_cache.copy()
    
    def get_tool_definition(self, tool_name: str) -> Optional[ToolDefinition]:
        """Get definition for a specific tool."""
        if not self.tools_loaded:
            self._load_available_tools()
        return self.tools_cache.get(tool_name)
    
    def get_tool_schema(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get OpenAI function calling schema for a tool."""
        tool_def = self.get_tool_definition(tool_name)
        if not tool_def:
            return None

        # Clean up docstring for better display
        description = tool_def.docstring or f"Execute {tool_name} tool"
        if isinstance(description, str):
            # Remove excessive whitespace and normalize
            description = ' '.join(description.split())

        schema = {
            "type": "function",
            "function": {
                "name": tool_name,
                "description": description,
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        }

        # Add signature information if available
        if tool_def.signature:
            schema["function"]["signature"] = tool_def.signature

        # Add end_name for multi-line commands
        if tool_def.end_name:
            schema["function"]["end_name"] = tool_def.end_name

        # Convert arguments to OpenAI schema format
        for arg in tool_def.arguments:
            arg_name = arg.get('name')
            if not arg_name:
                continue

            arg_type = arg.get('type', 'string')
            arg_description = arg.get('description', '')

            arg_schema = {
                "type": arg_type,
                "description": arg_description
            }

            # Handle enum values
            if 'enum' in arg:
                arg_schema['enum'] = arg['enum']

            # Handle array items
            if arg_type == 'array' and 'items' in arg:
                arg_schema['items'] = arg['items']

            # Handle argument formatting (for complex tools like str_replace_editor)
            if 'argument_format' in arg:
                arg_schema['argument_format'] = arg['argument_format']

            schema["function"]["parameters"]["properties"][arg_name] = arg_schema

            # Add to required if specified
            if arg.get('required', False):
                schema["function"]["parameters"]["required"].append(arg_name)

        return schema
    
    def validate_tool_parameters(self, tool_name: str, parameters: Dict[str, Any]) -> List[SecurityViolation]:
        """Validate tool parameters against security rules and tool schema."""
        violations = []
        
        # Security validation
        security_violations = tool_security.validate_tool_execution(tool_name, parameters)
        violations.extend(security_violations)
        
        # Schema validation
        tool_def = self.get_tool_definition(tool_name)
        if not tool_def:
            violations.append(SecurityViolation(
                violation_type="tool_not_found",
                message=f"Tool '{tool_name}' not found",
                parameter="tool_name",
                value=tool_name
            ))
            return violations
        
        # Validate required parameters
        required_params = {
            arg['name'] for arg in tool_def.arguments 
            if arg.get('required', False)
        }
        
        missing_params = required_params - set(parameters.keys())
        if missing_params:
            violations.append(SecurityViolation(
                violation_type="missing_required_parameters",
                message=f"Missing required parameters: {', '.join(missing_params)}",
                parameter="parameters",
                value=str(list(missing_params))
            ))
        
        # Validate parameter types
        for arg in tool_def.arguments:
            arg_name = arg.get('name')
            if arg_name not in parameters:
                continue
            
            param_value = parameters[arg_name]
            expected_type = arg.get('type', 'string')
            
            if not self._validate_parameter_type(param_value, expected_type):
                violations.append(SecurityViolation(
                    violation_type="invalid_parameter_type",
                    message=f"Parameter '{arg_name}' should be of type {expected_type}",
                    parameter=arg_name,
                    value=str(param_value)
                ))
        
        return violations
    
    def _validate_parameter_type(self, value: Any, expected_type: str) -> bool:
        """Validate parameter type."""
        if expected_type == "string":
            return isinstance(value, str)
        elif expected_type == "integer":
            return isinstance(value, int)
        elif expected_type == "number":
            return isinstance(value, (int, float))
        elif expected_type == "boolean":
            return isinstance(value, bool)
        elif expected_type == "array":
            return isinstance(value, (list, tuple))
        elif expected_type == "object":
            return isinstance(value, dict)
        else:
            # Unknown type, assume valid
            return True
    
    async def execute_tool(self, tool_name: str, parameters: Dict[str, Any], 
                          working_directory: str = None, timeout: int = 30) -> ToolResult:
        """
        Execute a SWE-Agent tool with given parameters.
        
        Args:
            tool_name: Name of the tool to execute
            parameters: Tool parameters
            working_directory: Working directory for execution
            timeout: Execution timeout in seconds
            
        Returns:
            ToolResult with execution results
        """
        start_time = time.time()
        
        # Validate parameters
        violations = self.validate_tool_parameters(tool_name, parameters)
        if violations:
            error_msg = tool_security.get_violation_summary(violations)
            return ToolResult(
                success=False,
                error=error_msg,
                execution_time=time.time() - start_time,
                tool_name=tool_name,
                parameters=parameters
            )
        
        # Get tool definition
        tool_def = self.get_tool_definition(tool_name)
        if not tool_def:
            return ToolResult(
                success=False,
                error=f"Tool '{tool_name}' not found",
                execution_time=time.time() - start_time,
                tool_name=tool_name,
                parameters=parameters
            )
        
        # Check if SWE environment is available
        if not self.has_swe_env:
            return ToolResult(
                success=False,
                error="SWE Python environment not available",
                execution_time=time.time() - start_time,
                tool_name=tool_name,
                parameters=parameters
            )
        
        try:
            # Execute the tool
            result = await self._execute_tool_command(tool_def, parameters, working_directory, timeout)
            result.execution_time = time.time() - start_time
            result.tool_name = tool_name
            result.parameters = parameters
            return result
            
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            return ToolResult(
                success=False,
                error=f"Tool execution error: {str(e)}",
                execution_time=time.time() - start_time,
                tool_name=tool_name,
                parameters=parameters
            )
    
    async def _execute_tool_command(self, tool_def: ToolDefinition, parameters: Dict[str, Any],
                                   working_directory: str = None, timeout: int = 30) -> ToolResult:
        """Execute the actual tool command."""
        # For real SWE-Agent tool execution, we need to:
        # 1. Build the command line arguments
        # 2. Execute the tool binary directly
        # 3. Handle multi-line commands properly

        try:
            # Build command arguments
            tool_executable = Path(tool_def.bundle_path) / "bin" / tool_def.name
            if not tool_executable.exists():
                return ToolResult(
                    success=False,
                    error=f"Tool executable not found: {tool_executable}",
                    exit_code=1
                )

            # Build command line arguments based on tool signature and parameters
            cmd_args = [str(tool_executable)]
            cmd_args.extend(self._build_tool_arguments(tool_def, parameters))

            # Set up environment
            env = os.environ.copy()
            env['PYTHONPATH'] = str(SWE_AGENT_PATH)

            # Set working directory
            if working_directory is None:
                working_directory = '/tmp'

            # For multi-line commands, we need to handle stdin input
            stdin_input = None
            if tool_def.end_name and 'replacement_text' in parameters:
                stdin_input = parameters['replacement_text'] + '\n' + tool_def.end_name + '\n'

            # Execute the tool
            process = await asyncio.create_subprocess_exec(
                *cmd_args,
                cwd=working_directory,
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                stdin=asyncio.subprocess.PIPE if stdin_input else None
            )

            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(input=stdin_input.encode() if stdin_input else None),
                    timeout=timeout
                )

                return ToolResult(
                    success=process.returncode == 0,
                    output=stdout.decode('utf-8', errors='replace'),
                    error=stderr.decode('utf-8', errors='replace'),
                    exit_code=process.returncode
                )

            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return ToolResult(
                    success=False,
                    error=f"Tool execution timed out after {timeout} seconds",
                    exit_code=-1
                )

        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error executing tool: {str(e)}",
                exit_code=1
            )

    def _build_tool_arguments(self, tool_def: ToolDefinition, parameters: Dict[str, Any]) -> List[str]:
        """Build command line arguments for tool execution."""
        args = []

        # Handle different argument patterns based on tool type
        if tool_def.name == "str_replace_editor":
            # Special handling for str_replace_editor with its complex argument format
            command = parameters.get('command')
            path = parameters.get('path')

            if command and path:
                args.extend([command, path])

                # Add optional arguments with their specific formats
                for arg_def in tool_def.arguments:
                    arg_name = arg_def.get('name')
                    if arg_name in parameters and arg_name not in ['command', 'path']:
                        arg_format = arg_def.get('argument_format')
                        arg_value = parameters[arg_name]

                        if arg_format:
                            # Use the specific argument format
                            if '{{value|join(\' \')}}' in arg_format:
                                # Handle array joining
                                formatted_value = ' '.join(map(str, arg_value)) if isinstance(arg_value, list) else str(arg_value)
                                args.append(arg_format.replace('{{value|join(\' \')}}', formatted_value).replace('{{value}}', formatted_value))
                            else:
                                args.append(arg_format.replace('{{value}}', str(arg_value)))
                        else:
                            args.append(str(arg_value))

        elif tool_def.name in ["edit"] and "start_line" in parameters:
            # Handle windowed edit tools with line ranges
            start_line = parameters.get('start_line')
            end_line = parameters.get('end_line')
            if start_line is not None and end_line is not None:
                args.append(f"{start_line}:{end_line}")

        else:
            # Standard argument handling for most tools
            for arg_def in tool_def.arguments:
                arg_name = arg_def.get('name')
                if arg_name in parameters:
                    arg_value = parameters[arg_name]

                    # Handle different argument types
                    if isinstance(arg_value, list):
                        args.extend(map(str, arg_value))
                    else:
                        args.append(str(arg_value))

        return args

    def _generate_tool_script(self, tool_def: ToolDefinition, parameters: Dict[str, Any]) -> str:
        """Generate a Python script to execute the tool."""
        # This is a simplified implementation
        # In practice, this would need to properly set up the SWE environment
        
        script = f'''#!/usr/bin/env python3
"""
Generated script for executing SWE-Agent tool: {tool_def.name}
"""

import sys
import os
import json

# Add SWE-Agent to path
sys.path.insert(0, "{SWE_AGENT_PATH}")

def main():
    try:
        # Tool: {tool_def.name}
        # Parameters: {json.dumps(parameters, indent=2)}
        
        # For now, return a mock response
        # TODO: Implement actual tool execution
        
        result = {{
            "tool": "{tool_def.name}",
            "status": "success",
            "message": "Tool execution simulated (not yet fully implemented)",
            "parameters": {json.dumps(parameters)}
        }}
        
        print(json.dumps(result, indent=2))
        return 0
        
    except Exception as e:
        error_result = {{
            "tool": "{tool_def.name}",
            "status": "error",
            "error": str(e),
            "parameters": {json.dumps(parameters)}
        }}
        print(json.dumps(error_result, indent=2), file=sys.stderr)
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
        return script


# Global instance
swe_tool_proxy = SWEToolProxy()
