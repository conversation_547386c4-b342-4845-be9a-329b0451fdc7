"""
Interface module for interacting with the SWE-Agent.
"""

import json
import sys
import time
import requests
from typing import Any, Optional, Callable

from bridge.core.config import config, SWE_AGENT_PATH, PROJECT_ROOT

# Define the path to the SWE-ReX repository
SWE_REX_PATH = PROJECT_ROOT / "swe-rex"

# Define the path to the Python 3.11 virtual environment
SWE_VENV_PATH = PROJECT_ROOT / "swe_venv"
SWE_VENV_PYTHON = SWE_VENV_PATH / "bin" / "python"

# Check if the Python 3.11 virtual environment exists
HAS_SWE_VENV = SWE_VENV_PYTHON.exists()
if not HAS_SWE_VENV:
    print(f"Warning: Python 3.11 virtual environment not found at {SWE_VENV_PATH}")
    print("SWE-agent requires Python 3.11 or higher. Some functionality may not work.")
else:
    print(f"Found Python 3.11 virtual environment at {SWE_VENV_PATH}")

# Check if SWE-agent and SWE-ReX are available
HAS_SWE_AGENT = (SWE_AGENT_PATH / "sweagent" / "__init__.py").exists()
HAS_SWE_REX = (SWE_REX_PATH / "swerex" / "__init__.py").exists()

if not HAS_SWE_AGENT:
    print(f"Warning: SWE-agent not found at {SWE_AGENT_PATH}")
if not HAS_SWE_REX:
    print(f"Warning: SWE-ReX not found at {SWE_REX_PATH}")

# We'll use subprocess to run SWE-agent instead of importing it directly
# This allows us to use the Python 3.11 virtual environment


class SWEAgentInterface:
    """Interface for interacting with the SWE-Agent."""

    def __init__(self):
        """Initialize the SWE-Agent interface."""
        self.api_host = config.get("swe_agent", "api_host", default="localhost")
        self.api_port = config.get("swe_agent", "api_port", default=8000)
        self.base_url = f"http://{self.api_host}:{self.api_port}"
        self.sessions = {}  # Track active sessions
        self._ensure_server_running()
    
    def _ensure_server_running(self) -> None:
        """Ensure the SWE-Agent server is running."""
        try:
            response = requests.get(f"{self.base_url}/", timeout=1)
            if response.status_code == 200:
                print("SWE-Agent server is already running.")
                return
        except requests.exceptions.RequestException:
            pass

        print("Starting SWE-Agent server...")
        # Import here to avoid circular imports
        from bridge.integrations.server_manager import start_swe_agent_server
        server_process = start_swe_agent_server()

        if server_process is None:
            print("SWE-Agent server not available - continuing without it")
            return

        # Wait for server to start
        max_retries = 10
        for _ in range(max_retries):
            try:
                response = requests.get(f"{self.base_url}/", timeout=1)
                if response.status_code == 200:
                    print("SWE-Agent server started successfully.")
                    return
            except requests.exceptions.RequestException:
                pass
            time.sleep(1)

        print("Failed to start SWE-Agent server - continuing without it")
    
    def run_agent(self, 
                  problem_statement: str, 
                  repo_path: str,
                  model_name: str = None,
                  callback: Optional[Callable[[str, Any], None]] = None) -> str:
        """
        Run the SWE-Agent on a problem statement.
        
        Args:
            problem_statement: The problem statement to solve.
            repo_path: Path to the repository to work on.
            model_name: Name of the model to use. If None, uses the one from config.
            callback: Callback function for updates.
            
        Returns:
            Session ID for the run.
        """
        # Use model from config if not specified
        if model_name is None:
            model_name = config.get("swe_agent", "model", default="claude-3-opus-20240229")
        
        # Check if we can use the Python 3.11 virtual environment
        if not HAS_SWE_VENV or not HAS_SWE_AGENT or not HAS_SWE_REX:
            print("Cannot run SWE-agent: Missing required components")
            print(f"  Python 3.11 virtual environment: {HAS_SWE_VENV}")
            print(f"  SWE-agent: {HAS_SWE_AGENT}")
            print(f"  SWE-ReX: {HAS_SWE_REX}")
            return "error"
            
        # Prepare the run configuration
        run_config = {
            "agent": {
                "model": {
                    "model_name": model_name,
                }
            },
            "environment": {
                "image_name": "default",
                "script": "default",
                "repo_path": repo_path,
                "base_commit": "HEAD",
            },
            "problem_statement": {
                "input": problem_statement,
                "type": "text",
            },
            "extra": {
                "test_run": False,
            }
        }
        
        # First, ensure the server is running
        from bridge.integrations.server_manager import start_swe_agent_server
        
        print("Starting SWE-Agent server...")
        server_process = start_swe_agent_server()
        
        if server_process is None:
            print("Failed to start SWE-Agent server or server is already running")
        else:
            print("SWE-Agent server started successfully")
            # Give the server a moment to initialize
            time.sleep(2)
        
        try:
            # Send the run request to the API server
            response = requests.get(
                f"{self.base_url}/run",
                params={"runConfig": json.dumps(run_config)},
                timeout=5
            )
            
            if response.status_code == 202:
                print("SWE-Agent run started successfully.")
                return "success"
            else:
                print(f"Failed to start SWE-Agent run: {response.status_code} {response.text}")
                return "error"
        except requests.exceptions.RequestException as e:
            print(f"Error starting SWE-Agent run: {e}")
            return "error"
    
    def stop_agent(self) -> bool:
        """
        Stop the current SWE-Agent run.
        
        Returns:
            True if successful, False otherwise.
        """
        try:
            response = requests.get(f"{self.base_url}/stop", timeout=5)
            if response.status_code == 202:
                print("SWE-Agent run stopped successfully.")
                return True
            else:
                print(f"Failed to stop SWE-Agent run: {response.status_code} {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"Error stopping SWE-Agent run: {e}")
            return False
