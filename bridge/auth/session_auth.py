"""
Session-based authentication and authorization middleware.
Provides decorators and utilities for securing API endpoints.
"""

import functools
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from flask import request, jsonify, g
from flask_socketio import disconnect

from .token_manager import token_manager, TokenClaims
from .oauth_provider import oauth_provider

logger = logging.getLogger(__name__)


@dataclass
class User:
    """User information."""
    user_id: str
    username: str
    email: str
    full_name: str
    is_active: bool = True
    created_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    preferences: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        if self.created_at:
            data['created_at'] = self.created_at.isoformat()
        if self.last_login:
            data['last_login'] = self.last_login.isoformat()
        return data


class SessionAuth:
    """Session authentication and authorization manager."""
    
    def __init__(self):
        self.users: Dict[str, User] = {}
        self._create_default_user()
    
    def _create_default_user(self):
        """Create default user for development."""
        default_user = User(
            user_id="default_user",
            username="developer",
            email="<EMAIL>",
            full_name="Default Developer",
            created_at=datetime.now(),
            preferences={"theme": "dark", "language": "en"}
        )
        self.users[default_user.user_id] = default_user
        logger.info(f"Created default user: {default_user.username}")
    
    def create_user(self, username: str, email: str, full_name: str, 
                   preferences: Dict[str, Any] = None) -> User:
        """
        Create a new user.
        
        Args:
            username: Unique username.
            email: User email address.
            full_name: User's full name.
            preferences: User preferences.
            
        Returns:
            Created user object.
        """
        user_id = f"user_{len(self.users) + 1}"
        
        user = User(
            user_id=user_id,
            username=username,
            email=email,
            full_name=full_name,
            created_at=datetime.now(),
            preferences=preferences or {}
        )
        
        self.users[user_id] = user
        logger.info(f"Created user: {username} ({user_id})")
        return user
    
    def get_user(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        return self.users.get(user_id)
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        for user in self.users.values():
            if user.username == username:
                return user
        return None
    
    def update_user_login(self, user_id: str):
        """Update user's last login time."""
        user = self.get_user(user_id)
        if user:
            user.last_login = datetime.now()
    
    def authenticate_request(self, required_scope: List[str] = None) -> Optional[TokenClaims]:
        """
        Authenticate current request.
        
        Args:
            required_scope: Required scopes for authorization.
            
        Returns:
            Token claims if authenticated, None otherwise.
        """
        # Get token from Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            return None
        
        # Extract bearer token
        parts = auth_header.split()
        if len(parts) != 2 or parts[0].lower() != 'bearer':
            return None
        
        token = parts[1]
        
        # Validate token
        claims = token_manager.validate_token(token, required_scope)
        if claims:
            # Update user login time
            self.update_user_login(claims.sub)
            
            # Store in Flask g for request context
            g.current_user_id = claims.sub
            g.current_client_id = claims.aud
            g.current_scope = claims.scope
            g.token_claims = claims
        
        return claims
    
    def require_auth(self, required_scope: List[str] = None):
        """
        Decorator to require authentication for Flask routes.
        
        Args:
            required_scope: Required scopes for authorization.
        """
        def decorator(f: Callable) -> Callable:
            @functools.wraps(f)
            def decorated_function(*args, **kwargs):
                claims = self.authenticate_request(required_scope)
                if not claims:
                    return jsonify({
                        "error": "unauthorized",
                        "error_description": "Valid authentication required"
                    }), 401
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    def require_scope(self, required_scope: List[str]):
        """
        Decorator to require specific scopes.
        
        Args:
            required_scope: Required scopes.
        """
        return self.require_auth(required_scope)
    
    def optional_auth(self, f: Callable) -> Callable:
        """
        Decorator for optional authentication.
        Sets user context if authenticated, but doesn't require it.
        """
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            self.authenticate_request()  # Sets g.current_user_id if authenticated
            return f(*args, **kwargs)
        return decorated_function
    
    def get_current_user(self) -> Optional[User]:
        """Get current authenticated user from request context."""
        user_id = getattr(g, 'current_user_id', None)
        if user_id:
            return self.get_user(user_id)
        return None
    
    def get_current_user_id(self) -> Optional[str]:
        """Get current user ID from request context."""
        return getattr(g, 'current_user_id', None)
    
    def get_current_client_id(self) -> Optional[str]:
        """Get current client ID from request context."""
        return getattr(g, 'current_client_id', None)
    
    def get_current_scope(self) -> List[str]:
        """Get current token scope from request context."""
        return getattr(g, 'current_scope', [])
    
    def has_scope(self, scope: str) -> bool:
        """Check if current token has specific scope."""
        current_scope = self.get_current_scope()
        return scope in current_scope
    
    def require_websocket_auth(self, required_scope: List[str] = None):
        """
        Decorator to require authentication for SocketIO events.
        
        Args:
            required_scope: Required scopes for authorization.
        """
        def decorator(f: Callable) -> Callable:
            @functools.wraps(f)
            def decorated_function(*args, **kwargs):
                # Get token from SocketIO session or query parameters
                token = None
                
                # Try to get from session
                from flask import session
                token = session.get('access_token')
                
                # Try to get from query parameters
                if not token:
                    token = request.args.get('access_token')
                
                if not token:
                    disconnect()
                    return
                
                # Validate token
                claims = token_manager.validate_token(token, required_scope)
                if not claims:
                    disconnect()
                    return
                
                # Update user login time
                self.update_user_login(claims.sub)
                
                # Store in session for this connection
                session['user_id'] = claims.sub
                session['client_id'] = claims.aud
                session['scope'] = claims.scope
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    def create_api_key(self, user_id: str, name: str, scope: List[str], 
                      expires_days: int = 365) -> str:
        """
        Create API key for programmatic access.
        
        Args:
            user_id: User identifier.
            name: API key name/description.
            scope: API key scopes.
            expires_days: Expiration in days.
            
        Returns:
            API key token.
        """
        # Use default client for API keys
        client = oauth_provider.get_client("ai-coding-agent-default")
        if not client:
            raise ValueError("Default OAuth client not found")
        
        # Create long-lived access token
        from datetime import timedelta
        original_lifetime = token_manager.access_token_lifetime
        token_manager.access_token_lifetime = timedelta(days=expires_days)
        
        try:
            api_key = token_manager.create_access_token(user_id, client.client_id, scope)
            logger.info(f"Created API key '{name}' for user {user_id}")
            return api_key
        finally:
            token_manager.access_token_lifetime = original_lifetime
    
    def revoke_api_key(self, api_key: str) -> bool:
        """
        Revoke API key.
        
        Args:
            api_key: API key to revoke.
            
        Returns:
            True if revoked successfully.
        """
        try:
            claims = token_manager.validate_token(api_key)
            if claims:
                return token_manager.revoke_token(claims.jti)
        except Exception as e:
            logger.error(f"Error revoking API key: {e}")
        return False
    
    def list_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """
        List active sessions for a user.
        
        Args:
            user_id: User identifier.
            
        Returns:
            List of session information.
        """
        tokens = token_manager.list_user_tokens(user_id, active_only=True)
        
        sessions = []
        for token_info in tokens:
            sessions.append({
                "token_id": token_info.token_id,
                "client_id": token_info.client_id,
                "token_type": token_info.token_type.value,
                "scope": token_info.scope,
                "created_at": token_info.created_at.isoformat(),
                "expires_at": token_info.expires_at.isoformat(),
                "last_used": token_info.last_used.isoformat() if token_info.last_used else None
            })
        
        return sessions
    
    def revoke_user_session(self, user_id: str, token_id: str) -> bool:
        """
        Revoke specific user session.
        
        Args:
            user_id: User identifier.
            token_id: Token ID to revoke.
            
        Returns:
            True if revoked successfully.
        """
        token_info = token_manager.get_token_info(token_id)
        if token_info and token_info.user_id == user_id:
            return token_manager.revoke_token(token_id)
        return False
    
    def get_auth_stats(self) -> Dict[str, Any]:
        """Get authentication statistics."""
        token_stats = token_manager.get_token_stats()
        
        # Count active users
        active_users = len([u for u in self.users.values() if u.is_active])
        
        # Count recent logins (last 24 hours)
        from datetime import timedelta
        recent_cutoff = datetime.now() - timedelta(hours=24)
        recent_logins = len([
            u for u in self.users.values() 
            if u.last_login and u.last_login > recent_cutoff
        ])
        
        return {
            "total_users": len(self.users),
            "active_users": active_users,
            "recent_logins": recent_logins,
            **token_stats
        }


# Global session auth instance
session_auth = SessionAuth()
