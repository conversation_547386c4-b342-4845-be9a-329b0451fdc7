"""
User Management System for AI-Coding-Agent Bridge
Handles user profiles, preferences, and account management.
"""

import hashlib
import json
import logging
import secrets
import time
from dataclasses import dataclass, asdict, field
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List, Set
import uuid

from bridge.auth.oauth_providers import OAuthUser
from bridge.core.config import config

logger = logging.getLogger(__name__)

class UserRole(Enum):
    """User roles for RBAC."""
    ADMIN = "admin"
    PREMIUM = "premium"
    STANDARD = "standard"
    TRIAL = "trial"
    SUSPENDED = "suspended"

class AccountStatus(Enum):
    """Account status."""
    ACTIVE = "active"
    PENDING = "pending"
    SUSPENDED = "suspended"
    DELETED = "deleted"

@dataclass
class UserPreferences:
    """User preferences and settings."""
    theme: str = "dark"
    language: str = "en"
    completion_enabled: bool = True
    swe_tools_enabled: bool = True
    max_completions: int = 5
    auto_save: bool = True
    notifications_enabled: bool = True
    analytics_enabled: bool = True
    custom_settings: Dict[str, Any] = field(default_factory=dict)

@dataclass
class UserProfile:
    """User profile information."""
    user_id: str
    email: str
    name: str
    username: Optional[str] = None
    avatar_url: Optional[str] = None
    role: UserRole = UserRole.STANDARD
    status: AccountStatus = AccountStatus.ACTIVE
    preferences: UserPreferences = field(default_factory=UserPreferences)
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    last_login_at: Optional[float] = None
    email_verified: bool = False
    mfa_enabled: bool = False
    oauth_accounts: List[Dict[str, Any]] = field(default_factory=list)
    api_key: Optional[str] = None
    usage_stats: Dict[str, Any] = field(default_factory=dict)

@dataclass
class UserSession:
    """User session information."""
    session_id: str
    user_id: str
    device_id: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    created_at: float
    last_activity_at: float
    expires_at: float
    is_active: bool = True

class UserManager:
    """User management system."""
    
    def __init__(self):
        # In-memory storage (in production, use database)
        self.users: Dict[str, UserProfile] = {}
        self.sessions: Dict[str, UserSession] = {}
        self.email_to_user_id: Dict[str, str] = {}
        self.username_to_user_id: Dict[str, str] = {}
        self.oauth_to_user_id: Dict[str, str] = {}  # provider:provider_id -> user_id
        
        # Load default admin user
        self._create_default_admin()
        
        logger.info("User Manager initialized")
    
    def _create_default_admin(self):
        """Create default admin user if configured."""
        admin_email = config.get("auth", "admin_email")
        if admin_email:
            admin_user = self.create_user(
                email=admin_email,
                name="Administrator",
                username="admin",
                role=UserRole.ADMIN
            )
            logger.info(f"Created default admin user: {admin_email}")
    
    def create_user(self, email: str, name: str, username: Optional[str] = None,
                   role: UserRole = UserRole.STANDARD, 
                   oauth_user: Optional[OAuthUser] = None) -> UserProfile:
        """Create a new user."""
        # Check if user already exists
        if email in self.email_to_user_id:
            raise ValueError(f"User with email {email} already exists")
        
        if username and username in self.username_to_user_id:
            raise ValueError(f"Username {username} already taken")
        
        # Generate user ID
        user_id = str(uuid.uuid4())
        
        # Create user profile
        user_profile = UserProfile(
            user_id=user_id,
            email=email,
            name=name,
            username=username,
            role=role,
            api_key=self._generate_api_key()
        )
        
        # Add OAuth account if provided
        if oauth_user:
            oauth_account = {
                "provider": oauth_user.provider,
                "provider_id": oauth_user.provider_id,
                "username": oauth_user.username,
                "avatar_url": oauth_user.avatar_url,
                "linked_at": time.time()
            }
            user_profile.oauth_accounts.append(oauth_account)
            user_profile.avatar_url = oauth_user.avatar_url
            user_profile.email_verified = True  # OAuth emails are considered verified
            
            # Map OAuth account to user
            oauth_key = f"{oauth_user.provider}:{oauth_user.provider_id}"
            self.oauth_to_user_id[oauth_key] = user_id
        
        # Store user
        self.users[user_id] = user_profile
        self.email_to_user_id[email] = user_id
        if username:
            self.username_to_user_id[username] = user_id
        
        logger.info(f"Created user: {email} ({user_id})")
        return user_profile
    
    def get_user(self, user_id: str) -> Optional[UserProfile]:
        """Get user by ID."""
        return self.users.get(user_id)
    
    def get_user_by_email(self, email: str) -> Optional[UserProfile]:
        """Get user by email."""
        user_id = self.email_to_user_id.get(email)
        return self.users.get(user_id) if user_id else None
    
    def get_user_by_username(self, username: str) -> Optional[UserProfile]:
        """Get user by username."""
        user_id = self.username_to_user_id.get(username)
        return self.users.get(user_id) if user_id else None
    
    def get_user_by_oauth(self, provider: str, provider_id: str) -> Optional[UserProfile]:
        """Get user by OAuth provider and ID."""
        oauth_key = f"{provider}:{provider_id}"
        user_id = self.oauth_to_user_id.get(oauth_key)
        return self.users.get(user_id) if user_id else None
    
    def link_oauth_account(self, user_id: str, oauth_user: OAuthUser) -> bool:
        """Link OAuth account to existing user."""
        user = self.get_user(user_id)
        if not user:
            return False
        
        oauth_key = f"{oauth_user.provider}:{oauth_user.provider_id}"
        
        # Check if OAuth account is already linked to another user
        if oauth_key in self.oauth_to_user_id:
            existing_user_id = self.oauth_to_user_id[oauth_key]
            if existing_user_id != user_id:
                raise ValueError(f"OAuth account already linked to another user")
            return True  # Already linked to this user
        
        # Add OAuth account
        oauth_account = {
            "provider": oauth_user.provider,
            "provider_id": oauth_user.provider_id,
            "username": oauth_user.username,
            "avatar_url": oauth_user.avatar_url,
            "linked_at": time.time()
        }
        user.oauth_accounts.append(oauth_account)
        user.updated_at = time.time()
        
        # Update avatar if not set
        if not user.avatar_url and oauth_user.avatar_url:
            user.avatar_url = oauth_user.avatar_url
        
        # Map OAuth account to user
        self.oauth_to_user_id[oauth_key] = user_id
        
        logger.info(f"Linked {oauth_user.provider} account to user {user_id}")
        return True
    
    def unlink_oauth_account(self, user_id: str, provider: str, provider_id: str) -> bool:
        """Unlink OAuth account from user."""
        user = self.get_user(user_id)
        if not user:
            return False
        
        # Find and remove OAuth account
        oauth_accounts = [
            acc for acc in user.oauth_accounts
            if not (acc["provider"] == provider and acc["provider_id"] == provider_id)
        ]
        
        if len(oauth_accounts) == len(user.oauth_accounts):
            return False  # Account not found
        
        user.oauth_accounts = oauth_accounts
        user.updated_at = time.time()
        
        # Remove OAuth mapping
        oauth_key = f"{provider}:{provider_id}"
        self.oauth_to_user_id.pop(oauth_key, None)
        
        logger.info(f"Unlinked {provider} account from user {user_id}")
        return True
    
    def update_user(self, user_id: str, updates: Dict[str, Any]) -> bool:
        """Update user profile."""
        user = self.get_user(user_id)
        if not user:
            return False
        
        # Update allowed fields
        allowed_fields = ["name", "username", "avatar_url", "preferences"]
        for field, value in updates.items():
            if field in allowed_fields:
                if field == "username":
                    # Check username availability
                    if value and value in self.username_to_user_id:
                        existing_user_id = self.username_to_user_id[value]
                        if existing_user_id != user_id:
                            raise ValueError(f"Username {value} already taken")
                    
                    # Update username mapping
                    if user.username:
                        self.username_to_user_id.pop(user.username, None)
                    if value:
                        self.username_to_user_id[value] = user_id
                
                elif field == "preferences":
                    # Update preferences
                    if isinstance(value, dict):
                        for pref_key, pref_value in value.items():
                            if hasattr(user.preferences, pref_key):
                                setattr(user.preferences, pref_key, pref_value)
                            else:
                                user.preferences.custom_settings[pref_key] = pref_value
                    continue
                
                setattr(user, field, value)
        
        user.updated_at = time.time()
        logger.info(f"Updated user {user_id}")
        return True
    
    def update_user_role(self, user_id: str, role: UserRole) -> bool:
        """Update user role (admin only)."""
        user = self.get_user(user_id)
        if not user:
            return False
        
        user.role = role
        user.updated_at = time.time()
        logger.info(f"Updated user {user_id} role to {role.value}")
        return True
    
    def suspend_user(self, user_id: str, reason: str = "") -> bool:
        """Suspend user account."""
        user = self.get_user(user_id)
        if not user:
            return False
        
        user.status = AccountStatus.SUSPENDED
        user.updated_at = time.time()
        
        # Revoke all user sessions
        self.revoke_user_sessions(user_id)
        
        logger.info(f"Suspended user {user_id}: {reason}")
        return True
    
    def activate_user(self, user_id: str) -> bool:
        """Activate user account."""
        user = self.get_user(user_id)
        if not user:
            return False
        
        user.status = AccountStatus.ACTIVE
        user.updated_at = time.time()
        logger.info(f"Activated user {user_id}")
        return True
    
    def delete_user(self, user_id: str) -> bool:
        """Delete user account (soft delete)."""
        user = self.get_user(user_id)
        if not user:
            return False
        
        # Mark as deleted
        user.status = AccountStatus.DELETED
        user.updated_at = time.time()
        
        # Revoke all sessions
        self.revoke_user_sessions(user_id)
        
        # Remove from lookup tables (but keep user record for audit)
        self.email_to_user_id.pop(user.email, None)
        if user.username:
            self.username_to_user_id.pop(user.username, None)
        
        # Remove OAuth mappings
        for oauth_account in user.oauth_accounts:
            oauth_key = f"{oauth_account['provider']}:{oauth_account['provider_id']}"
            self.oauth_to_user_id.pop(oauth_key, None)
        
        logger.info(f"Deleted user {user_id}")
        return True
    
    def create_session(self, user_id: str, device_id: Optional[str] = None,
                      ip_address: Optional[str] = None, 
                      user_agent: Optional[str] = None) -> UserSession:
        """Create user session."""
        session_id = str(uuid.uuid4())
        session_ttl = config.get("auth", "session_ttl", default=86400)  # 24 hours
        
        session = UserSession(
            session_id=session_id,
            user_id=user_id,
            device_id=device_id,
            ip_address=ip_address,
            user_agent=user_agent,
            created_at=time.time(),
            last_activity_at=time.time(),
            expires_at=time.time() + session_ttl
        )
        
        self.sessions[session_id] = session
        
        # Update user last login
        user = self.get_user(user_id)
        if user:
            user.last_login_at = time.time()
        
        logger.info(f"Created session {session_id} for user {user_id}")
        return session
    
    def get_session(self, session_id: str) -> Optional[UserSession]:
        """Get session by ID."""
        session = self.sessions.get(session_id)
        if session and session.expires_at > time.time() and session.is_active:
            # Update last activity
            session.last_activity_at = time.time()
            return session
        return None
    
    def revoke_session(self, session_id: str) -> bool:
        """Revoke user session."""
        session = self.sessions.get(session_id)
        if session:
            session.is_active = False
            logger.info(f"Revoked session {session_id}")
            return True
        return False
    
    def revoke_user_sessions(self, user_id: str) -> int:
        """Revoke all sessions for a user."""
        revoked_count = 0
        for session in self.sessions.values():
            if session.user_id == user_id and session.is_active:
                session.is_active = False
                revoked_count += 1
        
        logger.info(f"Revoked {revoked_count} sessions for user {user_id}")
        return revoked_count
    
    def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions."""
        current_time = time.time()
        expired_sessions = [
            session_id for session_id, session in self.sessions.items()
            if session.expires_at < current_time
        ]
        
        for session_id in expired_sessions:
            del self.sessions[session_id]
        
        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
        
        return len(expired_sessions)
    
    def _generate_api_key(self) -> str:
        """Generate API key for user."""
        return f"aca_{secrets.token_urlsafe(32)}"
    
    def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """Get user statistics."""
        user = self.get_user(user_id)
        if not user:
            return {}
        
        active_sessions = sum(
            1 for session in self.sessions.values()
            if session.user_id == user_id and session.is_active and session.expires_at > time.time()
        )
        
        return {
            "user_id": user_id,
            "role": user.role.value,
            "status": user.status.value,
            "created_at": user.created_at,
            "last_login_at": user.last_login_at,
            "active_sessions": active_sessions,
            "oauth_accounts": len(user.oauth_accounts),
            "email_verified": user.email_verified,
            "mfa_enabled": user.mfa_enabled
        }
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get system-wide user statistics."""
        current_time = time.time()
        
        total_users = len(self.users)
        active_users = sum(1 for user in self.users.values() if user.status == AccountStatus.ACTIVE)
        suspended_users = sum(1 for user in self.users.values() if user.status == AccountStatus.SUSPENDED)
        
        active_sessions = sum(
            1 for session in self.sessions.values()
            if session.is_active and session.expires_at > current_time
        )
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "suspended_users": suspended_users,
            "deleted_users": total_users - active_users - suspended_users,
            "active_sessions": active_sessions,
            "total_sessions": len(self.sessions)
        }

# Global user manager instance
user_manager = UserManager()
