"""
OAuth 2.0 provider implementation for secure authentication.
Supports authorization code flow, client credentials, and token refresh.
"""

import base64
import hashlib
import json
import logging
import secrets
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import threading

logger = logging.getLogger(__name__)


class GrantType(Enum):
    """OAuth 2.0 grant types."""
    AUTHORIZATION_CODE = "authorization_code"
    CLIENT_CREDENTIALS = "client_credentials"
    REFRESH_TOKEN = "refresh_token"
    DEVICE_CODE = "urn:ietf:params:oauth:grant-type:device_code"


class TokenType(Enum):
    """Token types."""
    ACCESS_TOKEN = "access_token"
    REFRESH_TOKEN = "refresh_token"
    ID_TOKEN = "id_token"


@dataclass
class OAuthClient:
    """OAuth client registration."""
    client_id: str
    client_secret: str
    client_name: str
    redirect_uris: List[str]
    grant_types: List[GrantType]
    scope: List[str]
    created_at: datetime
    is_active: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['grant_types'] = [gt.value for gt in self.grant_types]
        data['created_at'] = self.created_at.isoformat()
        return data


@dataclass
class AuthorizationCode:
    """Authorization code for OAuth flow."""
    code: str
    client_id: str
    user_id: str
    redirect_uri: str
    scope: List[str]
    code_challenge: Optional[str]
    code_challenge_method: Optional[str]
    created_at: datetime
    expires_at: datetime
    is_used: bool = False


@dataclass
class AccessToken:
    """Access token."""
    token: str
    token_type: str
    client_id: str
    user_id: str
    scope: List[str]
    created_at: datetime
    expires_at: datetime
    is_revoked: bool = False


@dataclass
class RefreshToken:
    """Refresh token."""
    token: str
    client_id: str
    user_id: str
    scope: List[str]
    created_at: datetime
    expires_at: datetime
    is_revoked: bool = False


@dataclass
class DeviceCode:
    """Device code for device flow."""
    device_code: str
    user_code: str
    verification_uri: str
    verification_uri_complete: str
    client_id: str
    scope: List[str]
    created_at: datetime
    expires_at: datetime
    interval: int = 5
    user_id: Optional[str] = None
    is_authorized: bool = False


class OAuthProvider:
    """OAuth 2.0 provider implementation."""
    
    def __init__(self, issuer: str = "http://localhost:8080"):
        self.issuer = issuer
        self.clients: Dict[str, OAuthClient] = {}
        self.authorization_codes: Dict[str, AuthorizationCode] = {}
        self.access_tokens: Dict[str, AccessToken] = {}
        self.refresh_tokens: Dict[str, RefreshToken] = {}
        self.device_codes: Dict[str, DeviceCode] = {}
        self.lock = threading.RLock()
        
        # Default configuration
        self.access_token_lifetime = timedelta(hours=1)
        self.refresh_token_lifetime = timedelta(days=30)
        self.authorization_code_lifetime = timedelta(minutes=10)
        self.device_code_lifetime = timedelta(minutes=15)
        
        # Create default client for development
        self._create_default_client()
    
    def _create_default_client(self):
        """Create a default OAuth client for development."""
        default_client = OAuthClient(
            client_id="ai-coding-agent-default",
            client_secret=self._generate_client_secret(),
            client_name="AI Coding Agent Default Client",
            redirect_uris=["http://localhost:8080/auth/callback", "urn:ietf:wg:oauth:2.0:oob"],
            grant_types=[GrantType.AUTHORIZATION_CODE, GrantType.CLIENT_CREDENTIALS, GrantType.REFRESH_TOKEN, GrantType.DEVICE_CODE],
            scope=["read", "write", "admin"],
            created_at=datetime.now()
        )
        self.clients[default_client.client_id] = default_client
        logger.info(f"Created default OAuth client: {default_client.client_id}")
    
    def register_client(self, client_name: str, redirect_uris: List[str], 
                       grant_types: List[GrantType], scope: List[str]) -> OAuthClient:
        """
        Register a new OAuth client.
        
        Args:
            client_name: Human-readable client name.
            redirect_uris: List of valid redirect URIs.
            grant_types: Supported grant types.
            scope: Requested scopes.
            
        Returns:
            Registered OAuth client.
        """
        with self.lock:
            client_id = f"client_{uuid.uuid4().hex[:16]}"
            client_secret = self._generate_client_secret()
            
            client = OAuthClient(
                client_id=client_id,
                client_secret=client_secret,
                client_name=client_name,
                redirect_uris=redirect_uris,
                grant_types=grant_types,
                scope=scope,
                created_at=datetime.now()
            )
            
            self.clients[client_id] = client
            logger.info(f"Registered OAuth client: {client_id}")
            return client
    
    def get_client(self, client_id: str) -> Optional[OAuthClient]:
        """Get OAuth client by ID."""
        return self.clients.get(client_id)
    
    def validate_client(self, client_id: str, client_secret: str = None) -> bool:
        """Validate OAuth client credentials."""
        client = self.get_client(client_id)
        if not client or not client.is_active:
            return False
        
        if client_secret is not None:
            return client.client_secret == client_secret
        
        return True
    
    def create_authorization_code(self, client_id: str, user_id: str, redirect_uri: str,
                                scope: List[str], code_challenge: str = None,
                                code_challenge_method: str = None) -> str:
        """
        Create authorization code for OAuth flow.
        
        Args:
            client_id: OAuth client ID.
            user_id: User identifier.
            redirect_uri: Redirect URI.
            scope: Requested scopes.
            code_challenge: PKCE code challenge.
            code_challenge_method: PKCE code challenge method.
            
        Returns:
            Authorization code.
        """
        with self.lock:
            code = self._generate_authorization_code()
            
            auth_code = AuthorizationCode(
                code=code,
                client_id=client_id,
                user_id=user_id,
                redirect_uri=redirect_uri,
                scope=scope,
                code_challenge=code_challenge,
                code_challenge_method=code_challenge_method,
                created_at=datetime.now(),
                expires_at=datetime.now() + self.authorization_code_lifetime
            )
            
            self.authorization_codes[code] = auth_code
            logger.info(f"Created authorization code for client {client_id}, user {user_id}")
            return code
    
    def exchange_authorization_code(self, code: str, client_id: str, client_secret: str,
                                  redirect_uri: str, code_verifier: str = None) -> Tuple[str, str, int]:
        """
        Exchange authorization code for access token.
        
        Args:
            code: Authorization code.
            client_id: OAuth client ID.
            client_secret: OAuth client secret.
            redirect_uri: Redirect URI.
            code_verifier: PKCE code verifier.
            
        Returns:
            Tuple of (access_token, refresh_token, expires_in).
        """
        with self.lock:
            # Validate client
            if not self.validate_client(client_id, client_secret):
                raise ValueError("Invalid client credentials")
            
            # Get authorization code
            auth_code = self.authorization_codes.get(code)
            if not auth_code:
                raise ValueError("Invalid authorization code")
            
            # Check if code is expired or used
            if auth_code.is_used or datetime.now() > auth_code.expires_at:
                raise ValueError("Authorization code expired or already used")
            
            # Validate redirect URI
            if auth_code.redirect_uri != redirect_uri:
                raise ValueError("Invalid redirect URI")
            
            # Validate PKCE if present
            if auth_code.code_challenge:
                if not code_verifier:
                    raise ValueError("Code verifier required for PKCE")
                
                if not self._verify_pkce(code_verifier, auth_code.code_challenge, auth_code.code_challenge_method):
                    raise ValueError("Invalid code verifier")
            
            # Mark code as used
            auth_code.is_used = True
            
            # Create tokens
            access_token = self._create_access_token(client_id, auth_code.user_id, auth_code.scope)
            refresh_token = self._create_refresh_token(client_id, auth_code.user_id, auth_code.scope)
            
            expires_in = int(self.access_token_lifetime.total_seconds())
            
            logger.info(f"Exchanged authorization code for tokens: client {client_id}, user {auth_code.user_id}")
            return access_token, refresh_token, expires_in
    
    def create_device_code(self, client_id: str, scope: List[str]) -> DeviceCode:
        """
        Create device code for device flow.
        
        Args:
            client_id: OAuth client ID.
            scope: Requested scopes.
            
        Returns:
            Device code object.
        """
        with self.lock:
            device_code = self._generate_device_code()
            user_code = self._generate_user_code()
            
            verification_uri = f"{self.issuer}/auth/device"
            verification_uri_complete = f"{verification_uri}?user_code={user_code}"
            
            device_code_obj = DeviceCode(
                device_code=device_code,
                user_code=user_code,
                verification_uri=verification_uri,
                verification_uri_complete=verification_uri_complete,
                client_id=client_id,
                scope=scope,
                created_at=datetime.now(),
                expires_at=datetime.now() + self.device_code_lifetime
            )
            
            self.device_codes[device_code] = device_code_obj
            logger.info(f"Created device code for client {client_id}")
            return device_code_obj
    
    def authorize_device_code(self, user_code: str, user_id: str) -> bool:
        """
        Authorize device code with user consent.
        
        Args:
            user_code: User code from device flow.
            user_id: User identifier.
            
        Returns:
            True if authorization successful.
        """
        with self.lock:
            # Find device code by user code
            device_code_obj = None
            for dc in self.device_codes.values():
                if dc.user_code == user_code and not dc.is_authorized:
                    device_code_obj = dc
                    break
            
            if not device_code_obj:
                return False
            
            # Check if expired
            if datetime.now() > device_code_obj.expires_at:
                return False
            
            # Authorize
            device_code_obj.user_id = user_id
            device_code_obj.is_authorized = True
            
            logger.info(f"Authorized device code for user {user_id}")
            return True
    
    def poll_device_code(self, device_code: str, client_id: str) -> Tuple[Optional[str], Optional[str], Optional[int], Optional[str]]:
        """
        Poll device code for authorization.
        
        Args:
            device_code: Device code.
            client_id: OAuth client ID.
            
        Returns:
            Tuple of (access_token, refresh_token, expires_in, error).
        """
        with self.lock:
            device_code_obj = self.device_codes.get(device_code)
            if not device_code_obj or device_code_obj.client_id != client_id:
                return None, None, None, "invalid_request"
            
            # Check if expired
            if datetime.now() > device_code_obj.expires_at:
                return None, None, None, "expired_token"
            
            # Check if authorized
            if not device_code_obj.is_authorized:
                return None, None, None, "authorization_pending"
            
            # Create tokens
            access_token = self._create_access_token(client_id, device_code_obj.user_id, device_code_obj.scope)
            refresh_token = self._create_refresh_token(client_id, device_code_obj.user_id, device_code_obj.scope)
            expires_in = int(self.access_token_lifetime.total_seconds())
            
            # Remove device code
            del self.device_codes[device_code]
            
            logger.info(f"Device code flow completed for client {client_id}, user {device_code_obj.user_id}")
            return access_token, refresh_token, expires_in, None
    
    def _create_access_token(self, client_id: str, user_id: str, scope: List[str]) -> str:
        """Create access token."""
        token = self._generate_token()
        
        access_token = AccessToken(
            token=token,
            token_type="Bearer",
            client_id=client_id,
            user_id=user_id,
            scope=scope,
            created_at=datetime.now(),
            expires_at=datetime.now() + self.access_token_lifetime
        )
        
        self.access_tokens[token] = access_token
        return token
    
    def _create_refresh_token(self, client_id: str, user_id: str, scope: List[str]) -> str:
        """Create refresh token."""
        token = self._generate_token()
        
        refresh_token = RefreshToken(
            token=token,
            client_id=client_id,
            user_id=user_id,
            scope=scope,
            created_at=datetime.now(),
            expires_at=datetime.now() + self.refresh_token_lifetime
        )
        
        self.refresh_tokens[token] = refresh_token
        return token
    
    def _generate_client_secret(self) -> str:
        """Generate client secret."""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    
    def _generate_authorization_code(self) -> str:
        """Generate authorization code."""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    
    def _generate_token(self) -> str:
        """Generate access/refresh token."""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    
    def _generate_device_code(self) -> str:
        """Generate device code."""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    
    def _generate_user_code(self) -> str:
        """Generate user-friendly code."""
        # Generate 8-character alphanumeric code
        chars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"  # Exclude confusing characters
        return ''.join(secrets.choice(chars) for _ in range(8))
    
    def _verify_pkce(self, code_verifier: str, code_challenge: str, method: str) -> bool:
        """Verify PKCE code challenge."""
        if method == "S256":
            computed_challenge = base64.urlsafe_b64encode(
                hashlib.sha256(code_verifier.encode('utf-8')).digest()
            ).decode('utf-8').rstrip('=')
            return computed_challenge == code_challenge
        elif method == "plain":
            return code_verifier == code_challenge
        else:
            return False


# Global OAuth provider instance
oauth_provider = OAuthProvider()
