"""
OAuth 2.0 Provider Integration for AI-Coding-Agent Bridge
Supports GitHub, Google, Microsoft, and generic OAuth providers.
"""

import asyncio
import base64
import hashlib
import json
import logging
import secrets
import time
import urllib.parse
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List, Tuple
import requests

from bridge.core.config import config

logger = logging.getLogger(__name__)

@dataclass
class OAuthConfig:
    """OAuth provider configuration."""
    client_id: str
    client_secret: str
    redirect_uri: str
    scope: List[str]
    authorization_url: str
    token_url: str
    user_info_url: str
    revoke_url: Optional[str] = None
    extra_params: Dict[str, Any] = None

@dataclass
class OAuthUser:
    """OAuth user information."""
    provider: str
    provider_id: str
    email: str
    name: str
    username: Optional[str] = None
    avatar_url: Optional[str] = None
    raw_data: Dict[str, Any] = None

@dataclass
class OAuthToken:
    """OAuth token information."""
    access_token: str
    token_type: str
    expires_in: Optional[int] = None
    refresh_token: Optional[str] = None
    scope: Optional[str] = None
    created_at: float = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()

    @property
    def is_expired(self) -> bool:
        """Check if token is expired."""
        if self.expires_in is None:
            return False
        return time.time() > (self.created_at + self.expires_in - 60)  # 60s buffer

class OAuthProvider(ABC):
    """Abstract OAuth provider base class."""
    
    def __init__(self, config: OAuthConfig):
        self.config = config
        self.session = requests.Session()
        
    @abstractmethod
    def get_provider_name(self) -> str:
        """Get provider name."""
        pass
    
    def generate_state(self) -> str:
        """Generate secure state parameter."""
        return secrets.token_urlsafe(32)
    
    def generate_pkce_challenge(self) -> Tuple[str, str]:
        """Generate PKCE code verifier and challenge."""
        code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')
        return code_verifier, code_challenge
    
    def get_authorization_url(self, state: str, code_challenge: Optional[str] = None) -> str:
        """Get authorization URL for OAuth flow."""
        params = {
            'client_id': self.config.client_id,
            'redirect_uri': self.config.redirect_uri,
            'scope': ' '.join(self.config.scope),
            'state': state,
            'response_type': 'code'
        }
        
        if code_challenge:
            params.update({
                'code_challenge': code_challenge,
                'code_challenge_method': 'S256'
            })
        
        if self.config.extra_params:
            params.update(self.config.extra_params)
        
        return f"{self.config.authorization_url}?{urllib.parse.urlencode(params)}"
    
    def exchange_code_for_token(self, code: str, code_verifier: Optional[str] = None) -> OAuthToken:
        """Exchange authorization code for access token."""
        data = {
            'client_id': self.config.client_id,
            'client_secret': self.config.client_secret,
            'code': code,
            'redirect_uri': self.config.redirect_uri,
            'grant_type': 'authorization_code'
        }
        
        if code_verifier:
            data['code_verifier'] = code_verifier
        
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        try:
            response = self.session.post(self.config.token_url, data=data, headers=headers, timeout=30)
            response.raise_for_status()
            
            token_data = response.json()
            return OAuthToken(
                access_token=token_data['access_token'],
                token_type=token_data.get('token_type', 'Bearer'),
                expires_in=token_data.get('expires_in'),
                refresh_token=token_data.get('refresh_token'),
                scope=token_data.get('scope')
            )
            
        except requests.RequestException as e:
            logger.error(f"Token exchange failed for {self.get_provider_name()}: {e}")
            raise OAuthError(f"Token exchange failed: {e}")
    
    def refresh_access_token(self, refresh_token: str) -> OAuthToken:
        """Refresh access token using refresh token."""
        if not refresh_token:
            raise OAuthError("No refresh token provided")
        
        data = {
            'client_id': self.config.client_id,
            'client_secret': self.config.client_secret,
            'refresh_token': refresh_token,
            'grant_type': 'refresh_token'
        }
        
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        try:
            response = self.session.post(self.config.token_url, data=data, headers=headers, timeout=30)
            response.raise_for_status()
            
            token_data = response.json()
            return OAuthToken(
                access_token=token_data['access_token'],
                token_type=token_data.get('token_type', 'Bearer'),
                expires_in=token_data.get('expires_in'),
                refresh_token=token_data.get('refresh_token', refresh_token),
                scope=token_data.get('scope')
            )
            
        except requests.RequestException as e:
            logger.error(f"Token refresh failed for {self.get_provider_name()}: {e}")
            raise OAuthError(f"Token refresh failed: {e}")
    
    def revoke_token(self, token: str) -> bool:
        """Revoke access token."""
        if not self.config.revoke_url:
            logger.warning(f"No revoke URL configured for {self.get_provider_name()}")
            return False
        
        data = {
            'token': token,
            'client_id': self.config.client_id,
            'client_secret': self.config.client_secret
        }
        
        try:
            response = self.session.post(self.config.revoke_url, data=data, timeout=30)
            return response.status_code in [200, 204]
            
        except requests.RequestException as e:
            logger.error(f"Token revocation failed for {self.get_provider_name()}: {e}")
            return False
    
    @abstractmethod
    def get_user_info(self, token: OAuthToken) -> OAuthUser:
        """Get user information using access token."""
        pass

class GitHubOAuthProvider(OAuthProvider):
    """GitHub OAuth 2.0 provider."""
    
    def get_provider_name(self) -> str:
        return "github"
    
    def get_user_info(self, token: OAuthToken) -> OAuthUser:
        """Get GitHub user information."""
        headers = {
            'Authorization': f'{token.token_type} {token.access_token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'AI-Coding-Agent/1.0'
        }
        
        try:
            response = self.session.get(self.config.user_info_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            user_data = response.json()
            return OAuthUser(
                provider="github",
                provider_id=str(user_data['id']),
                email=user_data.get('email', ''),
                name=user_data.get('name', ''),
                username=user_data.get('login', ''),
                avatar_url=user_data.get('avatar_url', ''),
                raw_data=user_data
            )
            
        except requests.RequestException as e:
            logger.error(f"Failed to get GitHub user info: {e}")
            raise OAuthError(f"Failed to get user info: {e}")

class GoogleOAuthProvider(OAuthProvider):
    """Google OAuth 2.0 provider."""
    
    def get_provider_name(self) -> str:
        return "google"
    
    def get_user_info(self, token: OAuthToken) -> OAuthUser:
        """Get Google user information."""
        headers = {
            'Authorization': f'{token.token_type} {token.access_token}'
        }
        
        try:
            response = self.session.get(self.config.user_info_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            user_data = response.json()
            return OAuthUser(
                provider="google",
                provider_id=user_data['sub'],
                email=user_data.get('email', ''),
                name=user_data.get('name', ''),
                username=user_data.get('preferred_username', ''),
                avatar_url=user_data.get('picture', ''),
                raw_data=user_data
            )
            
        except requests.RequestException as e:
            logger.error(f"Failed to get Google user info: {e}")
            raise OAuthError(f"Failed to get user info: {e}")

class MicrosoftOAuthProvider(OAuthProvider):
    """Microsoft OAuth 2.0 provider."""
    
    def get_provider_name(self) -> str:
        return "microsoft"
    
    def get_user_info(self, token: OAuthToken) -> OAuthUser:
        """Get Microsoft user information."""
        headers = {
            'Authorization': f'{token.token_type} {token.access_token}'
        }
        
        try:
            response = self.session.get(self.config.user_info_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            user_data = response.json()
            return OAuthUser(
                provider="microsoft",
                provider_id=user_data['id'],
                email=user_data.get('mail', user_data.get('userPrincipalName', '')),
                name=user_data.get('displayName', ''),
                username=user_data.get('userPrincipalName', ''),
                avatar_url=None,  # Microsoft Graph doesn't provide avatar URL directly
                raw_data=user_data
            )
            
        except requests.RequestException as e:
            logger.error(f"Failed to get Microsoft user info: {e}")
            raise OAuthError(f"Failed to get user info: {e}")

class OAuthError(Exception):
    """OAuth-related error."""
    pass

class OAuthManager:
    """OAuth provider manager."""
    
    def __init__(self):
        self.providers: Dict[str, OAuthProvider] = {}
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize OAuth providers from configuration."""
        # GitHub OAuth
        github_config = self._get_provider_config('github')
        if github_config:
            self.providers['github'] = GitHubOAuthProvider(github_config)
        
        # Google OAuth
        google_config = self._get_provider_config('google')
        if google_config:
            self.providers['google'] = GoogleOAuthProvider(google_config)
        
        # Microsoft OAuth
        microsoft_config = self._get_provider_config('microsoft')
        if microsoft_config:
            self.providers['microsoft'] = MicrosoftOAuthProvider(microsoft_config)
        
        logger.info(f"Initialized OAuth providers: {list(self.providers.keys())}")
    
    def _get_provider_config(self, provider_name: str) -> Optional[OAuthConfig]:
        """Get OAuth configuration for provider."""
        try:
            provider_config = config.get("oauth", provider_name, default={})
            if not provider_config.get("client_id"):
                logger.warning(f"No OAuth configuration found for {provider_name}")
                return None
            
            # Provider-specific configurations
            if provider_name == "github":
                return OAuthConfig(
                    client_id=provider_config["client_id"],
                    client_secret=provider_config["client_secret"],
                    redirect_uri=provider_config.get("redirect_uri", "http://localhost:8080/auth/callback/github"),
                    scope=provider_config.get("scope", ["user:email", "read:user"]),
                    authorization_url="https://github.com/login/oauth/authorize",
                    token_url="https://github.com/login/oauth/access_token",
                    user_info_url="https://api.github.com/user",
                    revoke_url="https://api.github.com/applications/{client_id}/grant"
                )
            elif provider_name == "google":
                return OAuthConfig(
                    client_id=provider_config["client_id"],
                    client_secret=provider_config["client_secret"],
                    redirect_uri=provider_config.get("redirect_uri", "http://localhost:8080/auth/callback/google"),
                    scope=provider_config.get("scope", ["openid", "email", "profile"]),
                    authorization_url="https://accounts.google.com/o/oauth2/v2/auth",
                    token_url="https://oauth2.googleapis.com/token",
                    user_info_url="https://openidconnect.googleapis.com/v1/userinfo",
                    revoke_url="https://oauth2.googleapis.com/revoke"
                )
            elif provider_name == "microsoft":
                tenant_id = provider_config.get("tenant_id", "common")
                return OAuthConfig(
                    client_id=provider_config["client_id"],
                    client_secret=provider_config["client_secret"],
                    redirect_uri=provider_config.get("redirect_uri", "http://localhost:8080/auth/callback/microsoft"),
                    scope=provider_config.get("scope", ["openid", "email", "profile", "User.Read"]),
                    authorization_url=f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/authorize",
                    token_url=f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token",
                    user_info_url="https://graph.microsoft.com/v1.0/me"
                )
            
        except Exception as e:
            logger.error(f"Failed to load OAuth config for {provider_name}: {e}")
            return None
    
    def get_provider(self, provider_name: str) -> Optional[OAuthProvider]:
        """Get OAuth provider by name."""
        return self.providers.get(provider_name)
    
    def get_available_providers(self) -> List[str]:
        """Get list of available OAuth providers."""
        return list(self.providers.keys())
    
    def is_provider_available(self, provider_name: str) -> bool:
        """Check if OAuth provider is available."""
        return provider_name in self.providers

# Global OAuth manager instance
oauth_manager = OAuthManager()
