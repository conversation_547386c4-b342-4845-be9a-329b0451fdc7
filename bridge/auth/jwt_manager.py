"""
JWT Token Management System for AI-Coding-Agent Bridge
Handles JWT token generation, validation, refresh, and revocation.
"""

import hashlib
import json
import logging
import secrets
import time
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Set
import jwt
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa

from bridge.core.config import config

logger = logging.getLogger(__name__)

@dataclass
class JWTClaims:
    """JWT token claims."""
    sub: str  # Subject (user ID)
    iss: str  # Issuer
    aud: str  # Audience
    exp: int  # Expiration time
    iat: int  # Issued at
    jti: str  # JWT ID
    scope: List[str]  # Permissions/scopes
    user_data: Dict[str, Any]  # Additional user data
    session_id: str  # Session identifier
    device_id: Optional[str] = None  # Device identifier
    ip_address: Optional[str] = None  # Client IP address

@dataclass
class RefreshToken:
    """Refresh token information."""
    token_id: str
    user_id: str
    session_id: str
    device_id: Optional[str]
    expires_at: float
    created_at: float
    last_used_at: float
    is_revoked: bool = False

class JWTManager:
    """JWT token management system."""
    
    def __init__(self):
        self.algorithm = "RS256"
        self.issuer = config.get("jwt", "issuer", default="ai-coding-agent")
        self.audience = config.get("jwt", "audience", default="ai-coding-agent-api")
        self.access_token_ttl = config.get("jwt", "access_token_ttl", default=3600)  # 1 hour
        self.refresh_token_ttl = config.get("jwt", "refresh_token_ttl", default=2592000)  # 30 days
        
        # Token storage (in production, use Redis or database)
        self.revoked_tokens: Set[str] = set()
        self.refresh_tokens: Dict[str, RefreshToken] = {}
        
        # Initialize RSA key pair
        self.private_key, self.public_key = self._generate_or_load_keys()
        
        logger.info("JWT Manager initialized with RSA256 algorithm")
    
    def _generate_or_load_keys(self) -> tuple:
        """Generate or load RSA key pair for JWT signing."""
        try:
            # Try to load existing keys from config
            private_key_pem = config.get("jwt", "private_key")
            public_key_pem = config.get("jwt", "public_key")
            
            if private_key_pem and public_key_pem:
                private_key = serialization.load_pem_private_key(
                    private_key_pem.encode(), password=None
                )
                public_key = serialization.load_pem_public_key(
                    public_key_pem.encode()
                )
                logger.info("Loaded existing RSA keys from configuration")
                return private_key, public_key
                
        except Exception as e:
            logger.warning(f"Failed to load existing keys: {e}")
        
        # Generate new RSA key pair
        logger.info("Generating new RSA key pair for JWT signing")
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048
        )
        public_key = private_key.public_key()
        
        # Save keys to config (in production, store securely)
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        logger.warning("Generated new RSA keys - store them securely in production!")
        logger.debug(f"Private key: {private_pem.decode()}")
        logger.debug(f"Public key: {public_pem.decode()}")
        
        return private_key, public_key
    
    def generate_token_pair(self, user_id: str, user_data: Dict[str, Any], 
                           session_id: str, device_id: Optional[str] = None,
                           ip_address: Optional[str] = None, 
                           scopes: List[str] = None) -> Dict[str, Any]:
        """Generate access token and refresh token pair."""
        if scopes is None:
            scopes = ["read", "write"]
        
        current_time = int(time.time())
        jti = secrets.token_urlsafe(32)
        
        # Create JWT claims
        claims = JWTClaims(
            sub=user_id,
            iss=self.issuer,
            aud=self.audience,
            exp=current_time + self.access_token_ttl,
            iat=current_time,
            jti=jti,
            scope=scopes,
            user_data=user_data,
            session_id=session_id,
            device_id=device_id,
            ip_address=ip_address
        )
        
        # Generate access token
        access_token = jwt.encode(
            asdict(claims),
            self.private_key,
            algorithm=self.algorithm
        )
        
        # Generate refresh token
        refresh_token_id = secrets.token_urlsafe(32)
        refresh_token = RefreshToken(
            token_id=refresh_token_id,
            user_id=user_id,
            session_id=session_id,
            device_id=device_id,
            expires_at=time.time() + self.refresh_token_ttl,
            created_at=time.time(),
            last_used_at=time.time()
        )
        
        # Store refresh token
        self.refresh_tokens[refresh_token_id] = refresh_token
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token_id,
            "token_type": "Bearer",
            "expires_in": self.access_token_ttl,
            "scope": " ".join(scopes),
            "jti": jti
        }
    
    def validate_token(self, token: str) -> Optional[JWTClaims]:
        """Validate and decode JWT token."""
        try:
            # Decode token
            payload = jwt.decode(
                token,
                self.public_key,
                algorithms=[self.algorithm],
                audience=self.audience,
                issuer=self.issuer
            )
            
            # Check if token is revoked
            jti = payload.get("jti")
            if jti in self.revoked_tokens:
                logger.warning(f"Attempted use of revoked token: {jti}")
                return None
            
            # Convert to JWTClaims object
            claims = JWTClaims(**payload)
            
            # Additional validation
            if claims.exp < time.time():
                logger.debug("Token has expired")
                return None
            
            return claims
            
        except jwt.ExpiredSignatureError:
            logger.debug("Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {e}")
            return None
        except Exception as e:
            logger.error(f"Token validation error: {e}")
            return None
    
    def refresh_access_token(self, refresh_token_id: str, 
                           device_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Refresh access token using refresh token."""
        refresh_token = self.refresh_tokens.get(refresh_token_id)
        
        if not refresh_token:
            logger.warning(f"Refresh token not found: {refresh_token_id}")
            return None
        
        if refresh_token.is_revoked:
            logger.warning(f"Attempted use of revoked refresh token: {refresh_token_id}")
            return None
        
        if refresh_token.expires_at < time.time():
            logger.warning(f"Refresh token has expired: {refresh_token_id}")
            # Clean up expired token
            del self.refresh_tokens[refresh_token_id]
            return None
        
        # Validate device ID if provided
        if device_id and refresh_token.device_id != device_id:
            logger.warning(f"Device ID mismatch for refresh token: {refresh_token_id}")
            return None
        
        # Update last used time
        refresh_token.last_used_at = time.time()
        
        # Generate new access token with same user data and scopes
        # Note: In a real implementation, you'd fetch current user data from database
        user_data = {"user_id": refresh_token.user_id}  # Simplified
        scopes = ["read", "write"]  # Simplified
        
        return self.generate_token_pair(
            user_id=refresh_token.user_id,
            user_data=user_data,
            session_id=refresh_token.session_id,
            device_id=refresh_token.device_id,
            scopes=scopes
        )
    
    def revoke_token(self, token: str) -> bool:
        """Revoke access token."""
        try:
            # Decode token to get JTI
            payload = jwt.decode(
                token,
                self.public_key,
                algorithms=[self.algorithm],
                options={"verify_exp": False}  # Don't verify expiration for revocation
            )
            
            jti = payload.get("jti")
            if jti:
                self.revoked_tokens.add(jti)
                logger.info(f"Revoked token: {jti}")
                return True
            
        except Exception as e:
            logger.error(f"Failed to revoke token: {e}")
        
        return False
    
    def revoke_refresh_token(self, refresh_token_id: str) -> bool:
        """Revoke refresh token."""
        refresh_token = self.refresh_tokens.get(refresh_token_id)
        
        if refresh_token:
            refresh_token.is_revoked = True
            logger.info(f"Revoked refresh token: {refresh_token_id}")
            return True
        
        return False
    
    def revoke_all_user_tokens(self, user_id: str) -> int:
        """Revoke all tokens for a user."""
        revoked_count = 0
        
        # Revoke all refresh tokens for user
        for token_id, refresh_token in self.refresh_tokens.items():
            if refresh_token.user_id == user_id and not refresh_token.is_revoked:
                refresh_token.is_revoked = True
                revoked_count += 1
        
        logger.info(f"Revoked {revoked_count} tokens for user: {user_id}")
        return revoked_count
    
    def revoke_session_tokens(self, session_id: str) -> int:
        """Revoke all tokens for a session."""
        revoked_count = 0
        
        # Revoke all refresh tokens for session
        for token_id, refresh_token in self.refresh_tokens.items():
            if refresh_token.session_id == session_id and not refresh_token.is_revoked:
                refresh_token.is_revoked = True
                revoked_count += 1
        
        logger.info(f"Revoked {revoked_count} tokens for session: {session_id}")
        return revoked_count
    
    def cleanup_expired_tokens(self) -> int:
        """Clean up expired tokens."""
        current_time = time.time()
        expired_count = 0
        
        # Clean up expired refresh tokens
        expired_tokens = [
            token_id for token_id, refresh_token in self.refresh_tokens.items()
            if refresh_token.expires_at < current_time
        ]
        
        for token_id in expired_tokens:
            del self.refresh_tokens[token_id]
            expired_count += 1
        
        # Clean up old revoked access tokens (keep for 24 hours)
        # This is a simplified implementation - in production, use a proper cleanup strategy
        
        if expired_count > 0:
            logger.info(f"Cleaned up {expired_count} expired tokens")
        
        return expired_count
    
    def get_token_info(self, token: str) -> Optional[Dict[str, Any]]:
        """Get token information without validation."""
        try:
            # Decode without verification to get claims
            payload = jwt.decode(
                token,
                options={"verify_signature": False, "verify_exp": False}
            )
            
            return {
                "jti": payload.get("jti"),
                "sub": payload.get("sub"),
                "iss": payload.get("iss"),
                "aud": payload.get("aud"),
                "exp": payload.get("exp"),
                "iat": payload.get("iat"),
                "scope": payload.get("scope"),
                "session_id": payload.get("session_id"),
                "device_id": payload.get("device_id")
            }
            
        except Exception as e:
            logger.error(f"Failed to get token info: {e}")
            return None
    
    def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """Get active sessions for a user."""
        sessions = []
        
        for token_id, refresh_token in self.refresh_tokens.items():
            if (refresh_token.user_id == user_id and 
                not refresh_token.is_revoked and 
                refresh_token.expires_at > time.time()):
                
                sessions.append({
                    "session_id": refresh_token.session_id,
                    "device_id": refresh_token.device_id,
                    "created_at": refresh_token.created_at,
                    "last_used_at": refresh_token.last_used_at,
                    "expires_at": refresh_token.expires_at
                })
        
        return sessions
    
    def get_stats(self) -> Dict[str, Any]:
        """Get JWT manager statistics."""
        current_time = time.time()
        
        active_refresh_tokens = sum(
            1 for token in self.refresh_tokens.values()
            if not token.is_revoked and token.expires_at > current_time
        )
        
        expired_refresh_tokens = sum(
            1 for token in self.refresh_tokens.values()
            if token.expires_at <= current_time
        )
        
        revoked_refresh_tokens = sum(
            1 for token in self.refresh_tokens.values()
            if token.is_revoked
        )
        
        return {
            "active_refresh_tokens": active_refresh_tokens,
            "expired_refresh_tokens": expired_refresh_tokens,
            "revoked_refresh_tokens": revoked_refresh_tokens,
            "revoked_access_tokens": len(self.revoked_tokens),
            "total_refresh_tokens": len(self.refresh_tokens),
            "access_token_ttl": self.access_token_ttl,
            "refresh_token_ttl": self.refresh_token_ttl
        }

# Global JWT manager instance
jwt_manager = JWTManager()
