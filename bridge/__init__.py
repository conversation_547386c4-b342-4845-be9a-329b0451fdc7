"""
AI Coding Agent Bridge

A bridge between SWE-Agent and IDE plugins.
"""

__version__ = "2.0.0"

# Import core components
from .core import (
    session_manager,
    SessionManager,
    SessionConfig,
    SessionStatus,
    Session,
    enhanced_config,
    EnhancedConfig,
    config,
    Config
)

# Import API components
from .api import *

# Import integration components
from .integrations import (
    SWEAgentInterface,
    vim_integration,
    start_swe_agent_server,
    stop_swe_agent_server
)

# Import utility components
from .utils import (
    get_env,
    check_python_version
)

__all__ = [
    # Core components
    'session_manager',
    'SessionManager',
    'SessionConfig',
    'SessionStatus',
    'Session',
    'enhanced_config',
    'EnhancedConfig',
    'config',
    'Config',

    # Integration components
    'SWEAgentInterface',
    'vim_integration',
    'start_swe_agent_server',
    'stop_swe_agent_server',

    # Utility components
    'get_env',
    'check_python_version'
]
