/**
 * OAuth Integration Tests for VS Code Extension
 * Tests the Phase 4 OAuth integration with the bridge server
 */

import * as assert from 'assert';
import * as vscode from 'vscode';
import { OAuthManager } from '../auth/oauth-manager';
import { AuthenticationUI } from '../auth/auth-ui';
import { BridgeClient } from '../bridge-client';

suite('OAuth Integration Tests', () => {
    let context: vscode.ExtensionContext;
    let outputChannel: vscode.OutputChannel;
    let oauthManager: OAuthManager;
    let authUI: AuthenticationUI;
    let bridgeClient: BridgeClient;

    suiteSetup(async () => {
        // Get extension context
        const extension = vscode.extensions.getExtension('ai-coding-agent.ai-coding-agent');
        if (!extension) {
            throw new Error('Extension not found');
        }
        
        if (!extension.isActive) {
            await extension.activate();
        }
        
        context = extension.exports?.context;
        if (!context) {
            throw new Error('Extension context not available');
        }

        // Create output channel
        outputChannel = vscode.window.createOutputChannel('OAuth Test');

        // Initialize components
        const baseUrl = 'http://localhost:8080';
        oauthManager = new OAuthManager(context, outputChannel, baseUrl);
        authUI = new AuthenticationUI(context, outputChannel, oauthManager);
        bridgeClient = new BridgeClient(outputChannel, () => {});
        bridgeClient.setOAuthManager(oauthManager);
    });

    suiteTeardown(() => {
        if (oauthManager) {
            oauthManager.dispose();
        }
        if (authUI) {
            authUI.dispose();
        }
        if (bridgeClient) {
            bridgeClient.disconnect();
        }
        if (outputChannel) {
            outputChannel.dispose();
        }
    });

    test('OAuth Manager Initialization', () => {
        assert.ok(oauthManager, 'OAuth manager should be initialized');
        assert.strictEqual(oauthManager.isAuthenticated(), false, 'Should not be authenticated initially');
        
        const authState = oauthManager.getAuthState();
        assert.strictEqual(authState.isAuthenticated, false, 'Auth state should be false');
        assert.strictEqual(authState.user, undefined, 'User should be undefined');
        assert.strictEqual(authState.tokens, undefined, 'Tokens should be undefined');
    });

    test('Authentication UI Initialization', () => {
        assert.ok(authUI, 'Authentication UI should be initialized');
        // Test that UI components are properly set up
        // Note: UI testing in VS Code is limited, so we mainly test initialization
    });

    test('Bridge Client OAuth Integration', () => {
        assert.ok(bridgeClient, 'Bridge client should be initialized');
        
        // Test that OAuth manager is properly connected
        const token = oauthManager.getAccessToken();
        assert.strictEqual(token, undefined, 'Should not have access token initially');
    });

    test('OAuth Provider Discovery', async function() {
        this.timeout(10000); // 10 second timeout for network requests
        
        try {
            const providers = await oauthManager.getAvailableProviders();
            assert.ok(Array.isArray(providers), 'Providers should be an array');
            
            // Test provider structure
            providers.forEach(provider => {
                assert.ok(provider.name, 'Provider should have a name');
                assert.ok(provider.display_name, 'Provider should have a display name');
                assert.ok(typeof provider.available === 'boolean', 'Provider should have availability status');
            });
            
            console.log(`Found ${providers.length} OAuth providers:`, providers.map(p => p.name));
        } catch (error) {
            console.warn('OAuth provider discovery failed (bridge server may not be running):', error);
            // Don't fail the test if bridge server is not running
        }
    });

    test('Token Storage and Retrieval', async () => {
        // Test secure token storage
        const mockTokens = {
            access_token: 'test_access_token',
            refresh_token: 'test_refresh_token',
            token_type: 'Bearer',
            expires_in: 3600,
            scope: 'read write',
            expires_at: Date.now() + 3600000
        };

        const mockUser = {
            sub: 'test_user_123',
            username: 'test_user',
            email: '<EMAIL>',
            name: 'Test User',
            email_verified: true
        };

        // Store test data
        await context.secrets.store('oauth_tokens', JSON.stringify(mockTokens));
        await context.secrets.store('oauth_user', JSON.stringify(mockUser));
        await context.secrets.store('oauth_provider', 'github');

        // Retrieve and verify
        const storedTokens = await context.secrets.get('oauth_tokens');
        const storedUser = await context.secrets.get('oauth_user');
        const storedProvider = await context.secrets.get('oauth_provider');

        assert.ok(storedTokens, 'Tokens should be stored');
        assert.ok(storedUser, 'User should be stored');
        assert.strictEqual(storedProvider, 'github', 'Provider should be stored');

        const parsedTokens = JSON.parse(storedTokens!);
        const parsedUser = JSON.parse(storedUser!);

        assert.strictEqual(parsedTokens.access_token, mockTokens.access_token, 'Access token should match');
        assert.strictEqual(parsedUser.username, mockUser.username, 'Username should match');

        // Clean up
        await context.secrets.delete('oauth_tokens');
        await context.secrets.delete('oauth_user');
        await context.secrets.delete('oauth_provider');
    });

    test('Authentication State Management', () => {
        // Test initial state
        let authState = oauthManager.getAuthState();
        assert.strictEqual(authState.isAuthenticated, false, 'Should start unauthenticated');

        // Test state updates (mock authentication)
        // Note: We can't easily test the full OAuth flow in unit tests
        // This would require integration testing with a real OAuth provider
    });

    test('Bridge Client Authentication Headers', async () => {
        // Test that bridge client properly adds authentication headers
        // when OAuth manager has a valid token
        
        // This is tested indirectly through the HTTP interceptor
        // The actual HTTP requests would need integration testing
        assert.ok(true, 'Authentication header integration tested via interceptors');
    });

    test('Error Handling', async () => {
        // Test error handling for invalid tokens, network errors, etc.
        
        try {
            // Test with invalid base URL
            const invalidOAuthManager = new OAuthManager(
                context, 
                outputChannel, 
                'http://invalid-url:9999'
            );
            
            const providers = await invalidOAuthManager.getAvailableProviders();
            assert.strictEqual(providers.length, 0, 'Should return empty array for invalid URL');
            
            invalidOAuthManager.dispose();
        } catch (error) {
            // Expected behavior for network errors
            assert.ok(true, 'Error handling works correctly');
        }
    });

    test('Configuration Integration', () => {
        // Test VS Code configuration integration
        const config = vscode.workspace.getConfiguration('aiCodingAgent');
        
        // Test OAuth-related configuration
        const requireAuth = config.get<boolean>('requireAuthentication');
        const autoRefresh = config.get<boolean>('autoRefreshTokens');
        
        assert.ok(typeof requireAuth === 'boolean', 'requireAuthentication should be boolean');
        assert.ok(typeof autoRefresh === 'boolean', 'autoRefreshTokens should be boolean');
    });

    test('Command Registration', async () => {
        // Test that OAuth commands are properly registered
        const commands = await vscode.commands.getCommands();
        
        const oauthCommands = [
            'ai-coding-agent.login',
            'ai-coding-agent.logout',
            'ai-coding-agent.showAuthMenu',
            'ai-coding-agent.showUserInfo'
        ];

        oauthCommands.forEach(command => {
            assert.ok(commands.includes(command), `Command ${command} should be registered`);
        });
    });

    test('Memory Management', () => {
        // Test that components properly dispose of resources
        const testOAuthManager = new OAuthManager(context, outputChannel, 'http://localhost:8080');
        const testAuthUI = new AuthenticationUI(context, outputChannel, testOAuthManager);
        
        // Test disposal
        assert.doesNotThrow(() => {
            testAuthUI.dispose();
            testOAuthManager.dispose();
        }, 'Components should dispose without errors');
    });
});

suite('OAuth Integration - End-to-End Tests', () => {
    // These tests require a running bridge server with OAuth configured
    // They are marked as integration tests and may be skipped in CI
    
    test('Full OAuth Flow Simulation', async function() {
        this.timeout(30000); // 30 second timeout for full flow
        
        // This test would simulate the complete OAuth flow:
        // 1. Get available providers
        // 2. Initiate OAuth login
        // 3. Handle callback (simulated)
        // 4. Store tokens
        // 5. Make authenticated API calls
        // 6. Refresh tokens
        // 7. Logout
        
        console.log('Full OAuth flow test requires manual testing with real OAuth providers');
        assert.ok(true, 'End-to-end OAuth testing requires integration environment');
    });
});

// Export test utilities for manual testing
export {
    OAuthManager,
    AuthenticationUI
};
