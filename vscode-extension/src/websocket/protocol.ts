/**
 * Enhanced WebSocket Protocol for VS Code Completion Streaming
 * 
 * This protocol optimizes real-time communication between the VS Code extension
 * and the bridge server for intelligent code completion with SWE-Agent tools.
 */

import { CompletionContext, CompletionResult } from '../completion/completion-provider';

/**
 * WebSocket message base interface
 */
export interface BaseMessage {
    type: string;
    id: string;
    timestamp?: number;
}

/**
 * Completion request message
 */
export interface CompletionRequest extends BaseMessage {
    type: 'completion_request';
    context: CompletionContext;
    options: CompletionOptions;
    priority?: 'low' | 'normal' | 'high';
}

/**
 * Completion options for requests
 */
export interface CompletionOptions {
    max_completions?: number;
    use_swe_tools?: boolean;
    include_context?: boolean;
    streaming?: boolean;
    timeout_ms?: number;
    cache_strategy?: 'none' | 'read' | 'write' | 'read_write';
    tools_filter?: string[];
    language_specific?: boolean;
}

/**
 * Completion chunk message (streaming)
 */
export interface CompletionChunk extends BaseMessage {
    type: 'completion_chunk';
    completions: CompletionResult[];
    partial: boolean;
    progress: CompletionProgress;
    stage: CompletionStage;
}

/**
 * Completion progress information
 */
export interface CompletionProgress {
    current: number;
    total: number;
    percentage: number;
    estimated_remaining_ms?: number;
}

/**
 * Completion stages for progress tracking
 */
export enum CompletionStage {
    INITIALIZING = 'initializing',
    CONTEXT_ANALYSIS = 'context_analysis',
    SWE_TOOLS_EXECUTION = 'swe_tools_execution',
    AI_PROCESSING = 'ai_processing',
    RESULT_FORMATTING = 'result_formatting',
    CACHING = 'caching',
    COMPLETE = 'complete'
}

/**
 * Completion complete message
 */
export interface CompletionComplete extends BaseMessage {
    type: 'completion_complete';
    completions: CompletionResult[];
    metadata: CompletionMetadata;
}

/**
 * Completion metadata
 */
export interface CompletionMetadata {
    response_time_ms: number;
    tools_used: string[];
    cache_hit: boolean;
    cache_key?: string;
    context_analyzed: boolean;
    performance_metrics: PerformanceMetrics;
    quality_score?: number;
}

/**
 * Performance metrics for completion
 */
export interface PerformanceMetrics {
    context_analysis_ms: number;
    swe_tools_execution_ms: number;
    ai_processing_ms: number;
    result_formatting_ms: number;
    total_processing_ms: number;
    memory_usage_mb: number;
    cache_operations: {
        reads: number;
        writes: number;
        hits: number;
        misses: number;
    };
}

/**
 * Completion error message
 */
export interface CompletionError extends BaseMessage {
    type: 'completion_error';
    error: ErrorInfo;
}

/**
 * Error information
 */
export interface ErrorInfo {
    code: string;
    message: string;
    details?: any;
    recoverable: boolean;
    retry_after_ms?: number;
    suggestions?: string[];
}

/**
 * Completion cancel message
 */
export interface CompletionCancel extends BaseMessage {
    type: 'completion_cancel';
    reason?: string;
}

/**
 * Connection status message
 */
export interface ConnectionStatus extends BaseMessage {
    type: 'connection_status';
    status: 'connected' | 'disconnected' | 'reconnecting' | 'error';
    server_info?: ServerInfo;
}

/**
 * Server information
 */
export interface ServerInfo {
    version: string;
    swe_tools_available: number;
    swe_tools_loaded: string[];
    performance_mode: 'development' | 'production';
    cache_enabled: boolean;
    max_concurrent_requests: number;
}

/**
 * Health check message
 */
export interface HealthCheck extends BaseMessage {
    type: 'health_check';
    client_info?: ClientInfo;
}

/**
 * Client information
 */
export interface ClientInfo {
    extension_version: string;
    vscode_version: string;
    platform: string;
    workspace_path?: string;
    active_language?: string;
}

/**
 * Health response message
 */
export interface HealthResponse extends BaseMessage {
    type: 'health_response';
    status: 'healthy' | 'degraded' | 'unhealthy';
    server_info: ServerInfo;
    performance_stats: {
        avg_response_time_ms: number;
        cache_hit_rate: number;
        error_rate: number;
        active_connections: number;
    };
}

/**
 * Union type for all WebSocket messages
 */
export type WebSocketMessage = 
    | CompletionRequest
    | CompletionChunk
    | CompletionComplete
    | CompletionError
    | CompletionCancel
    | ConnectionStatus
    | HealthCheck
    | HealthResponse;

/**
 * Message validation utilities
 */
export class ProtocolValidator {
    /**
     * Validate a WebSocket message
     */
    static validateMessage(message: any): { valid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!message || typeof message !== 'object') {
            errors.push('Message must be an object');
            return { valid: false, errors };
        }

        if (!message.type || typeof message.type !== 'string') {
            errors.push('Message must have a valid type');
        }

        if (!message.id || typeof message.id !== 'string') {
            errors.push('Message must have a valid id');
        }

        // Type-specific validation
        switch (message.type) {
            case 'completion_request':
                errors.push(...this.validateCompletionRequest(message));
                break;
            case 'completion_chunk':
                errors.push(...this.validateCompletionChunk(message));
                break;
            case 'completion_complete':
                errors.push(...this.validateCompletionComplete(message));
                break;
            case 'completion_error':
                errors.push(...this.validateCompletionError(message));
                break;
        }

        return { valid: errors.length === 0, errors };
    }

    private static validateCompletionRequest(message: any): string[] {
        const errors: string[] = [];

        if (!message.context || typeof message.context !== 'object') {
            errors.push('Completion request must have a valid context');
        } else {
            if (!message.context.file_path || typeof message.context.file_path !== 'string') {
                errors.push('Context must have a valid file_path');
            }
            if (typeof message.context.cursor_line !== 'number') {
                errors.push('Context must have a valid cursor_line');
            }
            if (typeof message.context.cursor_column !== 'number') {
                errors.push('Context must have a valid cursor_column');
            }
        }

        if (message.options && typeof message.options !== 'object') {
            errors.push('Options must be an object if provided');
        }

        return errors;
    }

    private static validateCompletionChunk(message: any): string[] {
        const errors: string[] = [];

        if (!Array.isArray(message.completions)) {
            errors.push('Completion chunk must have a completions array');
        }

        if (typeof message.partial !== 'boolean') {
            errors.push('Completion chunk must have a partial boolean');
        }

        if (!message.progress || typeof message.progress !== 'object') {
            errors.push('Completion chunk must have progress information');
        }

        return errors;
    }

    private static validateCompletionComplete(message: any): string[] {
        const errors: string[] = [];

        if (!Array.isArray(message.completions)) {
            errors.push('Completion complete must have a completions array');
        }

        if (!message.metadata || typeof message.metadata !== 'object') {
            errors.push('Completion complete must have metadata');
        }

        return errors;
    }

    private static validateCompletionError(message: any): string[] {
        const errors: string[] = [];

        if (!message.error || typeof message.error !== 'object') {
            errors.push('Completion error must have error information');
        } else {
            if (!message.error.code || typeof message.error.code !== 'string') {
                errors.push('Error must have a valid code');
            }
            if (!message.error.message || typeof message.error.message !== 'string') {
                errors.push('Error must have a valid message');
            }
        }

        return errors;
    }
}

/**
 * Protocol utilities for message handling
 */
export class ProtocolUtils {
    /**
     * Create a completion request message
     */
    static createCompletionRequest(
        id: string,
        context: CompletionContext,
        options: CompletionOptions = {}
    ): CompletionRequest {
        return {
            type: 'completion_request',
            id,
            context,
            options: {
                max_completions: 5,
                use_swe_tools: true,
                include_context: true,
                streaming: true,
                timeout_ms: 30000,
                cache_strategy: 'read_write',
                language_specific: true,
                ...options
            },
            timestamp: Date.now()
        };
    }

    /**
     * Create a completion cancel message
     */
    static createCompletionCancel(id: string, reason?: string): CompletionCancel {
        return {
            type: 'completion_cancel',
            id,
            reason,
            timestamp: Date.now()
        };
    }

    /**
     * Create a health check message
     */
    static createHealthCheck(id: string, clientInfo?: ClientInfo): HealthCheck {
        return {
            type: 'health_check',
            id,
            client_info: clientInfo,
            timestamp: Date.now()
        };
    }

    /**
     * Parse WebSocket message safely
     */
    static parseMessage(data: string): { message: WebSocketMessage | null; error: string | null } {
        try {
            const parsed = JSON.parse(data);
            const validation = ProtocolValidator.validateMessage(parsed);
            
            if (!validation.valid) {
                return {
                    message: null,
                    error: `Invalid message: ${validation.errors.join(', ')}`
                };
            }

            return { message: parsed as WebSocketMessage, error: null };
        } catch (error) {
            return {
                message: null,
                error: `Failed to parse message: ${error}`
            };
        }
    }

    /**
     * Serialize message for transmission
     */
    static serializeMessage(message: WebSocketMessage): string {
        return JSON.stringify({
            ...message,
            timestamp: message.timestamp || Date.now()
        });
    }

    /**
     * Generate unique message ID
     */
    static generateMessageId(): string {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Calculate message priority score
     */
    static calculatePriority(context: CompletionContext): 'low' | 'normal' | 'high' {
        // Simple priority calculation based on context
        const fileExtension = context.filePath.split('.').pop()?.toLowerCase();
        const isMainFile = context.filePath.includes('main') || context.filePath.includes('index');
        const hasRecentActivity = Date.now() - (context as any).lastModified < 60000; // 1 minute

        if (isMainFile && hasRecentActivity) {
            return 'high';
        } else if (fileExtension === 'py' || fileExtension === 'ts' || fileExtension === 'js') {
            return 'normal';
        } else {
            return 'low';
        }
    }
}
