import * as vscode from 'vscode';
import { CompletionContext, CompletionResult } from '../completion/completion-provider';

/**
 * WebSocket message types for completion protocol
 */
export enum CompletionMessageType {
    REQUEST = 'completion_request',
    CHUNK = 'completion_chunk',
    COMPLETE = 'completion_complete',
    ERROR = 'completion_error',
    CANCEL = 'completion_cancel'
}

/**
 * Interface for completion request message
 */
export interface CompletionRequestMessage {
    type: CompletionMessageType.REQUEST;
    id: string;
    context: CompletionContext;
    options: {
        max_completions?: number;
        use_swe_tools?: boolean;
        include_context?: boolean;
        streaming?: boolean;
    };
}

/**
 * Interface for completion chunk message
 */
export interface CompletionChunkMessage {
    type: CompletionMessageType.CHUNK;
    id: string;
    completions: CompletionResult[];
    partial: boolean;
    progress?: {
        current: number;
        total: number;
        stage: string;
    };
}

/**
 * Interface for completion complete message
 */
export interface CompletionCompleteMessage {
    type: CompletionMessageType.COMPLETE;
    id: string;
    completions: CompletionResult[];
    metadata: {
        response_time_ms: number;
        tools_used: string[];
        cache_hit: boolean;
    };
}

/**
 * Interface for completion error message
 */
export interface CompletionErrorMessage {
    type: CompletionMessageType.ERROR;
    id: string;
    error: string;
    details?: any;
}

/**
 * WebSocket client for real-time completion streaming
 */
export class CompletionWebSocketClient {
    private ws: WebSocket | null = null;
    private url: string;
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private reconnectDelay = 1000;
    private isConnecting = false;
    private pendingRequests = new Map<string, {
        resolve: (completions: CompletionResult[]) => void;
        reject: (error: Error) => void;
        timeout: NodeJS.Timeout;
    }>();
    private outputChannel: vscode.OutputChannel;
    private connectionPromise: Promise<void> | null = null;

    constructor(host: string, port: number, outputChannel: vscode.OutputChannel) {
        this.url = `ws://${host}:${port}/ws/completion`;
        this.outputChannel = outputChannel;
    }

    /**
     * Connect to the WebSocket server
     */
    async connect(): Promise<void> {
        if (this.ws?.readyState === WebSocket.OPEN) {
            return Promise.resolve();
        }

        if (this.connectionPromise) {
            return this.connectionPromise;
        }

        this.connectionPromise = this._connect();
        return this.connectionPromise;
    }

    private async _connect(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (this.isConnecting) {
                reject(new Error('Already connecting'));
                return;
            }

            this.isConnecting = true;
            this.outputChannel.appendLine(`Connecting to completion WebSocket: ${this.url}`);

            try {
                this.ws = new WebSocket(this.url);

                this.ws.onopen = () => {
                    this.outputChannel.appendLine('Completion WebSocket connected');
                    this.isConnecting = false;
                    this.reconnectAttempts = 0;
                    this.connectionPromise = null;
                    resolve();
                };

                this.ws.onmessage = (event) => {
                    this.handleMessage(event.data);
                };

                this.ws.onclose = (event) => {
                    this.outputChannel.appendLine(`Completion WebSocket closed: ${event.code} ${event.reason}`);
                    this.isConnecting = false;
                    this.connectionPromise = null;
                    this.handleDisconnection();
                };

                this.ws.onerror = (error) => {
                    this.outputChannel.appendLine(`Completion WebSocket error: ${error}`);
                    this.isConnecting = false;
                    this.connectionPromise = null;
                    reject(new Error(`WebSocket connection failed: ${error}`));
                };

                // Connection timeout
                setTimeout(() => {
                    if (this.isConnecting) {
                        this.isConnecting = false;
                        this.connectionPromise = null;
                        reject(new Error('WebSocket connection timeout'));
                    }
                }, 10000);

            } catch (error) {
                this.isConnecting = false;
                this.connectionPromise = null;
                reject(error);
            }
        });
    }

    /**
     * Disconnect from the WebSocket server
     */
    disconnect(): void {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.connectionPromise = null;
        
        // Reject all pending requests
        for (const [id, request] of this.pendingRequests) {
            clearTimeout(request.timeout);
            request.reject(new Error('WebSocket disconnected'));
        }
        this.pendingRequests.clear();
    }

    /**
     * Request completions via WebSocket with streaming support
     */
    async requestCompletions(
        context: CompletionContext,
        options: {
            max_completions?: number;
            use_swe_tools?: boolean;
            include_context?: boolean;
            streaming?: boolean;
            timeout?: number;
        } = {}
    ): Promise<CompletionResult[]> {
        await this.connect();

        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            throw new Error('WebSocket not connected');
        }

        const requestId = this.generateRequestId();
        const timeout = options.timeout || 30000;

        const message: CompletionRequestMessage = {
            type: CompletionMessageType.REQUEST,
            id: requestId,
            context,
            options: {
                max_completions: options.max_completions || 5,
                use_swe_tools: options.use_swe_tools !== false,
                include_context: options.include_context !== false,
                streaming: options.streaming !== false
            }
        };

        return new Promise((resolve, reject) => {
            // Set up timeout
            const timeoutHandle = setTimeout(() => {
                this.pendingRequests.delete(requestId);
                reject(new Error(`Completion request timeout after ${timeout}ms`));
            }, timeout);

            // Store request
            this.pendingRequests.set(requestId, {
                resolve,
                reject,
                timeout: timeoutHandle
            });

            // Send request
            try {
                this.ws!.send(JSON.stringify(message));
                this.outputChannel.appendLine(`Sent completion request: ${requestId}`);
            } catch (error) {
                clearTimeout(timeoutHandle);
                this.pendingRequests.delete(requestId);
                reject(error);
            }
        });
    }

    /**
     * Cancel a completion request
     */
    cancelRequest(requestId: string): void {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            const cancelMessage = {
                type: CompletionMessageType.CANCEL,
                id: requestId
            };
            this.ws.send(JSON.stringify(cancelMessage));
        }

        const request = this.pendingRequests.get(requestId);
        if (request) {
            clearTimeout(request.timeout);
            request.reject(new Error('Request cancelled'));
            this.pendingRequests.delete(requestId);
        }
    }

    /**
     * Handle incoming WebSocket messages
     */
    private handleMessage(data: string): void {
        try {
            const message = JSON.parse(data);
            
            switch (message.type) {
                case CompletionMessageType.CHUNK:
                    this.handleCompletionChunk(message as CompletionChunkMessage);
                    break;
                
                case CompletionMessageType.COMPLETE:
                    this.handleCompletionComplete(message as CompletionCompleteMessage);
                    break;
                
                case CompletionMessageType.ERROR:
                    this.handleCompletionError(message as CompletionErrorMessage);
                    break;
                
                default:
                    this.outputChannel.appendLine(`Unknown message type: ${message.type}`);
            }
        } catch (error) {
            this.outputChannel.appendLine(`Error parsing WebSocket message: ${error}`);
        }
    }

    /**
     * Handle completion chunk (streaming)
     */
    private handleCompletionChunk(message: CompletionChunkMessage): void {
        // For now, we'll accumulate chunks and resolve when complete
        // In a more advanced implementation, we could provide partial results
        this.outputChannel.appendLine(`Received completion chunk: ${message.id} (${message.completions.length} items)`);
        
        if (message.progress) {
            this.outputChannel.appendLine(`Progress: ${message.progress.current}/${message.progress.total} - ${message.progress.stage}`);
        }
    }

    /**
     * Handle completion complete
     */
    private handleCompletionComplete(message: CompletionCompleteMessage): void {
        const request = this.pendingRequests.get(message.id);
        if (request) {
            clearTimeout(request.timeout);
            this.pendingRequests.delete(message.id);
            
            this.outputChannel.appendLine(
                `Completion complete: ${message.id} (${message.completions.length} items, ` +
                `${message.metadata.response_time_ms}ms, tools: ${message.metadata.tools_used.join(', ')})`
            );
            
            request.resolve(message.completions);
        }
    }

    /**
     * Handle completion error
     */
    private handleCompletionError(message: CompletionErrorMessage): void {
        const request = this.pendingRequests.get(message.id);
        if (request) {
            clearTimeout(request.timeout);
            this.pendingRequests.delete(message.id);
            request.reject(new Error(message.error));
        }
    }

    /**
     * Handle WebSocket disconnection
     */
    private handleDisconnection(): void {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
            
            this.outputChannel.appendLine(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                this.connect().catch(error => {
                    this.outputChannel.appendLine(`Reconnection failed: ${error.message}`);
                });
            }, delay);
        } else {
            this.outputChannel.appendLine('Max reconnection attempts reached');
        }
    }

    /**
     * Generate unique request ID
     */
    private generateRequestId(): string {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Check if WebSocket is connected
     */
    isConnected(): boolean {
        return this.ws?.readyState === WebSocket.OPEN;
    }

    /**
     * Get connection status
     */
    getStatus(): {
        connected: boolean;
        reconnectAttempts: number;
        pendingRequests: number;
    } {
        return {
            connected: this.isConnected(),
            reconnectAttempts: this.reconnectAttempts,
            pendingRequests: this.pendingRequests.size
        };
    }
}
