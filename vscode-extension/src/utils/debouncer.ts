/**
 * Debouncer for managing rapid completion requests
 */
export class Debouncer {
    private timeout: NodeJS.Timeout | null = null;
    private delay: number;
    private pendingPromises: Array<{
        resolve: (value: any) => void;
        reject: (error: any) => void;
    }> = [];

    constructor(delay: number = 150) {
        this.delay = delay;
    }

    /**
     * Debounce a function call
     */
    async debounce<T>(fn: () => Promise<T>): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            // Clear existing timeout
            if (this.timeout) {
                clearTimeout(this.timeout);
            }

            // Add this promise to pending list
            this.pendingPromises.push({ resolve, reject });

            // Set new timeout
            this.timeout = setTimeout(async () => {
                const promises = [...this.pendingPromises];
                this.pendingPromises = [];
                this.timeout = null;

                try {
                    const result = await fn();
                    // Resolve all pending promises with the same result
                    promises.forEach(p => p.resolve(result));
                } catch (error) {
                    // Reject all pending promises with the same error
                    promises.forEach(p => p.reject(error));
                }
            }, this.delay);
        });
    }

    /**
     * Cancel any pending debounced calls
     */
    cancel(): void {
        if (this.timeout) {
            clearTimeout(this.timeout);
            this.timeout = null;
        }

        // Reject all pending promises
        const promises = [...this.pendingPromises];
        this.pendingPromises = [];
        promises.forEach(p => p.reject(new Error('Debounced call cancelled')));
    }

    /**
     * Check if there are pending calls
     */
    isPending(): boolean {
        return this.timeout !== null;
    }

    /**
     * Get the number of pending promises
     */
    getPendingCount(): number {
        return this.pendingPromises.length;
    }

    /**
     * Set new delay
     */
    setDelay(delay: number): void {
        this.delay = delay;
    }

    /**
     * Get current delay
     */
    getDelay(): number {
        return this.delay;
    }

    /**
     * Dispose of the debouncer
     */
    dispose(): void {
        this.cancel();
    }
}
