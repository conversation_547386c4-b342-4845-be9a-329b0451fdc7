/**
 * Performance monitoring for completion provider
 */
export class PerformanceMonitor {
    private completionTimes: number[] = [];
    private cacheHitTimes: number[] = [];
    private errorCount = 0;
    private totalRequests = 0;
    private cacheHits = 0;
    private maxHistorySize = 1000;

    /**
     * Record a completion request
     */
    recordCompletion(responseTime: number, completionCount: number): void {
        this.completionTimes.push(responseTime);
        this.totalRequests++;

        // Keep only recent history
        if (this.completionTimes.length > this.maxHistorySize) {
            this.completionTimes.shift();
        }
    }

    /**
     * Record a cache hit
     */
    recordCacheHit(responseTime: number): void {
        this.cacheHitTimes.push(responseTime);
        this.cacheHits++;

        // Keep only recent history
        if (this.cacheHitTimes.length > this.maxHistorySize) {
            this.cacheHitTimes.shift();
        }
    }

    /**
     * Record an error
     */
    recordError(): void {
        this.errorCount++;
    }

    /**
     * Get performance statistics
     */
    getStats(): {
        averageResponseTime: number;
        medianResponseTime: number;
        p95ResponseTime: number;
        cacheHitRate: number;
        errorRate: number;
        totalRequests: number;
        slowRequestCount: number;
        averageCacheHitTime: number;
    } {
        const stats = {
            averageResponseTime: 0,
            medianResponseTime: 0,
            p95ResponseTime: 0,
            cacheHitRate: 0,
            errorRate: 0,
            totalRequests: this.totalRequests,
            slowRequestCount: 0,
            averageCacheHitTime: 0
        };

        if (this.completionTimes.length > 0) {
            // Calculate average response time
            stats.averageResponseTime = this.completionTimes.reduce((a, b) => a + b, 0) / this.completionTimes.length;

            // Calculate median response time
            const sorted = [...this.completionTimes].sort((a, b) => a - b);
            const mid = Math.floor(sorted.length / 2);
            stats.medianResponseTime = sorted.length % 2 === 0
                ? (sorted[mid - 1] + sorted[mid]) / 2
                : sorted[mid];

            // Calculate 95th percentile
            const p95Index = Math.floor(sorted.length * 0.95);
            stats.p95ResponseTime = sorted[p95Index] || 0;

            // Count slow requests (>200ms)
            stats.slowRequestCount = this.completionTimes.filter(time => time > 200).length;
        }

        if (this.cacheHitTimes.length > 0) {
            stats.averageCacheHitTime = this.cacheHitTimes.reduce((a, b) => a + b, 0) / this.cacheHitTimes.length;
        }

        if (this.totalRequests > 0) {
            stats.cacheHitRate = this.cacheHits / this.totalRequests;
            stats.errorRate = this.errorCount / this.totalRequests;
        }

        return stats;
    }

    /**
     * Get recent performance trend
     */
    getRecentTrend(windowSize: number = 100): {
        recentAverageTime: number;
        trend: 'improving' | 'degrading' | 'stable';
        changePercent: number;
    } {
        if (this.completionTimes.length < windowSize * 2) {
            return {
                recentAverageTime: 0,
                trend: 'stable',
                changePercent: 0
            };
        }

        const recent = this.completionTimes.slice(-windowSize);
        const previous = this.completionTimes.slice(-windowSize * 2, -windowSize);

        const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
        const previousAvg = previous.reduce((a, b) => a + b, 0) / previous.length;

        const changePercent = ((recentAvg - previousAvg) / previousAvg) * 100;

        let trend: 'improving' | 'degrading' | 'stable' = 'stable';
        if (Math.abs(changePercent) > 5) {
            trend = changePercent > 0 ? 'degrading' : 'improving';
        }

        return {
            recentAverageTime: recentAvg,
            trend,
            changePercent
        };
    }

    /**
     * Check if performance is within acceptable thresholds
     */
    isPerformanceAcceptable(): {
        acceptable: boolean;
        issues: string[];
    } {
        const stats = this.getStats();
        const issues: string[] = [];

        // Check average response time
        if (stats.averageResponseTime > 200) {
            issues.push(`Average response time too high: ${stats.averageResponseTime.toFixed(1)}ms`);
        }

        // Check 95th percentile
        if (stats.p95ResponseTime > 500) {
            issues.push(`95th percentile response time too high: ${stats.p95ResponseTime.toFixed(1)}ms`);
        }

        // Check error rate
        if (stats.errorRate > 0.05) {
            issues.push(`Error rate too high: ${(stats.errorRate * 100).toFixed(1)}%`);
        }

        // Check cache hit rate
        if (stats.cacheHitRate < 0.3) {
            issues.push(`Cache hit rate too low: ${(stats.cacheHitRate * 100).toFixed(1)}%`);
        }

        return {
            acceptable: issues.length === 0,
            issues
        };
    }

    /**
     * Get performance report
     */
    getReport(): string {
        const stats = this.getStats();
        const trend = this.getRecentTrend();
        const health = this.isPerformanceAcceptable();

        return `
Performance Report:
==================
Total Requests: ${stats.totalRequests}
Average Response Time: ${stats.averageResponseTime.toFixed(1)}ms
Median Response Time: ${stats.medianResponseTime.toFixed(1)}ms
95th Percentile: ${stats.p95ResponseTime.toFixed(1)}ms
Cache Hit Rate: ${(stats.cacheHitRate * 100).toFixed(1)}%
Error Rate: ${(stats.errorRate * 100).toFixed(1)}%
Slow Requests (>200ms): ${stats.slowRequestCount}
Average Cache Hit Time: ${stats.averageCacheHitTime.toFixed(1)}ms

Recent Trend: ${trend.trend} (${trend.changePercent > 0 ? '+' : ''}${trend.changePercent.toFixed(1)}%)
Recent Average: ${trend.recentAverageTime.toFixed(1)}ms

Health Status: ${health.acceptable ? 'GOOD' : 'ISSUES DETECTED'}
${health.issues.length > 0 ? 'Issues:\n' + health.issues.map(issue => `- ${issue}`).join('\n') : ''}
        `.trim();
    }

    /**
     * Reset all statistics
     */
    reset(): void {
        this.completionTimes = [];
        this.cacheHitTimes = [];
        this.errorCount = 0;
        this.totalRequests = 0;
        this.cacheHits = 0;
    }

    /**
     * Export performance data
     */
    export(): any {
        return {
            completionTimes: this.completionTimes,
            cacheHitTimes: this.cacheHitTimes,
            errorCount: this.errorCount,
            totalRequests: this.totalRequests,
            cacheHits: this.cacheHits,
            timestamp: Date.now()
        };
    }

    /**
     * Import performance data
     */
    import(data: any): void {
        if (data.completionTimes) this.completionTimes = data.completionTimes;
        if (data.cacheHitTimes) this.cacheHitTimes = data.cacheHitTimes;
        if (data.errorCount) this.errorCount = data.errorCount;
        if (data.totalRequests) this.totalRequests = data.totalRequests;
        if (data.cacheHits) this.cacheHits = data.cacheHits;
    }
}
