import * as vscode from 'vscode';
import { BridgeClient } from './bridge-client';
import { ChatView } from './chat-view';
import { AICompletionProvider } from './completion/completion-provider';
import { OAuthManager } from './auth/oauth-manager';
import { AuthenticationUI } from './auth/auth-ui';
import { StatusBarManager } from './ui/status-bar-manager';
import { ContextMenuProvider } from './ui/context-menu-provider';
import { SettingsManager } from './ui/settings-manager';
import { VisualFeedbackManager } from './ui/visual-feedback';

// Status bar item to show the current status of the AI Coding Agent
let statusBarItem: vscode.StatusBarItem;

// Output channel for logging
let outputChannel: vscode.OutputChannel;

// Bridge client for communicating with the AI Coding Agent bridge
let bridgeClient: BridgeClient;

// Chat view for interactive communication
let chatView: ChatView;

// AI completion provider
let completionProvider: AICompletionProvider;

// OAuth authentication manager
let oauthManager: OAuthManager;

// Authentication UI
let authUI: AuthenticationUI;

// Enhanced UI managers
let statusBarManager: StatusBarManager;
let contextMenuProvider: ContextMenuProvider;
let settingsManager: SettingsManager;
let visualFeedback: VisualFeedbackManager;

/**
 * Activate the extension
 */
export function activate(context: vscode.ExtensionContext) {
    // Create output channel
    outputChannel = vscode.window.createOutputChannel('AI Coding Agent');
    outputChannel.appendLine('AI Coding Agent extension is now active');
    context.subscriptions.push(outputChannel);

    // Create status bar item
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = '$(rocket) AI Agent';
    statusBarItem.tooltip = 'AI Coding Agent';
    statusBarItem.command = 'ai-coding-agent.checkStatus';
    statusBarItem.show();
    context.subscriptions.push(statusBarItem);
    
    // Initialize bridge client
    bridgeClient = new BridgeClient(outputChannel, updateStatusBar);

    // Initialize OAuth manager
    const oauthConfig = vscode.workspace.getConfiguration('aiCodingAgent');
    const host = oauthConfig.get<string>('bridgeHost') || 'localhost';
    const port = oauthConfig.get<number>('bridgePort') || 8080;
    const baseUrl = `http://${host}:${port}`;

    oauthManager = new OAuthManager(context, outputChannel, baseUrl, (authState) => {
        // Update status bar when auth state changes
        if (statusBarManager) {
            statusBarManager.updateAuthStatus(authState);
        }
        updateAuthStatusBar(authState.isAuthenticated, authState.user?.name);

        // Show success message on first authentication
        if (authState.isAuthenticated && authUI) {
            authUI.showAuthenticationSuccess();
        }
    });

    // Initialize authentication UI
    authUI = new AuthenticationUI(context, outputChannel, oauthManager);

    // Initialize enhanced UI managers
    statusBarManager = new StatusBarManager(context);
    visualFeedback = new VisualFeedbackManager(context, outputChannel);
    settingsManager = new SettingsManager(context, bridgeClient, statusBarManager, outputChannel);
    contextMenuProvider = new ContextMenuProvider(context, bridgeClient, statusBarManager, outputChannel);

    // Connect OAuth manager to bridge client
    bridgeClient.setOAuthManager(oauthManager);

    // Update status bar manager with initial auth state
    statusBarManager.updateAuthStatus(oauthManager.getAuthState());
    statusBarManager.updateConnectionStatus({ status: 'disconnected' });
    statusBarManager.updateModelStatus(oauthConfig.get<string>('modelName') || 'claude-sonnet-4-20250514');

    // Initialize chat view
    chatView = new ChatView(context, bridgeClient);

    // Initialize completion provider
    completionProvider = new AICompletionProvider(bridgeClient, outputChannel);

    // Register completion provider for supported languages
    const supportedLanguages = ['python', 'javascript', 'typescript', 'java', 'cpp', 'c'];
    for (const language of supportedLanguages) {
        const provider = vscode.languages.registerCompletionItemProvider(
            { scheme: 'file', language },
            completionProvider,
            '.', // Trigger on dot
            '(', // Trigger on opening parenthesis
            ' '  // Trigger on space
        );
        context.subscriptions.push(provider);
    }

    // Register commands
    const runAgentCommand = vscode.commands.registerCommand('ai-coding-agent.runAgent', runAgent);
    const stopAgentCommand = vscode.commands.registerCommand('ai-coding-agent.stopAgent', stopAgent);
    const checkStatusCommand = vscode.commands.registerCommand('ai-coding-agent.checkStatus', checkStatus);
    const openChatCommand = vscode.commands.registerCommand('ai-coding-agent.openChat', openChat);

    // Register new UI/UX commands
    const showAIActionsCommand = vscode.commands.registerCommand('ai-coding-agent.showAIActions', () => {
        contextMenuProvider.showAIActionsMenu();
    });

    context.subscriptions.push(runAgentCommand);
    context.subscriptions.push(stopAgentCommand);
    context.subscriptions.push(checkStatusCommand);
    context.subscriptions.push(openChatCommand);
    context.subscriptions.push(showAIActionsCommand);

    // Connect to the bridge if auto-connect is enabled
    const connectConfig = vscode.workspace.getConfiguration('aiCodingAgent');
    const autoConnect = connectConfig.get<boolean>('autoConnect', true);

    if (autoConnect) {
        bridgeClient.connect().catch((error) => {
            outputChannel.appendLine(`Failed to connect to bridge: ${error.message}`);
            updateStatusBar('disconnected');
        });
    } else {
        updateStatusBar('disconnected');
    }

    // Listen for configuration changes
    const configChangeListener = vscode.workspace.onDidChangeConfiguration((event) => {
        if (event.affectsConfiguration('aiCodingAgent')) {
            outputChannel.appendLine('AI Coding Agent configuration changed');
            // Optionally reconnect if connection settings changed
            if (event.affectsConfiguration('aiCodingAgent.bridgeHost') ||
                event.affectsConfiguration('aiCodingAgent.bridgePort')) {
                outputChannel.appendLine('Bridge connection settings changed, reconnecting...');
                bridgeClient.disconnect();
                setTimeout(() => {
                    bridgeClient.connect().catch((error) => {
                        outputChannel.appendLine(`Failed to reconnect to bridge: ${error.message}`);
                        updateStatusBar('disconnected');
                    });
                }, 1000);
            }
        }
    });

    context.subscriptions.push(configChangeListener);
}

/**
 * Deactivate the extension
 */
export function deactivate() {
    if (bridgeClient) {
        bridgeClient.disconnect();
    }
    if (completionProvider) {
        completionProvider.dispose();
    }
    if (oauthManager) {
        oauthManager.dispose();
    }
    if (authUI) {
        authUI.dispose();
    }
    if (statusBarManager) {
        statusBarManager.dispose();
    }
    if (visualFeedback) {
        visualFeedback.dispose();
    }
    outputChannel.appendLine('AI Coding Agent extension deactivated');
}

/**
 * Update the status bar item based on the current status
 */
function updateStatusBar(status: 'connected' | 'disconnected' | 'running' | 'error') {
    // Update legacy status bar item
    switch (status) {
        case 'connected':
            statusBarItem.text = '$(rocket) AI Agent: Ready';
            statusBarItem.backgroundColor = undefined;
            break;
        case 'disconnected':
            statusBarItem.text = '$(circle-slash) AI Agent: Disconnected';
            statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
            break;
        case 'running':
            statusBarItem.text = '$(sync~spin) AI Agent: Running';
            statusBarItem.backgroundColor = undefined;
            break;
        case 'error':
            statusBarItem.text = '$(error) AI Agent: Error';
            statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
            break;
    }

    // Update new status bar manager
    if (statusBarManager) {
        const connectionStatus = {
            status: status === 'running' ? 'connected' : status,
            message: status === 'running' ? 'AI Agent is processing...' : undefined,
            lastUpdate: new Date()
        } as const;

        statusBarManager.updateConnectionStatus(connectionStatus);

        if (status === 'running') {
            statusBarManager.updateActivityStatus({
                isActive: true,
                activity: 'tool_execution',
                message: 'AI Agent is running...'
            });
        } else {
            statusBarManager.updateActivityStatus({ isActive: false, activity: 'idle' });
        }
    }
}

/**
 * Update authentication status in the main status bar
 */
function updateAuthStatusBar(isAuthenticated: boolean, userName?: string) {
    if (isAuthenticated && userName) {
        // Update tooltip to include authentication status
        statusBarItem.tooltip = `AI Coding Agent - Authenticated as ${userName}\nClick to check status`;
    } else {
        // Update tooltip to show unauthenticated status
        statusBarItem.tooltip = 'AI Coding Agent - Not authenticated\nClick to check status';
    }
}

/**
 * Run the AI Coding Agent
 */
async function runAgent() {
    // Check authentication first
    if (!oauthManager.isAuthenticated()) {
        authUI.showAuthenticationRequired('run the AI Coding Agent');
        return;
    }

    // Get the current workspace folder
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) {
        vscode.window.showErrorMessage('No workspace folder is open. Please open a folder first.');
        return;
    }

    // Get the repository path
    const repoPath = workspaceFolders[0].uri.fsPath;

    // Get the problem statement from the user
    const problemStatement = await vscode.window.showInputBox({
        prompt: 'Enter a task description for the AI Coding Agent',
        placeHolder: 'e.g., Create a function to calculate the Fibonacci sequence'
    });

    if (!problemStatement) {
        return; // User cancelled
    }

    // Get configuration
    const config = vscode.workspace.getConfiguration('aiCodingAgent');
    const modelName = config.get<string>('modelName') || 'claude-3-opus-20240229';

    // Update status
    outputChannel.show();

    try {
        const response = await bridgeClient.runAgent(problemStatement, repoPath, modelName);

        if (response.status === 'success') {
            vscode.window.showInformationMessage('AI Coding Agent started successfully');
        } else {
            vscode.window.showErrorMessage(`Failed to start AI Coding Agent: ${response.error || 'Unknown error'}`);
        }
    } catch (error: any) {
        vscode.window.showErrorMessage(`Error running AI Coding Agent: ${error.message}`);
    }
}

/**
 * Stop the AI Coding Agent
 */
async function stopAgent() {
    try {
        const response = await bridgeClient.stopAgent();

        if (response.status === 'success') {
            vscode.window.showInformationMessage('AI Coding Agent stopped successfully');
        } else {
            vscode.window.showErrorMessage(`Failed to stop AI Coding Agent: ${response.error || 'Unknown error'}`);
        }
    } catch (error: any) {
        vscode.window.showErrorMessage(`Error stopping AI Coding Agent: ${error.message}`);
    }
}

/**
 * Check the status of the AI Coding Agent bridge
 */
async function checkStatus() {
    try {
        const response = await bridgeClient.checkStatus();

        if (response.status === 'ok') {
            vscode.window.showInformationMessage('AI Coding Agent bridge is running');
            updateStatusBar('connected');
        } else {
            vscode.window.showWarningMessage('AI Coding Agent bridge is not responding correctly');
            updateStatusBar('error');
        }
    } catch (error: any) {
        vscode.window.showErrorMessage(`Cannot connect to AI Coding Agent bridge: ${error.message}`);
        updateStatusBar('disconnected');
    }
}

/**
 * Open the chat interface
 */
function openChat() {
    chatView.show();
}
