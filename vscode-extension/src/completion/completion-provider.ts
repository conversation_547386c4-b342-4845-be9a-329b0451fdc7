import * as vscode from 'vscode';
import { BridgeClient } from '../bridge-client';
import { ContextAnalyzer } from '../context/context-analyzer';
import { CompletionCache } from './completion-cache';
import { SWEToolIntegration } from './swe-tool-integration';
import { Deboun<PERSON> } from '../utils/debouncer';
import { PerformanceMonitor } from '../utils/performance-monitor';
import { CompletionWebSocketClient } from '../websocket/completion-client';

/**
 * Interface for completion context
 */
export interface CompletionContext {
    filePath: string;
    content: string;
    cursorLine: number;
    cursorColumn: number;
    language: string;
    workspacePath?: string;
    surroundingLines?: string[];
    imports?: string[];
    symbols?: any[];
}

/**
 * Interface for completion result
 */
export interface CompletionResult {
    text: string;
    kind: vscode.CompletionItemKind;
    detail?: string;
    documentation?: string;
    insertText?: string | vscode.SnippetString;
    range?: vscode.Range;
    sortText?: string;
    filterText?: string;
    additionalTextEdits?: vscode.TextEdit[];
}

/**
 * AI-powered completion provider using SWE-Agent tools
 */
export class AICompletionProvider implements vscode.CompletionItemProvider {
    private bridgeClient: BridgeClient;
    private contextAnalyzer: ContextAnalyzer;
    private completionCache: CompletionCache;
    private sweToolIntegration: SWEToolIntegration;
    private debouncer: Debouncer;
    private performanceMonitor: PerformanceMonitor;
    private outputChannel: vscode.OutputChannel;
    private wsClient: CompletionWebSocketClient;
    private useWebSocket: boolean = true;

    constructor(
        bridgeClient: BridgeClient,
        outputChannel: vscode.OutputChannel
    ) {
        this.bridgeClient = bridgeClient;
        this.outputChannel = outputChannel;
        this.contextAnalyzer = new ContextAnalyzer();
        this.completionCache = new CompletionCache();
        this.sweToolIntegration = new SWEToolIntegration(bridgeClient);
        this.debouncer = new Debouncer(150); // 150ms debounce
        this.performanceMonitor = new PerformanceMonitor();
        this.wsClient = new CompletionWebSocketClient('localhost', 8080, outputChannel);

        // Initialize WebSocket connection
        this.initializeWebSocket();
    }

    /**
     * Provide completion items for the given position
     */
    async provideCompletionItems(
        document: vscode.TextDocument,
        position: vscode.Position,
        token: vscode.CancellationToken,
        context: vscode.CompletionContext
    ): Promise<vscode.CompletionItem[] | vscode.CompletionList | null> {
        
        const startTime = Date.now();
        
        try {
            // Check if we should provide completions
            if (!this.shouldProvideCompletions(document, position, context)) {
                return null;
            }

            // Extract completion context
            const completionContext = await this.extractCompletionContext(document, position);
            
            // Check cache first
            const cacheKey = this.generateCacheKey(completionContext);
            const cachedResult = this.completionCache.get(cacheKey);
            if (cachedResult && !token.isCancellationRequested) {
                this.performanceMonitor.recordCacheHit(Date.now() - startTime);
                return this.formatCompletionItems(cachedResult);
            }

            // Debounce rapid requests
            const debouncedRequest = await this.debouncer.debounce(async () => {
                return this.getCompletions(completionContext, token);
            });

            if (token.isCancellationRequested) {
                return null;
            }

            const completions = await debouncedRequest;
            
            // Cache the results
            if (completions && completions.length > 0) {
                this.completionCache.set(cacheKey, completions);
            }

            // Record performance metrics
            const responseTime = Date.now() - startTime;
            this.performanceMonitor.recordCompletion(responseTime, completions?.length || 0);
            
            if (responseTime > 200) {
                this.outputChannel.appendLine(`Slow completion: ${responseTime}ms for ${completionContext.filePath}`);
            }

            return this.formatCompletionItems(completions || []);

        } catch (error: any) {
            this.outputChannel.appendLine(`Completion error: ${error.message}`);
            this.performanceMonitor.recordError();
            return null;
        }
    }

    /**
     * Determine if we should provide completions for this context
     */
    private shouldProvideCompletions(
        document: vscode.TextDocument,
        position: vscode.Position,
        context: vscode.CompletionContext
    ): boolean {
        // Skip if not a supported language
        const supportedLanguages = ['python', 'javascript', 'typescript', 'java', 'cpp', 'c'];
        if (!supportedLanguages.includes(document.languageId)) {
            return false;
        }

        // Skip if in comments or strings (basic check)
        const lineText = document.lineAt(position.line).text;
        const beforeCursor = lineText.substring(0, position.character);
        
        // Skip if in string literals
        if (this.isInStringLiteral(beforeCursor)) {
            return false;
        }

        // Skip if in comments
        if (this.isInComment(beforeCursor, document.languageId)) {
            return false;
        }

        return true;
    }

    /**
     * Extract completion context from document and position
     */
    private async extractCompletionContext(
        document: vscode.TextDocument,
        position: vscode.Position
    ): Promise<CompletionContext> {
        const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri);
        const workspacePath = workspaceFolder?.uri.fsPath;

        // Get surrounding context
        const surroundingLines = this.getSurroundingLines(document, position, 5);
        
        // Analyze context using context analyzer
        const contextInfo = await this.contextAnalyzer.analyzeContext(
            document.uri.fsPath,
            document.getText(),
            position.line,
            position.character,
            workspacePath
        );

        return {
            filePath: document.uri.fsPath,
            content: document.getText(),
            cursorLine: position.line,
            cursorColumn: position.character,
            language: document.languageId,
            workspacePath,
            surroundingLines,
            imports: contextInfo.imports,
            symbols: contextInfo.symbols
        };
    }

    /**
     * Initialize WebSocket connection
     */
    private async initializeWebSocket(): Promise<void> {
        try {
            await this.wsClient.connect();
            this.outputChannel.appendLine('WebSocket completion client connected');
        } catch (error: any) {
            this.outputChannel.appendLine(`WebSocket connection failed: ${error.message}`);
            this.useWebSocket = false;
        }
    }

    /**
     * Get completions using SWE-Agent tools and AI analysis
     */
    private async getCompletions(
        context: CompletionContext,
        token: vscode.CancellationToken
    ): Promise<CompletionResult[]> {

        // Try WebSocket first for better performance
        if (this.useWebSocket && this.wsClient.isConnected()) {
            try {
                const wsCompletions = await this.getWebSocketCompletions(context, token);
                if (wsCompletions.length > 0) {
                    return wsCompletions;
                }
            } catch (error: any) {
                this.outputChannel.appendLine(`WebSocket completion failed: ${error.message}`);
                // Fall back to HTTP API
            }
        }

        // Fallback to HTTP API
        return this.getHTTPCompletions(context, token);
    }

    /**
     * Get completions via WebSocket
     */
    private async getWebSocketCompletions(
        context: CompletionContext,
        token: vscode.CancellationToken
    ): Promise<CompletionResult[]> {

        const completions = await this.wsClient.requestCompletions(context, {
            max_completions: 5,
            use_swe_tools: true,
            include_context: true,
            streaming: true,
            timeout: 5000 // 5 second timeout for better responsiveness
        });

        return completions;
    }

    /**
     * Get completions via HTTP API (fallback)
     */
    private async getHTTPCompletions(
        context: CompletionContext,
        token: vscode.CancellationToken
    ): Promise<CompletionResult[]> {

        // Use SWE-Agent tools for intelligent analysis
        const sweAnalysis = await this.sweToolIntegration.analyzeForCompletions(context);

        if (token.isCancellationRequested) {
            return [];
        }

        // Get AI-powered completions from bridge
        const aiCompletions = await this.getAICompletions(context);

        if (token.isCancellationRequested) {
            return [];
        }

        // Combine and rank completions
        const allCompletions = [
            ...sweAnalysis.completions,
            ...aiCompletions
        ];

        // Remove duplicates and rank by relevance
        return this.rankAndDeduplicateCompletions(allCompletions, context);
    }

    /**
     * Get AI-powered completions from the bridge API
     */
    private async getAICompletions(context: CompletionContext): Promise<CompletionResult[]> {
        try {
            // Call the enhanced completion API
            const response = await this.bridgeClient.request('POST', '/api/completion/intelligent', {
                context: {
                    file_path: context.filePath,
                    content: context.content,
                    cursor_line: context.cursorLine,
                    cursor_column: context.cursorColumn,
                    language: context.language,
                    workspace_path: context.workspacePath
                },
                options: {
                    max_completions: 5,
                    use_swe_tools: true,
                    include_context: true
                }
            });

            if (response.status === 'success' && response.completions) {
                return response.completions.map((comp: any) => ({
                    text: comp.text,
                    kind: this.mapCompletionKind(comp.kind),
                    detail: comp.detail,
                    documentation: comp.documentation,
                    insertText: comp.insertText,
                    sortText: comp.sortText
                }));
            }

            return [];
        } catch (error: any) {
            this.outputChannel.appendLine(`AI completion error: ${error.message}`);
            return [];
        }
    }

    /**
     * Format completion results as VS Code completion items
     */
    private formatCompletionItems(completions: CompletionResult[]): vscode.CompletionItem[] {
        return completions.map(comp => {
            const item = new vscode.CompletionItem(comp.text, comp.kind);
            
            if (comp.detail) item.detail = comp.detail;
            if (comp.documentation) item.documentation = new vscode.MarkdownString(comp.documentation);
            if (comp.insertText) item.insertText = comp.insertText;
            if (comp.range) item.range = comp.range;
            if (comp.sortText) item.sortText = comp.sortText;
            if (comp.filterText) item.filterText = comp.filterText;
            if (comp.additionalTextEdits) item.additionalTextEdits = comp.additionalTextEdits;

            return item;
        });
    }

    /**
     * Generate cache key for completion context
     */
    private generateCacheKey(context: CompletionContext): string {
        const contentHash = this.simpleHash(context.content);
        return `${context.filePath}:${context.cursorLine}:${context.cursorColumn}:${contentHash}`;
    }

    /**
     * Get surrounding lines for context
     */
    private getSurroundingLines(
        document: vscode.TextDocument,
        position: vscode.Position,
        lineCount: number
    ): string[] {
        const lines: string[] = [];
        const startLine = Math.max(0, position.line - lineCount);
        const endLine = Math.min(document.lineCount - 1, position.line + lineCount);

        for (let i = startLine; i <= endLine; i++) {
            lines.push(document.lineAt(i).text);
        }

        return lines;
    }

    /**
     * Check if position is in string literal
     */
    private isInStringLiteral(text: string): boolean {
        const singleQuotes = (text.match(/'/g) || []).length;
        const doubleQuotes = (text.match(/"/g) || []).length;
        return (singleQuotes % 2 === 1) || (doubleQuotes % 2 === 1);
    }

    /**
     * Check if position is in comment
     */
    private isInComment(text: string, language: string): boolean {
        if (language === 'python') {
            return text.includes('#');
        } else if (['javascript', 'typescript', 'java', 'cpp', 'c'].includes(language)) {
            return text.includes('//') || text.includes('/*');
        }
        return false;
    }

    /**
     * Map completion kind from API to VS Code kind
     */
    private mapCompletionKind(kind: string): vscode.CompletionItemKind {
        switch (kind) {
            case 'method': return vscode.CompletionItemKind.Method;
            case 'function': return vscode.CompletionItemKind.Function;
            case 'variable': return vscode.CompletionItemKind.Variable;
            case 'class': return vscode.CompletionItemKind.Class;
            case 'module': return vscode.CompletionItemKind.Module;
            case 'property': return vscode.CompletionItemKind.Property;
            case 'keyword': return vscode.CompletionItemKind.Keyword;
            case 'snippet': return vscode.CompletionItemKind.Snippet;
            default: return vscode.CompletionItemKind.Text;
        }
    }

    /**
     * Rank and deduplicate completions
     */
    private rankAndDeduplicateCompletions(
        completions: CompletionResult[],
        context: CompletionContext
    ): CompletionResult[] {
        // Remove duplicates based on text
        const seen = new Set<string>();
        const unique = completions.filter(comp => {
            if (seen.has(comp.text)) {
                return false;
            }
            seen.add(comp.text);
            return true;
        });

        // Sort by relevance (this could be enhanced with ML ranking)
        return unique.sort((a, b) => {
            // Prioritize by kind
            const kindPriority = {
                [vscode.CompletionItemKind.Method]: 1,
                [vscode.CompletionItemKind.Function]: 2,
                [vscode.CompletionItemKind.Variable]: 3,
                [vscode.CompletionItemKind.Property]: 4,
                [vscode.CompletionItemKind.Class]: 5,
                [vscode.CompletionItemKind.Module]: 6,
                [vscode.CompletionItemKind.Keyword]: 7,
                [vscode.CompletionItemKind.Text]: 8
            };

            const aPriority = kindPriority[a.kind as keyof typeof kindPriority] || 9;
            const bPriority = kindPriority[b.kind as keyof typeof kindPriority] || 9;

            if (aPriority !== bPriority) {
                return aPriority - bPriority;
            }

            // Then by text length (shorter first)
            return a.text.length - b.text.length;
        }).slice(0, 10); // Limit to top 10 completions
    }

    /**
     * Simple hash function for content
     */
    private simpleHash(str: string): string {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    /**
     * Dispose of resources
     */
    dispose() {
        this.completionCache.clear();
        this.debouncer.dispose();
        this.wsClient.disconnect();
    }
}
