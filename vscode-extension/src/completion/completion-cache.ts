import { CompletionResult } from './completion-provider';

/**
 * Cache entry interface
 */
interface CacheEntry {
    completions: CompletionResult[];
    timestamp: number;
    accessCount: number;
    lastAccessed: number;
}

/**
 * LRU Cache for completion results with intelligent invalidation
 */
export class CompletionCache {
    private cache = new Map<string, CacheEntry>();
    private maxSize: number;
    private maxAge: number; // in milliseconds
    private hitCount = 0;
    private missCount = 0;

    constructor(maxSize: number = 1000, maxAge: number = 5 * 60 * 1000) { // 5 minutes default
        this.maxSize = maxSize;
        this.maxAge = maxAge;
    }

    /**
     * Get completions from cache
     */
    get(key: string): CompletionResult[] | null {
        const entry = this.cache.get(key);
        
        if (!entry) {
            this.missCount++;
            return null;
        }

        // Check if entry is expired
        if (Date.now() - entry.timestamp > this.maxAge) {
            this.cache.delete(key);
            this.missCount++;
            return null;
        }

        // Update access statistics
        entry.accessCount++;
        entry.lastAccessed = Date.now();
        this.hitCount++;

        return entry.completions;
    }

    /**
     * Set completions in cache
     */
    set(key: string, completions: CompletionResult[]): void {
        // Don't cache empty results
        if (!completions || completions.length === 0) {
            return;
        }

        const now = Date.now();
        const entry: CacheEntry = {
            completions,
            timestamp: now,
            accessCount: 1,
            lastAccessed: now
        };

        // If cache is full, remove least recently used entries
        if (this.cache.size >= this.maxSize) {
            this.evictLRU();
        }

        this.cache.set(key, entry);
    }

    /**
     * Check if key exists in cache
     */
    has(key: string): boolean {
        const entry = this.cache.get(key);
        if (!entry) {
            return false;
        }

        // Check if expired
        if (Date.now() - entry.timestamp > this.maxAge) {
            this.cache.delete(key);
            return false;
        }

        return true;
    }

    /**
     * Remove entry from cache
     */
    delete(key: string): boolean {
        return this.cache.delete(key);
    }

    /**
     * Clear all cache entries
     */
    clear(): void {
        this.cache.clear();
        this.hitCount = 0;
        this.missCount = 0;
    }

    /**
     * Invalidate cache entries for a specific file
     */
    invalidateFile(filePath: string): void {
        const keysToDelete: string[] = [];
        
        for (const key of this.cache.keys()) {
            if (key.startsWith(filePath + ':')) {
                keysToDelete.push(key);
            }
        }

        for (const key of keysToDelete) {
            this.cache.delete(key);
        }
    }

    /**
     * Invalidate cache entries older than specified age
     */
    invalidateOld(maxAge?: number): void {
        const cutoff = Date.now() - (maxAge || this.maxAge);
        const keysToDelete: string[] = [];

        for (const [key, entry] of this.cache.entries()) {
            if (entry.timestamp < cutoff) {
                keysToDelete.push(key);
            }
        }

        for (const key of keysToDelete) {
            this.cache.delete(key);
        }
    }

    /**
     * Evict least recently used entries
     */
    private evictLRU(): void {
        let oldestKey: string | null = null;
        let oldestTime = Date.now();

        for (const [key, entry] of this.cache.entries()) {
            if (entry.lastAccessed < oldestTime) {
                oldestTime = entry.lastAccessed;
                oldestKey = key;
            }
        }

        if (oldestKey) {
            this.cache.delete(oldestKey);
        }
    }

    /**
     * Get cache statistics
     */
    getStats(): {
        size: number;
        maxSize: number;
        hitCount: number;
        missCount: number;
        hitRate: number;
        memoryUsage: number;
    } {
        const totalRequests = this.hitCount + this.missCount;
        const hitRate = totalRequests > 0 ? this.hitCount / totalRequests : 0;
        
        // Estimate memory usage (rough calculation)
        let memoryUsage = 0;
        for (const [key, entry] of this.cache.entries()) {
            memoryUsage += key.length * 2; // UTF-16 characters
            memoryUsage += entry.completions.length * 200; // Rough estimate per completion
        }

        return {
            size: this.cache.size,
            maxSize: this.maxSize,
            hitCount: this.hitCount,
            missCount: this.missCount,
            hitRate,
            memoryUsage
        };
    }

    /**
     * Optimize cache by removing less useful entries
     */
    optimize(): void {
        // Remove entries with low access count and old timestamp
        const now = Date.now();
        const keysToDelete: string[] = [];

        for (const [key, entry] of this.cache.entries()) {
            const age = now - entry.timestamp;
            const accessRate = entry.accessCount / (age / 1000 / 60); // accesses per minute

            // Remove entries that are old and rarely accessed
            if (age > this.maxAge / 2 && accessRate < 0.1) {
                keysToDelete.push(key);
            }
        }

        for (const key of keysToDelete) {
            this.cache.delete(key);
        }
    }

    /**
     * Preload cache with common completions
     */
    preload(commonCompletions: { [key: string]: CompletionResult[] }): void {
        for (const [key, completions] of Object.entries(commonCompletions)) {
            this.set(key, completions);
        }
    }

    /**
     * Export cache data for persistence
     */
    export(): any {
        const data: any = {};
        
        for (const [key, entry] of this.cache.entries()) {
            // Only export recent and frequently accessed entries
            if (entry.accessCount > 1 && (Date.now() - entry.timestamp) < this.maxAge / 2) {
                data[key] = {
                    completions: entry.completions,
                    timestamp: entry.timestamp,
                    accessCount: entry.accessCount
                };
            }
        }

        return data;
    }

    /**
     * Import cache data from persistence
     */
    import(data: any): void {
        const now = Date.now();
        
        for (const [key, entryData] of Object.entries(data)) {
            const entry = entryData as any;
            
            // Only import recent entries
            if (now - entry.timestamp < this.maxAge) {
                this.cache.set(key, {
                    completions: entry.completions,
                    timestamp: entry.timestamp,
                    accessCount: entry.accessCount,
                    lastAccessed: now
                });
            }
        }
    }
}
