import { BridgeClient } from '../bridge-client';
import { CompletionContext, CompletionResult } from './completion-provider';
import * as vscode from 'vscode';

/**
 * Interface for SWE tool analysis result
 */
export interface SWEAnalysisResult {
    completions: CompletionResult[];
    context: {
        fileStructure?: any;
        searchResults?: any[];
        codeAnalysis?: any;
    };
    toolsUsed: string[];
}

/**
 * Integration with SWE-Agent tools for intelligent code completion
 */
export class SWEToolIntegration {
    private bridgeClient: BridgeClient;
    private toolCache = new Map<string, any>();

    constructor(bridgeClient: BridgeClient) {
        this.bridgeClient = bridgeClient;
    }

    /**
     * Analyze context using SWE-Agent tools to provide intelligent completions
     */
    async analyzeForCompletions(context: CompletionContext): Promise<SWEAnalysisResult> {
        const toolsUsed: string[] = [];
        const completions: CompletionResult[] = [];
        const analysisContext: any = {};

        try {
            // Use filemap tool for project structure understanding
            if (context.language === 'python') {
                const fileStructure = await this.useFilemapTool(context.filePath);
                if (fileStructure) {
                    analysisContext.fileStructure = fileStructure;
                    toolsUsed.push('filemap');
                    
                    // Extract completions from file structure
                    completions.push(...this.extractCompletionsFromFileStructure(fileStructure, context));
                }
            }

            // Use search_file tool for finding relevant patterns
            const searchResults = await this.useSearchFileTool(context);
            if (searchResults && searchResults.length > 0) {
                analysisContext.searchResults = searchResults;
                toolsUsed.push('search_file');
                
                // Extract completions from search results
                completions.push(...this.extractCompletionsFromSearchResults(searchResults, context));
            }

            // Use str_replace_editor for code analysis
            const codeAnalysis = await this.useStrReplaceEditorTool(context);
            if (codeAnalysis) {
                analysisContext.codeAnalysis = codeAnalysis;
                toolsUsed.push('str_replace_editor');
                
                // Extract completions from code analysis
                completions.push(...this.extractCompletionsFromCodeAnalysis(codeAnalysis, context));
            }

            // Use find_file tool for discovering related files
            if (context.workspacePath) {
                const relatedFiles = await this.useFindFileTool(context);
                if (relatedFiles && relatedFiles.length > 0) {
                    toolsUsed.push('find_file');
                    
                    // Extract completions from related files
                    completions.push(...this.extractCompletionsFromRelatedFiles(relatedFiles, context));
                }
            }

        } catch (error: any) {
            console.error('SWE tool integration error:', error);
        }

        return {
            completions: this.rankCompletions(completions, context),
            context: analysisContext,
            toolsUsed
        };
    }

    /**
     * Use filemap tool to get Python file structure
     */
    private async useFilemapTool(filePath: string): Promise<any> {
        try {
            const response = await this.bridgeClient.request('POST', '/api/swe-tools/execute', {
                tool_name: 'filemap',
                parameters: {
                    file_path: filePath
                },
                working_directory: require('path').dirname(filePath)
            });

            if (response.status === 'success' && response.result?.success) {
                return this.parseFilemapOutput(response.result.output);
            }
        } catch (error) {
            console.error('Filemap tool error:', error);
        }
        return null;
    }

    /**
     * Use search_file tool to find relevant code patterns
     */
    private async useSearchFileTool(context: CompletionContext): Promise<any[]> {
        try {
            // Extract potential search terms from cursor context
            const searchTerms = this.extractSearchTerms(context);
            const results: any[] = [];

            for (const term of searchTerms.slice(0, 3)) { // Limit to 3 searches
                const response = await this.bridgeClient.request('POST', '/api/swe-tools/execute', {
                    tool_name: 'search_file',
                    parameters: {
                        search_term: term,
                        file: context.filePath
                    },
                    working_directory: require('path').dirname(context.filePath)
                });

                if (response.status === 'success' && response.result?.success) {
                    results.push({
                        term,
                        output: response.result.output
                    });
                }
            }

            return results;
        } catch (error) {
            console.error('Search file tool error:', error);
            return [];
        }
    }

    /**
     * Use str_replace_editor tool for code analysis
     */
    private async useStrReplaceEditorTool(context: CompletionContext): Promise<any> {
        try {
            // Get a view of the current file around the cursor
            const startLine = Math.max(0, context.cursorLine - 10);
            const endLine = context.cursorLine + 10;

            const response = await this.bridgeClient.request('POST', '/api/swe-tools/execute', {
                tool_name: 'str_replace_editor',
                parameters: {
                    command: 'view',
                    path: context.filePath,
                    view_range: [startLine + 1, endLine + 1] // SWE tools use 1-based indexing
                },
                working_directory: require('path').dirname(context.filePath)
            });

            if (response.status === 'success' && response.result?.success) {
                return {
                    viewOutput: response.result.output,
                    startLine,
                    endLine
                };
            }
        } catch (error) {
            console.error('Str replace editor tool error:', error);
        }
        return null;
    }

    /**
     * Use find_file tool to discover related files
     */
    private async useFindFileTool(context: CompletionContext): Promise<string[]> {
        try {
            // Find files with similar names or in the same directory
            const baseName = require('path').basename(context.filePath, require('path').extname(context.filePath));
            const patterns = [
                `*${baseName}*`,
                `*test*${baseName}*`,
                `*${baseName}*test*`
            ];

            const results: string[] = [];

            for (const pattern of patterns) {
                const response = await this.bridgeClient.request('POST', '/api/swe-tools/execute', {
                    tool_name: 'find_file',
                    parameters: {
                        file_name: pattern,
                        dir: context.workspacePath
                    },
                    working_directory: context.workspacePath
                });

                if (response.status === 'success' && response.result?.success) {
                    const files = this.parseFileListOutput(response.result.output);
                    results.push(...files);
                }
            }

            return [...new Set(results)]; // Remove duplicates
        } catch (error) {
            console.error('Find file tool error:', error);
            return [];
        }
    }

    /**
     * Extract search terms from completion context
     */
    private extractSearchTerms(context: CompletionContext): string[] {
        const terms: string[] = [];
        
        // Get the current line and extract potential search terms
        const lines = context.content.split('\n');
        const currentLine = lines[context.cursorLine] || '';
        const beforeCursor = currentLine.substring(0, context.cursorColumn);
        
        // Extract word before cursor
        const wordMatch = beforeCursor.match(/(\w+)$/);
        if (wordMatch) {
            terms.push(wordMatch[1]);
        }

        // Extract function/method calls
        const callMatch = beforeCursor.match(/(\w+)\s*\($/);
        if (callMatch) {
            terms.push(callMatch[1]);
        }

        // Extract class/module references
        const dotMatch = beforeCursor.match(/(\w+)\.$/);
        if (dotMatch) {
            terms.push(dotMatch[1]);
        }

        // Add imports as search terms
        if (context.imports) {
            for (const imp of context.imports) {
                const importMatch = imp.match(/import\s+(\w+)|from\s+(\w+)/);
                if (importMatch) {
                    terms.push(importMatch[1] || importMatch[2]);
                }
            }
        }

        return [...new Set(terms)].filter(term => term.length > 2); // Remove duplicates and short terms
    }

    /**
     * Parse filemap output to extract structure information
     */
    private parseFilemapOutput(output: string): any {
        const structure = {
            classes: [] as string[],
            functions: [] as string[],
            imports: [] as string[]
        };

        const lines = output.split('\n');
        for (const line of lines) {
            if (line.includes('class ')) {
                const classMatch = line.match(/class\s+(\w+)/);
                if (classMatch) {
                    structure.classes.push(classMatch[1]);
                }
            } else if (line.includes('def ')) {
                const funcMatch = line.match(/def\s+(\w+)/);
                if (funcMatch) {
                    structure.functions.push(funcMatch[1]);
                }
            } else if (line.includes('import ') || line.includes('from ')) {
                structure.imports.push(line.trim());
            }
        }

        return structure;
    }

    /**
     * Parse file list output
     */
    private parseFileListOutput(output: string): string[] {
        return output.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0 && !line.startsWith('Found') && !line.startsWith('No files'));
    }

    /**
     * Extract completions from file structure
     */
    private extractCompletionsFromFileStructure(structure: any, context: CompletionContext): CompletionResult[] {
        const completions: CompletionResult[] = [];

        // Add class completions
        for (const className of structure.classes || []) {
            completions.push({
                text: className,
                kind: vscode.CompletionItemKind.Class,
                detail: 'Class from file structure',
                documentation: `Class ${className} found in current file`
            });
        }

        // Add function completions
        for (const funcName of structure.functions || []) {
            completions.push({
                text: funcName,
                kind: vscode.CompletionItemKind.Function,
                detail: 'Function from file structure',
                documentation: `Function ${funcName} found in current file`
            });
        }

        return completions;
    }

    /**
     * Extract completions from search results
     */
    private extractCompletionsFromSearchResults(results: any[], context: CompletionContext): CompletionResult[] {
        const completions: CompletionResult[] = [];

        for (const result of results) {
            // Parse search output to find relevant code patterns
            const lines = result.output.split('\n');
            for (const line of lines) {
                // Look for function definitions
                const funcMatch = line.match(/def\s+(\w+)/);
                if (funcMatch) {
                    completions.push({
                        text: funcMatch[1],
                        kind: vscode.CompletionItemKind.Function,
                        detail: `Function found by searching for "${result.term}"`,
                        documentation: `Function ${funcMatch[1]} found in search results`
                    });
                }

                // Look for variable assignments
                const varMatch = line.match(/(\w+)\s*=/);
                if (varMatch && !line.includes('def ')) {
                    completions.push({
                        text: varMatch[1],
                        kind: vscode.CompletionItemKind.Variable,
                        detail: `Variable found by searching for "${result.term}"`,
                        documentation: `Variable ${varMatch[1]} found in search results`
                    });
                }
            }
        }

        return completions;
    }

    /**
     * Extract completions from code analysis
     */
    private extractCompletionsFromCodeAnalysis(analysis: any, context: CompletionContext): CompletionResult[] {
        const completions: CompletionResult[] = [];

        if (analysis.viewOutput) {
            const lines = analysis.viewOutput.split('\n');
            for (const line of lines) {
                // Extract identifiers from the surrounding code
                const identifiers = line.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];
                for (const identifier of identifiers) {
                    if (identifier.length > 2 && !['def', 'class', 'if', 'for', 'while'].includes(identifier)) {
                        completions.push({
                            text: identifier,
                            kind: vscode.CompletionItemKind.Text,
                            detail: 'Identifier from surrounding code',
                            documentation: `Identifier ${identifier} found in nearby code`
                        });
                    }
                }
            }
        }

        return completions;
    }

    /**
     * Extract completions from related files
     */
    private extractCompletionsFromRelatedFiles(files: string[], context: CompletionContext): CompletionResult[] {
        const completions: CompletionResult[] = [];

        for (const file of files.slice(0, 5)) { // Limit to 5 files
            const fileName = require('path').basename(file, require('path').extname(file));
            completions.push({
                text: fileName,
                kind: vscode.CompletionItemKind.Module,
                detail: 'Related file',
                documentation: `Related file: ${file}`
            });
        }

        return completions;
    }

    /**
     * Rank completions by relevance
     */
    private rankCompletions(completions: CompletionResult[], context: CompletionContext): CompletionResult[] {
        // Remove duplicates
        const seen = new Set<string>();
        const unique = completions.filter(comp => {
            if (seen.has(comp.text)) {
                return false;
            }
            seen.add(comp.text);
            return true;
        });

        // Sort by relevance
        return unique.sort((a, b) => {
            // Prioritize by kind
            const kindPriority = {
                [vscode.CompletionItemKind.Function]: 1,
                [vscode.CompletionItemKind.Method]: 2,
                [vscode.CompletionItemKind.Class]: 3,
                [vscode.CompletionItemKind.Variable]: 4,
                [vscode.CompletionItemKind.Module]: 5,
                [vscode.CompletionItemKind.Text]: 6
            };

            const aPriority = kindPriority[a.kind as keyof typeof kindPriority] || 7;
            const bPriority = kindPriority[b.kind as keyof typeof kindPriority] || 7;

            if (aPriority !== bPriority) {
                return aPriority - bPriority;
            }

            // Then by text length
            return a.text.length - b.text.length;
        });
    }
}
