/**
 * Settings Manager for AI Coding Agent
 * Provides comprehensive configuration interface with real-time updates
 */

import * as vscode from 'vscode';
import { BridgeClient } from '../bridge-client';
import { StatusBarManager } from './status-bar-manager';

export interface AISettings {
    // Bridge connection
    bridgeHost: string;
    bridgePort: number;
    bridgeProtocol: 'http' | 'https';
    
    // Authentication
    requireAuthentication: boolean;
    autoRefreshTokens: boolean;
    
    // AI Model settings
    modelName: string;
    temperature: number;
    maxTokens: number;
    
    // Completion settings
    enableInlineCompletions: boolean;
    completionDelay: number;
    maxCompletions: number;
    autoAcceptSingleCompletion: boolean;
    
    // Chat settings
    enableChat: boolean;
    chatHistorySize: number;
    preserveChatHistory: boolean;
    
    // UI settings
    showStatusBar: boolean;
    showActivityIndicator: boolean;
    showSessionStats: boolean;
    enableContextMenus: boolean;
    
    // Performance settings
    enableCaching: boolean;
    cacheSize: number;
    requestTimeout: number;
    
    // Advanced settings
    enableDebugLogging: boolean;
    enableTelemetry: boolean;
    customPrompts: { [key: string]: string };
}

export class SettingsManager {
    private context: vscode.ExtensionContext;
    private bridgeClient: BridgeClient;
    private statusBarManager: StatusBarManager;
    private outputChannel: vscode.OutputChannel;
    
    private currentSettings: AISettings;
    private settingsPanel: vscode.WebviewPanel | undefined;

    constructor(
        context: vscode.ExtensionContext,
        bridgeClient: BridgeClient,
        statusBarManager: StatusBarManager,
        outputChannel: vscode.OutputChannel
    ) {
        this.context = context;
        this.bridgeClient = bridgeClient;
        this.statusBarManager = statusBarManager;
        this.outputChannel = outputChannel;
        
        this.currentSettings = this.loadSettings();
        this.registerCommands();
        this.watchConfigurationChanges();
    }

    /**
     * Register settings-related commands
     */
    private registerCommands(): void {
        const commands = [
            {
                command: 'ai-coding-agent.openSettings',
                callback: () => this.openSettingsUI()
            },
            {
                command: 'ai-coding-agent.resetSettings',
                callback: () => this.resetSettings()
            },
            {
                command: 'ai-coding-agent.exportSettings',
                callback: () => this.exportSettings()
            },
            {
                command: 'ai-coding-agent.importSettings',
                callback: () => this.importSettings()
            },
            {
                command: 'ai-coding-agent.selectModel',
                callback: () => this.showModelSelection()
            },
            {
                command: 'ai-coding-agent.showStats',
                callback: () => this.showSessionStats()
            }
        ];

        commands.forEach(({ command, callback }) => {
            const disposable = vscode.commands.registerCommand(command, callback);
            this.context.subscriptions.push(disposable);
        });
    }

    /**
     * Load settings from VS Code configuration
     */
    private loadSettings(): AISettings {
        const config = vscode.workspace.getConfiguration('aiCodingAgent');
        
        return {
            // Bridge connection
            bridgeHost: config.get('bridgeHost', 'localhost'),
            bridgePort: config.get('bridgePort', 8080),
            bridgeProtocol: config.get('bridgeProtocol', 'http'),
            
            // Authentication
            requireAuthentication: config.get('requireAuthentication', true),
            autoRefreshTokens: config.get('autoRefreshTokens', true),
            
            // AI Model settings
            modelName: config.get('modelName', 'claude-sonnet-4-20250514'),
            temperature: config.get('temperature', 0.7),
            maxTokens: config.get('maxTokens', 4000),
            
            // Completion settings
            enableInlineCompletions: config.get('enableInlineCompletions', true),
            completionDelay: config.get('completionDelay', 500),
            maxCompletions: config.get('maxCompletions', 3),
            autoAcceptSingleCompletion: config.get('autoAcceptSingleCompletion', false),
            
            // Chat settings
            enableChat: config.get('enableChat', true),
            chatHistorySize: config.get('chatHistorySize', 50),
            preserveChatHistory: config.get('preserveChatHistory', true),
            
            // UI settings
            showStatusBar: config.get('showStatusBar', true),
            showActivityIndicator: config.get('showActivityIndicator', true),
            showSessionStats: config.get('showSessionStats', true),
            enableContextMenus: config.get('enableContextMenus', true),
            
            // Performance settings
            enableCaching: config.get('enableCaching', true),
            cacheSize: config.get('cacheSize', 100),
            requestTimeout: config.get('requestTimeout', 30000),
            
            // Advanced settings
            enableDebugLogging: config.get('enableDebugLogging', false),
            enableTelemetry: config.get('enableTelemetry', true),
            customPrompts: config.get('customPrompts', {})
        };
    }

    /**
     * Watch for configuration changes
     */
    private watchConfigurationChanges(): void {
        vscode.workspace.onDidChangeConfiguration(event => {
            if (event.affectsConfiguration('aiCodingAgent')) {
                const newSettings = this.loadSettings();
                this.handleSettingsChange(this.currentSettings, newSettings);
                this.currentSettings = newSettings;
            }
        });
    }

    /**
     * Handle settings changes
     */
    private handleSettingsChange(oldSettings: AISettings, newSettings: AISettings): void {
        // Update bridge client if connection settings changed
        if (oldSettings.bridgeHost !== newSettings.bridgeHost ||
            oldSettings.bridgePort !== newSettings.bridgePort ||
            oldSettings.bridgeProtocol !== newSettings.bridgeProtocol) {
            this.bridgeClient.updateBaseUrl(`${newSettings.bridgeProtocol}://${newSettings.bridgeHost}:${newSettings.bridgePort}`);
        }

        // Update model if changed
        if (oldSettings.modelName !== newSettings.modelName) {
            this.statusBarManager.updateModelStatus(newSettings.modelName);
        }

        // Update status bar visibility
        if (oldSettings.showStatusBar !== newSettings.showStatusBar) {
            if (newSettings.showStatusBar) {
                this.statusBarManager.showAll();
            } else {
                this.statusBarManager.hideAll();
            }
        }

        // Log settings change
        if (newSettings.enableDebugLogging) {
            this.outputChannel.appendLine(`Settings updated: ${JSON.stringify(newSettings, null, 2)}`);
        }
    }

    /**
     * Open settings UI
     */
    public async openSettingsUI(): Promise<void> {
        if (this.settingsPanel) {
            this.settingsPanel.reveal();
            return;
        }

        this.settingsPanel = vscode.window.createWebviewPanel(
            'aiSettings',
            'AI Coding Agent Settings',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        this.settingsPanel.webview.html = this.getSettingsHtml();
        
        // Handle messages from webview
        this.settingsPanel.webview.onDidReceiveMessage(
            message => this.handleWebviewMessage(message),
            undefined,
            this.context.subscriptions
        );

        this.settingsPanel.onDidDispose(() => {
            this.settingsPanel = undefined;
        });
    }

    /**
     * Show model selection quick pick
     */
    public async showModelSelection(): Promise<void> {
        const models = [
            {
                label: '$(brain) Claude Sonnet 4',
                description: 'claude-sonnet-4-20250514',
                detail: 'Latest Claude model with excellent coding capabilities'
            },
            {
                label: '$(brain) GPT-4 Turbo',
                description: 'gpt-4-turbo-preview',
                detail: 'OpenAI GPT-4 Turbo with enhanced performance'
            },
            {
                label: '$(brain) GPT-4',
                description: 'gpt-4',
                detail: 'Standard GPT-4 model'
            },
            {
                label: '$(brain) GPT-3.5 Turbo',
                description: 'gpt-3.5-turbo',
                detail: 'Fast and efficient for most coding tasks'
            }
        ];

        const selection = await vscode.window.showQuickPick(models, {
            placeHolder: 'Select AI model',
            title: 'AI Coding Agent - Model Selection'
        });

        if (selection && selection.description) {
            const config = vscode.workspace.getConfiguration('aiCodingAgent');
            await config.update('modelName', selection.description, vscode.ConfigurationTarget.Global);
            
            vscode.window.showInformationMessage(`Model changed to ${selection.description}`);
        }
    }

    /**
     * Show session statistics
     */
    public async showSessionStats(): Promise<void> {
        // This would show detailed session statistics
        const panel = vscode.window.createWebviewPanel(
            'sessionStats',
            'Session Statistics',
            vscode.ViewColumn.One,
            { enableScripts: false }
        );

        panel.webview.html = this.getStatsHtml();
    }

    /**
     * Generate settings HTML
     */
    private getSettingsHtml(): string {
        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AI Coding Agent Settings</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 20px;
                    line-height: 1.6;
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                }
                .section {
                    background-color: var(--vscode-editor-inactiveSelectionBackground);
                    padding: 20px;
                    border-radius: 5px;
                    margin: 15px 0;
                }
                .setting-group {
                    margin: 15px 0;
                }
                .setting-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 10px 0;
                    padding: 10px;
                    background-color: var(--vscode-input-background);
                    border-radius: 3px;
                }
                input, select {
                    background-color: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border: 1px solid var(--vscode-input-border);
                    padding: 5px 10px;
                    border-radius: 3px;
                }
                button {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    padding: 8px 16px;
                    border-radius: 3px;
                    cursor: pointer;
                }
                button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                h1, h2 {
                    color: var(--vscode-textLink-foreground);
                }
                .description {
                    font-size: 0.9em;
                    color: var(--vscode-descriptionForeground);
                    margin-top: 5px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🤖 AI Coding Agent Settings</h1>
                
                <div class="section">
                    <h2>🔗 Connection Settings</h2>
                    <div class="setting-item">
                        <div>
                            <label>Bridge Host</label>
                            <div class="description">Hostname or IP address of the bridge server</div>
                        </div>
                        <input type="text" id="bridgeHost" value="${this.currentSettings.bridgeHost}">
                    </div>
                    <div class="setting-item">
                        <div>
                            <label>Bridge Port</label>
                            <div class="description">Port number for the bridge server</div>
                        </div>
                        <input type="number" id="bridgePort" value="${this.currentSettings.bridgePort}">
                    </div>
                </div>

                <div class="section">
                    <h2>🧠 AI Model Settings</h2>
                    <div class="setting-item">
                        <div>
                            <label>Model Name</label>
                            <div class="description">AI model to use for completions and chat</div>
                        </div>
                        <select id="modelName">
                            <option value="claude-sonnet-4-20250514" ${this.currentSettings.modelName === 'claude-sonnet-4-20250514' ? 'selected' : ''}>Claude Sonnet 4</option>
                            <option value="gpt-4-turbo-preview" ${this.currentSettings.modelName === 'gpt-4-turbo-preview' ? 'selected' : ''}>GPT-4 Turbo</option>
                            <option value="gpt-4" ${this.currentSettings.modelName === 'gpt-4' ? 'selected' : ''}>GPT-4</option>
                            <option value="gpt-3.5-turbo" ${this.currentSettings.modelName === 'gpt-3.5-turbo' ? 'selected' : ''}>GPT-3.5 Turbo</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <div>
                            <label>Temperature</label>
                            <div class="description">Creativity level (0.0 - 1.0)</div>
                        </div>
                        <input type="range" id="temperature" min="0" max="1" step="0.1" value="${this.currentSettings.temperature}">
                    </div>
                </div>

                <div class="section">
                    <h2>⚡ Completion Settings</h2>
                    <div class="setting-item">
                        <div>
                            <label>Enable Inline Completions</label>
                            <div class="description">Show AI completions as you type</div>
                        </div>
                        <input type="checkbox" id="enableInlineCompletions" ${this.currentSettings.enableInlineCompletions ? 'checked' : ''}>
                    </div>
                    <div class="setting-item">
                        <div>
                            <label>Completion Delay (ms)</label>
                            <div class="description">Delay before showing completions</div>
                        </div>
                        <input type="number" id="completionDelay" value="${this.currentSettings.completionDelay}">
                    </div>
                </div>

                <div class="section">
                    <button onclick="saveSettings()">💾 Save Settings</button>
                    <button onclick="resetSettings()">🔄 Reset to Defaults</button>
                    <button onclick="exportSettings()">📤 Export Settings</button>
                    <button onclick="importSettings()">📥 Import Settings</button>
                </div>
            </div>

            <script>
                const vscode = acquireVsCodeApi();

                function saveSettings() {
                    const settings = {
                        bridgeHost: document.getElementById('bridgeHost').value,
                        bridgePort: parseInt(document.getElementById('bridgePort').value),
                        modelName: document.getElementById('modelName').value,
                        temperature: parseFloat(document.getElementById('temperature').value),
                        enableInlineCompletions: document.getElementById('enableInlineCompletions').checked,
                        completionDelay: parseInt(document.getElementById('completionDelay').value)
                    };
                    
                    vscode.postMessage({
                        command: 'saveSettings',
                        settings: settings
                    });
                }

                function resetSettings() {
                    vscode.postMessage({ command: 'resetSettings' });
                }

                function exportSettings() {
                    vscode.postMessage({ command: 'exportSettings' });
                }

                function importSettings() {
                    vscode.postMessage({ command: 'importSettings' });
                }
            </script>
        </body>
        </html>
        `;
    }

    /**
     * Generate statistics HTML
     */
    private getStatsHtml(): string {
        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Session Statistics</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 20px;
                }
                .stat-card {
                    background-color: var(--vscode-editor-inactiveSelectionBackground);
                    padding: 20px;
                    border-radius: 5px;
                    margin: 10px 0;
                    text-align: center;
                }
                .stat-number {
                    font-size: 2em;
                    font-weight: bold;
                    color: var(--vscode-textLink-foreground);
                }
            </style>
        </head>
        <body>
            <h1>📊 Session Statistics</h1>
            <div class="stat-card">
                <div class="stat-number">42</div>
                <div>Code Completions</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">15</div>
                <div>Chat Messages</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div>Tool Executions</div>
            </div>
        </body>
        </html>
        `;
    }

    /**
     * Handle webview messages
     */
    private async handleWebviewMessage(message: any): Promise<void> {
        switch (message.command) {
            case 'saveSettings':
                await this.saveSettings(message.settings);
                break;
            case 'resetSettings':
                await this.resetSettings();
                break;
            case 'exportSettings':
                await this.exportSettings();
                break;
            case 'importSettings':
                await this.importSettings();
                break;
        }
    }

    /**
     * Save settings to VS Code configuration
     */
    private async saveSettings(settings: Partial<AISettings>): Promise<void> {
        const config = vscode.workspace.getConfiguration('aiCodingAgent');
        
        for (const [key, value] of Object.entries(settings)) {
            await config.update(key, value, vscode.ConfigurationTarget.Global);
        }
        
        vscode.window.showInformationMessage('Settings saved successfully');
    }

    /**
     * Reset settings to defaults
     */
    private async resetSettings(): Promise<void> {
        // Implementation for resetting settings
        vscode.window.showInformationMessage('Settings reset to defaults');
    }

    /**
     * Export settings to file
     */
    private async exportSettings(): Promise<void> {
        // Implementation for exporting settings
        vscode.window.showInformationMessage('Settings exported');
    }

    /**
     * Import settings from file
     */
    private async importSettings(): Promise<void> {
        // Implementation for importing settings
        vscode.window.showInformationMessage('Settings imported');
    }

    /**
     * Get current settings
     */
    public getSettings(): AISettings {
        return { ...this.currentSettings };
    }
}
