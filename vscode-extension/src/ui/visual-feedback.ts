/**
 * Visual Feedback System for AI Coding Agent
 * Provides comprehensive visual indicators, progress tracking, and user notifications
 */

import * as vscode from 'vscode';

export interface ProgressOptions {
    title: string;
    location: vscode.ProgressLocation;
    cancellable?: boolean;
    detail?: string;
}

export interface NotificationOptions {
    type: 'info' | 'warning' | 'error' | 'success';
    message: string;
    actions?: string[];
    modal?: boolean;
    timeout?: number;
}

export interface DecorationOptions {
    range: vscode.Range;
    type: 'completion' | 'error' | 'suggestion' | 'highlight';
    message?: string;
    timeout?: number;
}

export class VisualFeedbackManager {
    private context: vscode.ExtensionContext;
    private outputChannel: vscode.OutputChannel;
    
    // Decoration types
    private completionDecorationType: vscode.TextEditorDecorationType;
    private errorDecorationType: vscode.TextEditorDecorationType;
    private suggestionDecorationType: vscode.TextEditorDecorationType;
    private highlightDecorationType: vscode.TextEditorDecorationType;
    
    // Active decorations tracking
    private activeDecorations: Map<string, vscode.TextEditorDecorationType[]> = new Map();
    
    // Progress tracking
    private activeProgress: Map<string, vscode.Progress<{ message?: string; increment?: number }>> = new Map();

    constructor(context: vscode.ExtensionContext, outputChannel: vscode.OutputChannel) {
        this.context = context;
        this.outputChannel = outputChannel;
        
        this.createDecorationTypes();
        this.setupEventListeners();
    }

    /**
     * Create decoration types for different visual feedback
     */
    private createDecorationTypes(): void {
        // Completion decoration (ghost text style)
        this.completionDecorationType = vscode.window.createTextEditorDecorationType({
            after: {
                color: new vscode.ThemeColor('editorGhostText.foreground'),
                fontStyle: 'italic'
            },
            rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed
        });

        // Error decoration (red underline)
        this.errorDecorationType = vscode.window.createTextEditorDecorationType({
            textDecoration: 'underline wavy',
            color: new vscode.ThemeColor('errorForeground'),
            backgroundColor: new vscode.ThemeColor('errorBackground'),
            borderRadius: '2px'
        });

        // Suggestion decoration (blue highlight)
        this.suggestionDecorationType = vscode.window.createTextEditorDecorationType({
            backgroundColor: new vscode.ThemeColor('editor.findMatchHighlightBackground'),
            border: '1px solid',
            borderColor: new vscode.ThemeColor('editor.findMatchBorder'),
            borderRadius: '2px'
        });

        // Highlight decoration (yellow highlight)
        this.highlightDecorationType = vscode.window.createTextEditorDecorationType({
            backgroundColor: new vscode.ThemeColor('editor.wordHighlightBackground'),
            border: '1px solid',
            borderColor: new vscode.ThemeColor('editor.wordHighlightBorder'),
            borderRadius: '2px'
        });

        // Register for disposal
        this.context.subscriptions.push(
            this.completionDecorationType,
            this.errorDecorationType,
            this.suggestionDecorationType,
            this.highlightDecorationType
        );
    }

    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        // Clear decorations when editor changes
        vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor) {
                this.clearAllDecorations(editor);
            }
        });

        // Clear decorations when document changes
        vscode.workspace.onDidChangeTextDocument(event => {
            const editor = vscode.window.activeTextEditor;
            if (editor && editor.document === event.document) {
                this.clearAllDecorations(editor);
            }
        });
    }

    /**
     * Show progress indicator
     */
    public async showProgress<T>(
        options: ProgressOptions,
        task: (progress: vscode.Progress<{ message?: string; increment?: number }>, token: vscode.CancellationToken) => Thenable<T>
    ): Promise<T> {
        return vscode.window.withProgress(
            {
                location: options.location,
                title: options.title,
                cancellable: options.cancellable || false
            },
            async (progress, token) => {
                const progressId = `progress_${Date.now()}`;
                this.activeProgress.set(progressId, progress);

                try {
                    if (options.detail) {
                        progress.report({ message: options.detail });
                    }

                    const result = await task(progress, token);
                    return result;
                } finally {
                    this.activeProgress.delete(progressId);
                }
            }
        );
    }

    /**
     * Show notification with optional actions
     */
    public async showNotification(options: NotificationOptions): Promise<string | undefined> {
        const { type, message, actions = [], modal = false, timeout } = options;

        let result: string | undefined;

        switch (type) {
            case 'info':
                result = await vscode.window.showInformationMessage(message, { modal }, ...actions);
                break;
            case 'warning':
                result = await vscode.window.showWarningMessage(message, { modal }, ...actions);
                break;
            case 'error':
                result = await vscode.window.showErrorMessage(message, { modal }, ...actions);
                break;
            case 'success':
                // VS Code doesn't have a success message type, use info with success icon
                result = await vscode.window.showInformationMessage(`✅ ${message}`, { modal }, ...actions);
                break;
        }

        // Auto-dismiss after timeout if specified
        if (timeout && timeout > 0) {
            setTimeout(() => {
                // Note: VS Code doesn't provide a way to programmatically dismiss notifications
                // This is a limitation of the VS Code API
            }, timeout);
        }

        return result;
    }

    /**
     * Show completion suggestion as ghost text
     */
    public showCompletionSuggestion(editor: vscode.TextEditor, position: vscode.Position, suggestion: string): void {
        const range = new vscode.Range(position, position);
        
        const decoration: vscode.DecorationOptions = {
            range,
            renderOptions: {
                after: {
                    contentText: suggestion,
                    color: new vscode.ThemeColor('editorGhostText.foreground'),
                    fontStyle: 'italic'
                }
            }
        };

        editor.setDecorations(this.completionDecorationType, [decoration]);
        
        // Auto-clear after 10 seconds
        setTimeout(() => {
            editor.setDecorations(this.completionDecorationType, []);
        }, 10000);
    }

    /**
     * Add decoration to editor
     */
    public addDecoration(editor: vscode.TextEditor, options: DecorationOptions): void {
        const { range, type, message, timeout = 5000 } = options;
        
        let decorationType: vscode.TextEditorDecorationType;
        
        switch (type) {
            case 'completion':
                decorationType = this.completionDecorationType;
                break;
            case 'error':
                decorationType = this.errorDecorationType;
                break;
            case 'suggestion':
                decorationType = this.suggestionDecorationType;
                break;
            case 'highlight':
                decorationType = this.highlightDecorationType;
                break;
            default:
                decorationType = this.highlightDecorationType;
        }

        const decoration: vscode.DecorationOptions = {
            range,
            hoverMessage: message ? new vscode.MarkdownString(message) : undefined
        };

        editor.setDecorations(decorationType, [decoration]);

        // Track decoration for cleanup
        const editorId = editor.document.uri.toString();
        if (!this.activeDecorations.has(editorId)) {
            this.activeDecorations.set(editorId, []);
        }
        this.activeDecorations.get(editorId)!.push(decorationType);

        // Auto-clear after timeout
        if (timeout > 0) {
            setTimeout(() => {
                editor.setDecorations(decorationType, []);
                this.removeDecoration(editorId, decorationType);
            }, timeout);
        }
    }

    /**
     * Clear all decorations for an editor
     */
    public clearAllDecorations(editor: vscode.TextEditor): void {
        const editorId = editor.document.uri.toString();
        const decorations = this.activeDecorations.get(editorId) || [];
        
        decorations.forEach(decoration => {
            editor.setDecorations(decoration, []);
        });
        
        this.activeDecorations.delete(editorId);
    }

    /**
     * Remove specific decoration
     */
    private removeDecoration(editorId: string, decorationType: vscode.TextEditorDecorationType): void {
        const decorations = this.activeDecorations.get(editorId) || [];
        const index = decorations.indexOf(decorationType);
        if (index > -1) {
            decorations.splice(index, 1);
        }
    }

    /**
     * Show loading indicator in status bar
     */
    public showLoadingIndicator(message: string): vscode.Disposable {
        const statusBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Left,
            1000
        );
        
        statusBarItem.text = `$(loading~spin) ${message}`;
        statusBarItem.tooltip = 'AI Coding Agent is processing...';
        statusBarItem.show();

        return statusBarItem;
    }

    /**
     * Show typing indicator for chat
     */
    public showTypingIndicator(): vscode.Disposable {
        const statusBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Left,
            999
        );
        
        let dots = '';
        const interval = setInterval(() => {
            dots = dots.length >= 3 ? '' : dots + '.';
            statusBarItem.text = `$(comment-discussion) AI is typing${dots}`;
        }, 500);

        statusBarItem.show();

        return {
            dispose: () => {
                clearInterval(interval);
                statusBarItem.dispose();
            }
        };
    }

    /**
     * Highlight code ranges
     */
    public highlightCodeRanges(editor: vscode.TextEditor, ranges: vscode.Range[], message?: string): void {
        const decorations: vscode.DecorationOptions[] = ranges.map(range => ({
            range,
            hoverMessage: message ? new vscode.MarkdownString(message) : undefined
        }));

        editor.setDecorations(this.highlightDecorationType, decorations);

        // Auto-clear after 10 seconds
        setTimeout(() => {
            editor.setDecorations(this.highlightDecorationType, []);
        }, 10000);
    }

    /**
     * Show error indicators
     */
    public showErrorIndicators(editor: vscode.TextEditor, errors: { range: vscode.Range; message: string }[]): void {
        const decorations: vscode.DecorationOptions[] = errors.map(error => ({
            range: error.range,
            hoverMessage: new vscode.MarkdownString(`❌ **Error:** ${error.message}`)
        }));

        editor.setDecorations(this.errorDecorationType, decorations);
    }

    /**
     * Show success animation
     */
    public showSuccessAnimation(editor: vscode.TextEditor, range: vscode.Range): void {
        // Create temporary success decoration
        const successDecoration = vscode.window.createTextEditorDecorationType({
            backgroundColor: new vscode.ThemeColor('charts.green'),
            color: new vscode.ThemeColor('editor.background'),
            borderRadius: '2px'
        });

        editor.setDecorations(successDecoration, [{ range }]);

        // Fade out animation
        setTimeout(() => {
            editor.setDecorations(successDecoration, []);
            successDecoration.dispose();
        }, 1000);
    }

    /**
     * Show quick pick with custom styling
     */
    public async showStyledQuickPick<T extends vscode.QuickPickItem>(
        items: T[],
        options: vscode.QuickPickOptions & { title?: string; busy?: boolean }
    ): Promise<T | undefined> {
        const quickPick = vscode.window.createQuickPick<T>();
        
        quickPick.items = items;
        quickPick.placeholder = options.placeHolder;
        quickPick.title = options.title;
        quickPick.canSelectMany = false;
        quickPick.matchOnDescription = options.matchOnDescription;
        quickPick.matchOnDetail = options.matchOnDetail;
        
        if (options.busy) {
            quickPick.busy = true;
        }

        return new Promise<T | undefined>((resolve) => {
            quickPick.onDidChangeSelection(selection => {
                if (selection[0]) {
                    resolve(selection[0]);
                    quickPick.hide();
                }
            });

            quickPick.onDidHide(() => {
                resolve(undefined);
                quickPick.dispose();
            });

            quickPick.show();
        });
    }

    /**
     * Create and show webview with custom styling
     */
    public createStyledWebview(
        viewType: string,
        title: string,
        content: string,
        options?: vscode.WebviewPanelOptions & vscode.WebviewOptions
    ): vscode.WebviewPanel {
        const panel = vscode.window.createWebviewPanel(
            viewType,
            title,
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                ...options
            }
        );

        // Add VS Code theme-aware styling
        const styledContent = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    margin: 0;
                    padding: 20px;
                    line-height: 1.6;
                }
                .container {
                    max-width: 1000px;
                    margin: 0 auto;
                }
                .fade-in {
                    animation: fadeIn 0.3s ease-in;
                }
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(10px); }
                    to { opacity: 1; transform: translateY(0); }
                }
            </style>
        </head>
        <body>
            <div class="container fade-in">
                ${content}
            </div>
        </body>
        </html>
        `;

        panel.webview.html = styledContent;
        return panel;
    }

    /**
     * Dispose of all resources
     */
    public dispose(): void {
        // Clear all active decorations
        this.activeDecorations.clear();
        this.activeProgress.clear();
    }
}
