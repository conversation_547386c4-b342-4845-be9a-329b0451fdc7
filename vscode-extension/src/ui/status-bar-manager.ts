/**
 * Enhanced Status Bar Manager for AI Coding Agent
 * Provides comprehensive status indicators with feature parity to vim extension
 */

import * as vscode from 'vscode';
import { AuthState } from '../auth/oauth-manager';

export interface ConnectionStatus {
    status: 'connected' | 'connecting' | 'disconnected' | 'error';
    message?: string;
    lastUpdate?: Date;
}

export interface ActivityStatus {
    isActive: boolean;
    activity: 'completion' | 'chat' | 'tool_execution' | 'authentication' | 'idle';
    progress?: number;
    message?: string;
}

export class StatusBarManager {
    private context: vscode.ExtensionContext;
    
    // Status bar items
    private authStatusItem!: vscode.StatusBarItem;
    private connectionStatusItem!: vscode.StatusBarItem;
    private activityIndicator!: vscode.StatusBarItem;
    private modelStatusItem!: vscode.StatusBarItem;
    private statsItem!: vscode.StatusBarItem;
    
    // State tracking
    private authState: AuthState = { isAuthenticated: false };
    private connectionStatus: ConnectionStatus = { status: 'disconnected' };
    private activityStatus: ActivityStatus = { isActive: false, activity: 'idle' };
    private currentModel: string = 'claude-sonnet-4-20250514';
    private sessionStats = { completions: 0, chatMessages: 0, toolExecutions: 0 };

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.createStatusBarItems();
        this.updateAllItems();
    }

    /**
     * Create all status bar items with proper ordering
     */
    private createStatusBarItems(): void {
        // Authentication status (rightmost)
        this.authStatusItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            100
        );
        this.authStatusItem.command = 'ai-coding-agent.showAuthMenu';
        this.authStatusItem.tooltip = 'AI Coding Agent Authentication';
        this.context.subscriptions.push(this.authStatusItem);

        // Connection status
        this.connectionStatusItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            99
        );
        this.connectionStatusItem.command = 'ai-coding-agent.checkStatus';
        this.connectionStatusItem.tooltip = 'AI Coding Agent Connection Status';
        this.context.subscriptions.push(this.connectionStatusItem);

        // Activity indicator
        this.activityIndicator = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            98
        );
        this.activityIndicator.tooltip = 'AI Coding Agent Activity';
        this.context.subscriptions.push(this.activityIndicator);

        // Model status
        this.modelStatusItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            97
        );
        this.modelStatusItem.command = 'ai-coding-agent.selectModel';
        this.modelStatusItem.tooltip = 'Current AI Model - Click to change';
        this.context.subscriptions.push(this.modelStatusItem);

        // Session statistics
        this.statsItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            96
        );
        this.statsItem.command = 'ai-coding-agent.showStats';
        this.statsItem.tooltip = 'Session Statistics - Click for details';
        this.context.subscriptions.push(this.statsItem);
    }

    /**
     * Update authentication status
     */
    public updateAuthStatus(authState: AuthState): void {
        this.authState = authState;
        
        if (authState.isAuthenticated && authState.user) {
            this.authStatusItem.text = `$(account) ${authState.user.name}`;
            this.authStatusItem.tooltip = `Authenticated with ${authState.provider}\nClick to manage authentication`;
            this.authStatusItem.backgroundColor = undefined;
            this.authStatusItem.color = new vscode.ThemeColor('statusBar.foreground');
        } else {
            this.authStatusItem.text = '$(account) Sign In';
            this.authStatusItem.tooltip = 'Click to sign in with OAuth provider';
            this.authStatusItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
            this.authStatusItem.color = new vscode.ThemeColor('statusBarItem.warningForeground');
        }
        
        this.authStatusItem.show();
    }

    /**
     * Update connection status
     */
    public updateConnectionStatus(status: ConnectionStatus): void {
        this.connectionStatus = status;
        
        switch (status.status) {
            case 'connected':
                this.connectionStatusItem.text = '$(plug) Connected';
                this.connectionStatusItem.tooltip = `Connected to AI Coding Agent\n${status.message || 'Bridge server is responsive'}`;
                this.connectionStatusItem.backgroundColor = undefined;
                this.connectionStatusItem.color = new vscode.ThemeColor('charts.green');
                break;
                
            case 'connecting':
                this.connectionStatusItem.text = '$(sync~spin) Connecting';
                this.connectionStatusItem.tooltip = 'Connecting to AI Coding Agent...';
                this.connectionStatusItem.backgroundColor = undefined;
                this.connectionStatusItem.color = new vscode.ThemeColor('charts.yellow');
                break;
                
            case 'disconnected':
                this.connectionStatusItem.text = '$(plug) Disconnected';
                this.connectionStatusItem.tooltip = 'Not connected to AI Coding Agent\nClick to check connection';
                this.connectionStatusItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
                this.connectionStatusItem.color = new vscode.ThemeColor('statusBarItem.errorForeground');
                break;
                
            case 'error':
                this.connectionStatusItem.text = '$(error) Error';
                this.connectionStatusItem.tooltip = `Connection error: ${status.message || 'Unknown error'}`;
                this.connectionStatusItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
                this.connectionStatusItem.color = new vscode.ThemeColor('statusBarItem.errorForeground');
                break;
        }
        
        this.connectionStatusItem.show();
    }

    /**
     * Update activity status
     */
    public updateActivityStatus(activity: ActivityStatus): void {
        this.activityStatus = activity;
        
        if (activity.isActive) {
            const icons = {
                completion: '$(loading~spin)',
                chat: '$(comment-discussion)',
                tool_execution: '$(tools)',
                authentication: '$(key)',
                idle: '$(circle-outline)'
            };
            
            const icon = icons[activity.activity] || '$(circle-outline)';
            let text = icon;
            
            if (activity.progress !== undefined) {
                text += ` ${Math.round(activity.progress)}%`;
            }
            
            this.activityIndicator.text = text;
            this.activityIndicator.tooltip = `AI Activity: ${activity.activity.replace('_', ' ')}\n${activity.message || 'Processing...'}`;
            this.activityIndicator.color = new vscode.ThemeColor('charts.blue');
            this.activityIndicator.show();
        } else {
            this.activityIndicator.hide();
        }
    }

    /**
     * Update model status
     */
    public updateModelStatus(model: string): void {
        this.currentModel = model;
        
        // Shorten model name for display
        const displayName = model.replace('claude-sonnet-4-', 'Claude-4-').replace('gpt-', 'GPT-');
        
        this.modelStatusItem.text = `$(brain) ${displayName}`;
        this.modelStatusItem.tooltip = `Current AI Model: ${model}\nClick to change model`;
        this.modelStatusItem.show();
    }

    /**
     * Update session statistics
     */
    public updateStats(stats: { completions?: number; chatMessages?: number; toolExecutions?: number }): void {
        if (stats.completions !== undefined) this.sessionStats.completions = stats.completions;
        if (stats.chatMessages !== undefined) this.sessionStats.chatMessages = stats.chatMessages;
        if (stats.toolExecutions !== undefined) this.sessionStats.toolExecutions = stats.toolExecutions;
        
        const total = this.sessionStats.completions + this.sessionStats.chatMessages + this.sessionStats.toolExecutions;
        
        if (total > 0) {
            this.statsItem.text = `$(graph) ${total}`;
            this.statsItem.tooltip = `Session Statistics:\n• Completions: ${this.sessionStats.completions}\n• Chat Messages: ${this.sessionStats.chatMessages}\n• Tool Executions: ${this.sessionStats.toolExecutions}`;
            this.statsItem.show();
        } else {
            this.statsItem.hide();
        }
    }

    /**
     * Increment completion counter
     */
    public incrementCompletions(): void {
        this.sessionStats.completions++;
        this.updateStats({});
    }

    /**
     * Increment chat message counter
     */
    public incrementChatMessages(): void {
        this.sessionStats.chatMessages++;
        this.updateStats({});
    }

    /**
     * Increment tool execution counter
     */
    public incrementToolExecutions(): void {
        this.sessionStats.toolExecutions++;
        this.updateStats({});
    }

    /**
     * Show activity for a specific duration
     */
    public showTemporaryActivity(activity: ActivityStatus['activity'], message: string, duration: number = 3000): void {
        this.updateActivityStatus({
            isActive: true,
            activity,
            message
        });
        
        setTimeout(() => {
            this.updateActivityStatus({ isActive: false, activity: 'idle' });
        }, duration);
    }

    /**
     * Update all status bar items
     */
    private updateAllItems(): void {
        this.updateAuthStatus(this.authState);
        this.updateConnectionStatus(this.connectionStatus);
        this.updateActivityStatus(this.activityStatus);
        this.updateModelStatus(this.currentModel);
        this.updateStats({});
    }

    /**
     * Hide all status bar items
     */
    public hideAll(): void {
        this.authStatusItem.hide();
        this.connectionStatusItem.hide();
        this.activityIndicator.hide();
        this.modelStatusItem.hide();
        this.statsItem.hide();
    }

    /**
     * Show all status bar items
     */
    public showAll(): void {
        this.authStatusItem.show();
        this.connectionStatusItem.show();
        this.modelStatusItem.show();
        // Activity and stats are conditionally shown
        if (this.activityStatus.isActive) {
            this.activityIndicator.show();
        }
        if (this.sessionStats.completions + this.sessionStats.chatMessages + this.sessionStats.toolExecutions > 0) {
            this.statsItem.show();
        }
    }

    /**
     * Dispose of all status bar items
     */
    public dispose(): void {
        this.authStatusItem.dispose();
        this.connectionStatusItem.dispose();
        this.activityIndicator.dispose();
        this.modelStatusItem.dispose();
        this.statsItem.dispose();
    }
}
