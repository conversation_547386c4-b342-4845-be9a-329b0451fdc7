/**
 * Context Menu Provider for AI Coding Agent
 * Provides right-click AI actions with intelligent context awareness
 */

import * as vscode from 'vscode';
import { BridgeClient } from '../bridge-client';
import { StatusBarManager } from './status-bar-manager';

export interface ContextMenuAction {
    id: string;
    label: string;
    icon: string;
    description: string;
    when?: string; // VS Code when clause
    group?: string;
    order?: number;
}

export class ContextMenuProvider {
    private context: vscode.ExtensionContext;
    private bridgeClient: BridgeClient;
    private statusBarManager: StatusBarManager;
    private outputChannel: vscode.OutputChannel;

    constructor(
        context: vscode.ExtensionContext,
        bridgeClient: BridgeClient,
        statusBarManager: StatusBarManager,
        outputChannel: vscode.OutputChannel
    ) {
        this.context = context;
        this.bridgeClient = bridgeClient;
        this.statusBarManager = statusBarManager;
        this.outputChannel = outputChannel;

        this.registerContextMenuCommands();
        this.registerMenuContributions();
    }

    /**
     * Register all context menu commands
     */
    private registerContextMenuCommands(): void {
        const commands = [
            // Code analysis commands
            {
                command: 'ai-coding-agent.explainCode',
                callback: () => this.explainCode()
            },
            {
                command: 'ai-coding-agent.reviewCode',
                callback: () => this.reviewCode()
            },
            {
                command: 'ai-coding-agent.optimizeCode',
                callback: () => this.optimizeCode()
            },
            {
                command: 'ai-coding-agent.generateTests',
                callback: () => this.generateTests()
            },
            {
                command: 'ai-coding-agent.generateDocumentation',
                callback: () => this.generateDocumentation()
            },
            
            // Code modification commands
            {
                command: 'ai-coding-agent.refactorCode',
                callback: () => this.refactorCode()
            },
            {
                command: 'ai-coding-agent.fixCode',
                callback: () => this.fixCode()
            },
            {
                command: 'ai-coding-agent.addComments',
                callback: () => this.addComments()
            },
            
            // Chat and completion commands
            {
                command: 'ai-coding-agent.chatAboutCode',
                callback: () => this.chatAboutCode()
            },
            {
                command: 'ai-coding-agent.getCompletion',
                callback: () => this.getCompletion()
            },
            
            // File operations
            {
                command: 'ai-coding-agent.analyzeFile',
                callback: () => this.analyzeFile()
            },
            {
                command: 'ai-coding-agent.summarizeFile',
                callback: () => this.summarizeFile()
            }
        ];

        commands.forEach(({ command, callback }) => {
            const disposable = vscode.commands.registerCommand(command, callback);
            this.context.subscriptions.push(disposable);
        });
    }

    /**
     * Register menu contributions programmatically
     */
    private registerMenuContributions(): void {
        // Note: In a real extension, these would be defined in package.json
        // This is a programmatic approach for dynamic menu registration
        
        // We'll use the command palette and quick pick as fallbacks
        // since VS Code menu contributions must be declared in package.json
    }

    /**
     * Show AI actions quick pick menu
     */
    public async showAIActionsMenu(): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor');
            return;
        }

        const selection = editor.selection;
        const hasSelection = !selection.isEmpty;
        const document = editor.document;
        const language = document.languageId;

        const actions: vscode.QuickPickItem[] = [
            // Code analysis actions
            {
                label: '$(search-details) Explain Code',
                description: hasSelection ? 'Explain selected code' : 'Explain current function/class',
                detail: 'Get AI explanation of code functionality'
            },
            {
                label: '$(checklist) Review Code',
                description: 'Get code review and suggestions',
                detail: 'AI-powered code review with improvement suggestions'
            },
            {
                label: '$(rocket) Optimize Code',
                description: 'Optimize for performance and readability',
                detail: 'Get optimization suggestions and improvements'
            },
            {
                label: '$(beaker) Generate Tests',
                description: `Generate ${language} unit tests`,
                detail: 'Create comprehensive test cases for the code'
            },
            {
                label: '$(book) Generate Documentation',
                description: 'Generate documentation and comments',
                detail: 'Create detailed documentation for functions and classes'
            },
            
            // Code modification actions
            {
                label: '$(tools) Refactor Code',
                description: 'Refactor and improve structure',
                detail: 'Restructure code while maintaining functionality'
            },
            {
                label: '$(debug-alt) Fix Code',
                description: 'Find and fix potential issues',
                detail: 'Identify and suggest fixes for bugs and issues'
            },
            {
                label: '$(comment) Add Comments',
                description: 'Add meaningful comments',
                detail: 'Generate inline and block comments'
            },
            
            // Interactive actions
            {
                label: '$(comment-discussion) Chat About Code',
                description: 'Start conversation about this code',
                detail: 'Open chat with code context for questions'
            },
            {
                label: '$(lightbulb) Get Completion',
                description: 'Get AI code completion',
                detail: 'Generate code completion suggestions'
            },
            
            // File-level actions
            {
                label: '$(file-code) Analyze File',
                description: 'Analyze entire file structure',
                detail: 'Get comprehensive file analysis and insights'
            },
            {
                label: '$(list-unordered) Summarize File',
                description: 'Generate file summary',
                detail: 'Create a summary of file contents and purpose'
            }
        ];

        const selectedAction = await vscode.window.showQuickPick(actions, {
            placeHolder: 'Select an AI action',
            title: 'AI Coding Agent - Actions',
            matchOnDescription: true,
            matchOnDetail: true
        });

        if (selectedAction) {
            await this.executeAction(selectedAction.label);
        }
    }

    /**
     * Execute action based on label
     */
    private async executeAction(label: string): Promise<void> {
        switch (label) {
            case '$(search-details) Explain Code':
                await this.explainCode();
                break;
            case '$(checklist) Review Code':
                await this.reviewCode();
                break;
            case '$(rocket) Optimize Code':
                await this.optimizeCode();
                break;
            case '$(beaker) Generate Tests':
                await this.generateTests();
                break;
            case '$(book) Generate Documentation':
                await this.generateDocumentation();
                break;
            case '$(tools) Refactor Code':
                await this.refactorCode();
                break;
            case '$(debug-alt) Fix Code':
                await this.fixCode();
                break;
            case '$(comment) Add Comments':
                await this.addComments();
                break;
            case '$(comment-discussion) Chat About Code':
                await this.chatAboutCode();
                break;
            case '$(lightbulb) Get Completion':
                await this.getCompletion();
                break;
            case '$(file-code) Analyze File':
                await this.analyzeFile();
                break;
            case '$(list-unordered) Summarize File':
                await this.summarizeFile();
                break;
        }
    }

    /**
     * Get current code context
     */
    private getCodeContext(): { code: string; language: string; filePath: string; hasSelection: boolean } | null {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return null;
        }

        const selection = editor.selection;
        const hasSelection = !selection.isEmpty;
        const code = hasSelection ? editor.document.getText(selection) : editor.document.getText();
        
        return {
            code,
            language: editor.document.languageId,
            filePath: editor.document.fileName,
            hasSelection
        };
    }

    /**
     * Explain code functionality
     */
    private async explainCode(): Promise<void> {
        const context = this.getCodeContext();
        if (!context) return;

        this.statusBarManager.updateActivityStatus({
            isActive: true,
            activity: 'completion',
            message: 'Explaining code...'
        });

        try {
            const prompt = `Please explain the following ${context.language} code:\n\n\`\`\`${context.language}\n${context.code}\n\`\`\`\n\nProvide a clear explanation of what this code does, how it works, and any notable patterns or techniques used.`;
            
            const response = await this.bridgeClient.sendChatMessage(prompt);
            await this.showResponseInPanel('Code Explanation', response.message);
            
        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to explain code: ${error.message}`);
        } finally {
            this.statusBarManager.updateActivityStatus({ isActive: false, activity: 'idle' });
        }
    }

    /**
     * Review code for improvements
     */
    private async reviewCode(): Promise<void> {
        const context = this.getCodeContext();
        if (!context) return;

        this.statusBarManager.updateActivityStatus({
            isActive: true,
            activity: 'completion',
            message: 'Reviewing code...'
        });

        try {
            const prompt = `Please review the following ${context.language} code and provide suggestions for improvement:\n\n\`\`\`${context.language}\n${context.code}\n\`\`\`\n\nFocus on:\n- Code quality and best practices\n- Performance optimizations\n- Security considerations\n- Maintainability\n- Potential bugs or issues`;
            
            const response = await this.bridgeClient.sendChatMessage(prompt);
            await this.showResponseInPanel('Code Review', response.message);
            
        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to review code: ${error.message}`);
        } finally {
            this.statusBarManager.updateActivityStatus({ isActive: false, activity: 'idle' });
        }
    }

    /**
     * Show AI response in a webview panel
     */
    private async showResponseInPanel(title: string, content: string): Promise<void> {
        const panel = vscode.window.createWebviewPanel(
            'aiResponse',
            title,
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        panel.webview.html = this.getResponseHtml(title, content);
    }

    /**
     * Generate HTML for response panel
     */
    private getResponseHtml(title: string, content: string): string {
        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 20px;
                    line-height: 1.6;
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                }
                h1 {
                    color: var(--vscode-textLink-foreground);
                    border-bottom: 1px solid var(--vscode-textSeparator-foreground);
                    padding-bottom: 10px;
                }
                .content {
                    background-color: var(--vscode-editor-inactiveSelectionBackground);
                    padding: 20px;
                    border-radius: 5px;
                    margin: 15px 0;
                }
                pre {
                    background-color: var(--vscode-textCodeBlock-background);
                    padding: 10px;
                    border-radius: 3px;
                    overflow-x: auto;
                }
                code {
                    background-color: var(--vscode-textCodeBlock-background);
                    padding: 2px 4px;
                    border-radius: 3px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🤖 ${title}</h1>
                <div class="content">
                    ${content.replace(/\n/g, '<br>').replace(/`([^`]+)`/g, '<code>$1</code>')}
                </div>
            </div>
        </body>
        </html>
        `;
    }

    // Placeholder implementations for other actions
    private async optimizeCode(): Promise<void> { /* Implementation similar to explainCode */ }
    private async generateTests(): Promise<void> { /* Implementation similar to explainCode */ }
    private async generateDocumentation(): Promise<void> { /* Implementation similar to explainCode */ }
    private async refactorCode(): Promise<void> { /* Implementation similar to explainCode */ }
    private async fixCode(): Promise<void> { /* Implementation similar to explainCode */ }
    private async addComments(): Promise<void> { /* Implementation similar to explainCode */ }
    private async chatAboutCode(): Promise<void> { /* Implementation similar to explainCode */ }
    private async getCompletion(): Promise<void> { /* Implementation similar to explainCode */ }
    private async analyzeFile(): Promise<void> { /* Implementation similar to explainCode */ }
    private async summarizeFile(): Promise<void> { /* Implementation similar to explainCode */ }
}
