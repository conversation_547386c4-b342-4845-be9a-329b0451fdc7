/**
 * Comprehensive demo script for VS Code Extension functionality
 * This demonstrates all the features of the AI Coding Agent VS Code Extension
 */

const axios = require('axios');
const { io } = require('socket.io-client');

// Configuration
const BRIDGE_HOST = 'localhost';
const BRIDGE_PORT = 8080;
const BASE_URL = `http://${BRIDGE_HOST}:${BRIDGE_PORT}`;

/**
 * Demo: Full workflow simulation
 */
async function demoFullWorkflow() {
    console.log('🎬 Starting AI Coding Agent VS Code Extension Demo\n');
    console.log('='.repeat(60));
    
    try {
        // Step 1: Check bridge connectivity
        console.log('📡 Step 1: Checking bridge connectivity...');
        const healthResponse = await axios.get(`${BASE_URL}/health`);
        console.log('   ✅ Bridge is healthy:', healthResponse.data);
        
        // Step 2: Simulate VS Code workspace context
        console.log('\n📁 Step 2: Simulating VS Code workspace context...');
        const workspaceContext = {
            workspaceFolder: '/path/to/my-project',
            activeFile: '/path/to/my-project/src/main.py',
            language: 'python',
            selection: 'def calculate_fibonacci(n):\n    # TODO: Implement',
            cursorPosition: { line: 10, column: 5 }
        };
        console.log('   📝 Active file:', workspaceContext.activeFile);
        console.log('   🔤 Language:', workspaceContext.language);
        console.log('   ✂️  Selection:', workspaceContext.selection.replace('\n', '\\n'));
        
        // Step 3: Create session with context
        console.log('\n🚀 Step 3: Creating AI agent session...');
        const sessionData = {
            problem_statement: `Please implement the Fibonacci function in the selected code. 
                               Context: Working on ${workspaceContext.activeFile} 
                               Selected code: ${workspaceContext.selection}`,
            repo_path: workspaceContext.workspaceFolder,
            model_name: 'claude-3-opus-20240229'
        };
        
        const createResponse = await axios.post(`${BASE_URL}/api/sessions`, sessionData);
        const sessionId = createResponse.data.session_id;
        console.log('   ✅ Session created:', sessionId);
        
        // Step 4: Start session
        console.log('\n▶️  Step 4: Starting AI agent session...');
        const startResponse = await axios.post(`${BASE_URL}/api/sessions/${sessionId}/start`);
        console.log('   ✅ Session started:', startResponse.data);
        
        // Step 5: Monitor session status
        console.log('\n📊 Step 5: Monitoring session status...');
        const statusResponse = await axios.get(`${BASE_URL}/api/sessions/${sessionId}`);
        console.log('   📈 Current status:', statusResponse.data.status);
        console.log('   🕐 Created at:', statusResponse.data.created_at);
        
        // Step 6: Simulate real-time updates (WebSocket)
        console.log('\n🔄 Step 6: Demonstrating real-time updates...');
        await demoWebSocketUpdates(sessionId);
        
        // Step 7: Get trajectory
        console.log('\n📋 Step 7: Getting execution trajectory...');
        const trajectoryResponse = await axios.get(`${BASE_URL}/api/sessions/${sessionId}/trajectory`);
        console.log('   📊 Trajectory status:', trajectoryResponse.data.status);
        console.log('   📝 Steps count:', trajectoryResponse.data.trajectory.length);
        
        // Step 8: Stop session
        console.log('\n⏹️  Step 8: Stopping session...');
        const stopResponse = await axios.post(`${BASE_URL}/api/sessions/${sessionId}/stop`);
        console.log('   ✅ Session stopped:', stopResponse.data);
        
        // Step 9: Final status check
        console.log('\n🏁 Step 9: Final status check...');
        const finalStatusResponse = await axios.get(`${BASE_URL}/api/sessions/${sessionId}`);
        console.log('   📊 Final status:', finalStatusResponse.data.status);
        
        console.log('\n' + '='.repeat(60));
        console.log('🎉 Demo completed successfully!');
        console.log('\n💡 This demonstrates how the VS Code extension:');
        console.log('   ✅ Connects to the bridge');
        console.log('   ✅ Sends workspace context to the AI agent');
        console.log('   ✅ Manages session lifecycle');
        console.log('   ✅ Receives real-time updates');
        console.log('   ✅ Tracks execution progress');
        console.log('   ✅ Handles errors gracefully');
        
    } catch (error) {
        console.error('\n❌ Demo failed:', error.message);
        if (error.response) {
            console.error('   Response:', error.response.data);
        }
    }
}

/**
 * Demo WebSocket real-time updates
 */
async function demoWebSocketUpdates(sessionId) {
    return new Promise((resolve) => {
        console.log('   🔌 Connecting to WebSocket...');
        
        const socket = io(BASE_URL, {
            transports: ['websocket', 'polling']
        });
        
        socket.on('connect', () => {
            console.log('   ✅ WebSocket connected');
            
            // Subscribe to session updates
            socket.emit('subscribe_session', { session_id: sessionId });
            console.log('   📡 Subscribed to session updates');
            
            // Simulate some events
            setTimeout(() => {
                console.log('   📨 Simulating session events...');
                socket.emit('session_event', {
                    session_id: sessionId,
                    event_type: 'progress_update',
                    message: 'AI agent is analyzing the code...'
                });
            }, 1000);
            
            setTimeout(() => {
                socket.emit('trajectory_updated', {
                    session_id: sessionId,
                    step: {
                        action: 'code_analysis',
                        result: 'Found Fibonacci function stub',
                        timestamp: new Date().toISOString()
                    }
                });
                console.log('   📊 Trajectory update sent');
            }, 2000);
            
            setTimeout(() => {
                socket.disconnect();
                console.log('   🔌 WebSocket disconnected');
                resolve();
            }, 3000);
        });
        
        socket.on('session_event', (data) => {
            console.log('   📨 Received session event:', data);
        });
        
        socket.on('trajectory_updated', (data) => {
            console.log('   📊 Received trajectory update:', data);
        });
        
        socket.on('connect_error', (error) => {
            console.log('   ❌ WebSocket connection error:', error.message);
            resolve();
        });
    });
}

/**
 * Demo error handling
 */
async function demoErrorHandling() {
    console.log('\n🛠️  Testing error handling...');
    
    try {
        // Test invalid session ID
        await axios.get(`${BASE_URL}/api/sessions/invalid-session-id`);
    } catch (error) {
        console.log('   ✅ Correctly handled invalid session ID:', error.response.status);
    }
    
    try {
        // Test missing required fields
        await axios.post(`${BASE_URL}/api/sessions`, {});
    } catch (error) {
        console.log('   ✅ Correctly handled missing fields:', error.response.status);
    }
}

// Run demo if this file is executed directly
if (require.main === module) {
    demoFullWorkflow()
        .then(() => demoErrorHandling())
        .catch(console.error);
}

module.exports = {
    demoFullWorkflow,
    demoWebSocketUpdates,
    demoErrorHandling
};
