{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020", "DOM"], "sourceMap": true, "rootDir": "src", "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUnusedParameters": false, "noUnusedLocals": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "exclude": ["node_modules", ".vscode-test"]}