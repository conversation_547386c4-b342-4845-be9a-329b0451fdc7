/**
 * Simple test script to verify VS Code extension functionality
 * This can be run independently to test the bridge client
 */

const axios = require('axios');

// Configuration
const BRIDGE_HOST = 'localhost';
const BRIDGE_PORT = 8080;
const BASE_URL = `http://${BRIDGE_HOST}:${BRIDGE_PORT}`;

/**
 * Test bridge connectivity
 */
async function testBridgeConnection() {
    console.log('Testing bridge connection...');
    
    try {
        const response = await axios.get(`${BASE_URL}/health`, {
            timeout: 5000
        });
        
        if (response.data.status === 'ok') {
            console.log('✅ Bridge is healthy and responding');
            return true;
        } else {
            console.log('❌ Bridge responded but status is not ok:', response.data);
            return false;
        }
    } catch (error) {
        console.log('❌ Failed to connect to bridge:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('   Make sure the bridge is running on port', BRIDGE_PORT);
        }
        return false;
    }
}

/**
 * Test session creation
 */
async function testSessionCreation() {
    console.log('\nTesting session creation...');
    
    try {
        const sessionConfig = {
            problem_statement: "Test problem statement from VS Code extension",
            repo_path: "/tmp/test-repo",
            model_name: "claude-3-opus-20240229"
        };
        
        const response = await axios.post(`${BASE_URL}/api/sessions`, sessionConfig, {
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.data.status === 'created' && response.data.session_id) {
            console.log('✅ Session created successfully:', response.data.session_id);
            return response.data.session_id;
        } else {
            console.log('❌ Session creation failed:', response.data);
            return null;
        }
    } catch (error) {
        console.log('❌ Error creating session:', error.message);
        if (error.response) {
            console.log('   Response:', error.response.data);
        }
        return null;
    }
}

/**
 * Test session status check
 */
async function testSessionStatus(sessionId) {
    if (!sessionId) {
        console.log('⏭️  Skipping session status test (no session ID)');
        return;
    }
    
    console.log('\nTesting session status...');
    
    try {
        const response = await axios.get(`${BASE_URL}/api/sessions/${sessionId}`, {
            timeout: 5000
        });
        
        console.log('✅ Session status retrieved:', response.data);
    } catch (error) {
        console.log('❌ Error getting session status:', error.message);
        if (error.response) {
            console.log('   Response:', error.response.data);
        }
    }
}

/**
 * Test available endpoints
 */
async function testAvailableEndpoints() {
    console.log('\nTesting available endpoints...');
    
    const endpoints = [
        '/health',
        '/api/sessions',
        '/api/completion/health',
        '/api/chat/sessions'
    ];
    
    for (const endpoint of endpoints) {
        try {
            const response = await axios.get(`${BASE_URL}${endpoint}`, {
                timeout: 3000
            });
            console.log(`✅ ${endpoint}: Available`);
        } catch (error) {
            if (error.response && error.response.status === 405) {
                console.log(`✅ ${endpoint}: Available (Method Not Allowed - expected for POST endpoints)`);
            } else {
                console.log(`❌ ${endpoint}: ${error.message}`);
            }
        }
    }
}

/**
 * Test session lifecycle
 */
async function testSessionLifecycle(sessionId) {
    if (!sessionId) {
        console.log('⏭️  Skipping session lifecycle test (no session ID)');
        return;
    }

    console.log('\nTesting session lifecycle...');

    try {
        // Test starting session
        console.log('  Starting session...');
        const startResponse = await axios.post(`${BASE_URL}/api/sessions/${sessionId}/start`, {}, {
            timeout: 5000
        });

        if (startResponse.data.status === 'success') {
            console.log('  ✅ Session started successfully');
        } else {
            console.log('  ❌ Failed to start session:', startResponse.data);
        }

        // Wait a moment
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Test stopping session
        console.log('  Stopping session...');
        const stopResponse = await axios.post(`${BASE_URL}/api/sessions/${sessionId}/stop`, {}, {
            timeout: 5000
        });

        if (stopResponse.data.status === 'success') {
            console.log('  ✅ Session stopped successfully');
        } else {
            console.log('  ❌ Failed to stop session:', stopResponse.data);
        }

        // Test trajectory endpoint
        console.log('  Getting trajectory...');
        const trajectoryResponse = await axios.get(`${BASE_URL}/api/sessions/${sessionId}/trajectory`, {
            timeout: 5000
        });

        if (trajectoryResponse.data.status === 'success') {
            console.log('  ✅ Trajectory retrieved successfully');
        } else {
            console.log('  ❌ Failed to get trajectory:', trajectoryResponse.data);
        }

    } catch (error) {
        console.log('  ❌ Error in session lifecycle:', error.message);
    }
}

/**
 * Main test function
 */
async function runTests() {
    console.log('🚀 Starting VS Code Extension Bridge Tests\n');
    console.log('='.repeat(50));

    // Test 1: Bridge connection
    const isConnected = await testBridgeConnection();

    if (!isConnected) {
        console.log('\n❌ Cannot proceed with tests - bridge is not available');
        console.log('\nTo start the bridge, run:');
        console.log('  cd /path/to/AI-Coding-Agent');
        console.log('  python start_api_server.py');
        return;
    }

    // Test 2: Available endpoints
    await testAvailableEndpoints();

    // Test 3: Session creation
    const sessionId = await testSessionCreation();

    // Test 4: Session status
    await testSessionStatus(sessionId);

    // Test 5: Session lifecycle
    await testSessionLifecycle(sessionId);

    console.log('\n' + '='.repeat(50));
    console.log('🏁 Tests completed');

    if (sessionId) {
        console.log('\n💡 VS Code Extension is fully functional:');
        console.log('   ✅ Bridge connectivity working');
        console.log('   ✅ Session management working');
        console.log('   ✅ Session lifecycle working');
        console.log('   ✅ All required endpoints available');
        console.log('\n🎉 Ready for VS Code integration!');
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    testBridgeConnection,
    testSessionCreation,
    testSessionStatus,
    testSessionLifecycle,
    testAvailableEndpoints,
    runTests
};
