{"name": "ai-coding-agent", "displayName": "AI Coding Agent", "description": "VS Code extension for AI Coding Agent that integrates with SWE-Agent and OAuth authentication", "version": "0.2.0", "engines": {"vscode": "^1.75.0"}, "categories": ["Other"], "activationEvents": ["*"], "icon": "icon.png", "main": "./out/extension.js", "contributes": {"commands": [{"command": "ai-coding-agent.runAgent", "title": "AI Coding Agent: Run Agent"}, {"command": "ai-coding-agent.stopAgent", "title": "AI Coding Agent: Stop Agent"}, {"command": "ai-coding-agent.checkStatus", "title": "AI Coding Agent: Check Status"}, {"command": "ai-coding-agent.openChat", "title": "AI Coding Agent: Open Chat"}, {"command": "ai-coding-agent.login", "title": "AI Coding Agent: Sign In"}, {"command": "ai-coding-agent.logout", "title": "AI Coding Agent: Sign Out"}, {"command": "ai-coding-agent.showAuthMenu", "title": "AI Coding Agent: Au<PERSON>nti<PERSON> <PERSON>u"}, {"command": "ai-coding-agent.showUserInfo", "title": "AI Coding Agent: User Information"}, {"command": "ai-coding-agent.openSettings", "title": "AI Coding Agent: Open Settings"}, {"command": "ai-coding-agent.selectModel", "title": "AI Coding Agent: Select Model"}, {"command": "ai-coding-agent.showStats", "title": "AI Coding Agent: Show Statistics"}, {"command": "ai-coding-agent.showAIActions", "title": "AI Coding Agent: Show AI Actions"}, {"command": "ai-coding-agent.explainCode", "title": "AI Coding Agent: Explain Code"}, {"command": "ai-coding-agent.reviewCode", "title": "AI Coding Agent: Review Code"}, {"command": "ai-coding-agent.optimizeCode", "title": "AI Coding Agent: Optimize Code"}, {"command": "ai-coding-agent.generateTests", "title": "AI Coding Agent: Generate Tests"}, {"command": "ai-coding-agent.refactorCode", "title": "AI Coding Agent: Refactor Code"}], "menus": {"editor/context": [{"command": "ai-coding-agent.showAIActions", "when": "editorTextFocus", "group": "ai-coding-agent@1"}, {"command": "ai-coding-agent.explainCode", "when": "editorHasSelection", "group": "ai-coding-agent@2"}, {"command": "ai-coding-agent.reviewCode", "when": "editorTextFocus", "group": "ai-coding-agent@3"}, {"command": "ai-coding-agent.refactorCode", "when": "editorHasSelection", "group": "ai-coding-agent@4"}], "commandPalette": [{"command": "ai-coding-agent.explainCode", "when": "editorTextFocus"}, {"command": "ai-coding-agent.reviewCode", "when": "editorTextFocus"}, {"command": "ai-coding-agent.optimizeCode", "when": "editorTextFocus"}, {"command": "ai-coding-agent.generateTests", "when": "editorTextFocus"}, {"command": "ai-coding-agent.refactorCode", "when": "editorTextFocus"}]}, "configuration": {"title": "AI Coding Agent", "properties": {"aiCodingAgent.bridgeHost": {"type": "string", "default": "localhost", "description": "Host address for the AI Coding Agent bridge"}, "aiCodingAgent.bridgePort": {"type": "number", "default": 8080, "description": "Port number for the AI Coding Agent bridge"}, "aiCodingAgent.modelName": {"type": "string", "default": "claude-sonnet-4-20250514", "description": "Model name to use for the AI Coding Agent"}, "aiCodingAgent.autoConnect": {"type": "boolean", "default": true, "description": "Automatically connect to the bridge on extension activation"}, "aiCodingAgent.enableWebSocket": {"type": "boolean", "default": true, "description": "Enable WebSocket connection for real-time updates"}, "aiCodingAgent.timeout": {"type": "number", "default": 30000, "description": "Request timeout in milliseconds"}, "aiCodingAgent.requireAuthentication": {"type": "boolean", "default": true, "description": "Require OAuth authentication for AI Coding Agent operations"}, "aiCodingAgent.autoRefreshTokens": {"type": "boolean", "default": true, "description": "Automatically refresh OAuth tokens when they expire"}, "aiCodingAgent.enableInlineCompletions": {"type": "boolean", "default": true, "description": "Enable inline code completions"}, "aiCodingAgent.completionDelay": {"type": "number", "default": 500, "description": "Delay in milliseconds before showing completions"}, "aiCodingAgent.maxCompletions": {"type": "number", "default": 3, "description": "Maximum number of completion suggestions"}, "aiCodingAgent.showStatusBar": {"type": "boolean", "default": true, "description": "Show status bar indicators"}, "aiCodingAgent.showActivityIndicator": {"type": "boolean", "default": true, "description": "Show activity indicator in status bar"}, "aiCodingAgent.enableContextMenus": {"type": "boolean", "default": true, "description": "Enable AI actions in context menus"}, "aiCodingAgent.temperature": {"type": "number", "default": 0.7, "minimum": 0.0, "maximum": 1.0, "description": "AI model temperature (creativity level)"}, "aiCodingAgent.enableDebugLogging": {"type": "boolean", "default": false, "description": "Enable debug logging for troubleshooting"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/glob": "^7.1.4", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/vscode": "^1.60.0", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "eslint": "^8.1.0", "glob": "^7.1.7", "mocha": "^9.1.3", "typescript": "^4.4.4", "vscode-test": "^1.6.1"}, "dependencies": {"axios": "^0.24.0", "socket.io-client": "^4.4.0"}, "keywords": ["ai", "coding", "assistant", "swe-agent", "automation", "development", "productivity", "o<PERSON>h", "authentication"], "author": "AI Coding Agent Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/ai-coding-agent.git"}, "bugs": {"url": "https://github.com/your-org/ai-coding-agent/issues"}, "homepage": "https://github.com/your-org/ai-coding-agent#readme", "publisher": "ai-coding-agent"}