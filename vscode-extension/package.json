{"name": "ai-coding-agent", "displayName": "AI Coding Agent", "description": "VS Code extension for AI Coding Agent that integrates with SWE-Agent and OAuth authentication", "version": "0.2.0", "engines": {"vscode": "^1.75.0"}, "categories": ["Other"], "activationEvents": ["*"], "icon": "icon.png", "main": "./out/extension.js", "contributes": {"commands": [{"command": "ai-coding-agent.runAgent", "title": "AI Coding Agent: Run Agent"}, {"command": "ai-coding-agent.stopAgent", "title": "AI Coding Agent: Stop Agent"}, {"command": "ai-coding-agent.checkStatus", "title": "AI Coding Agent: Check Status"}, {"command": "ai-coding-agent.openChat", "title": "AI Coding Agent: Open Chat"}, {"command": "ai-coding-agent.login", "title": "AI Coding Agent: Sign In"}, {"command": "ai-coding-agent.logout", "title": "AI Coding Agent: Sign Out"}, {"command": "ai-coding-agent.showAuthMenu", "title": "AI Coding Agent: Au<PERSON>nti<PERSON> <PERSON>u"}, {"command": "ai-coding-agent.showUserInfo", "title": "AI Coding Agent: User Information"}], "configuration": {"title": "AI Coding Agent", "properties": {"aiCodingAgent.bridgeHost": {"type": "string", "default": "localhost", "description": "Host address for the AI Coding Agent bridge"}, "aiCodingAgent.bridgePort": {"type": "number", "default": 8080, "description": "Port number for the AI Coding Agent bridge"}, "aiCodingAgent.modelName": {"type": "string", "default": "claude-sonnet-4-20250514", "description": "Model name to use for the AI Coding Agent"}, "aiCodingAgent.autoConnect": {"type": "boolean", "default": true, "description": "Automatically connect to the bridge on extension activation"}, "aiCodingAgent.enableWebSocket": {"type": "boolean", "default": true, "description": "Enable WebSocket connection for real-time updates"}, "aiCodingAgent.timeout": {"type": "number", "default": 30000, "description": "Request timeout in milliseconds"}, "aiCodingAgent.requireAuthentication": {"type": "boolean", "default": true, "description": "Require OAuth authentication for AI Coding Agent operations"}, "aiCodingAgent.autoRefreshTokens": {"type": "boolean", "default": true, "description": "Automatically refresh OAuth tokens when they expire"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/glob": "^7.1.4", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/vscode": "^1.60.0", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "eslint": "^8.1.0", "glob": "^7.1.7", "mocha": "^9.1.3", "typescript": "^4.4.4", "vscode-test": "^1.6.1"}, "dependencies": {"axios": "^0.24.0", "socket.io-client": "^4.4.0"}, "keywords": ["ai", "coding", "assistant", "swe-agent", "automation", "development", "productivity", "o<PERSON>h", "authentication"], "author": "AI Coding Agent Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/ai-coding-agent.git"}, "bugs": {"url": "https://github.com/your-org/ai-coding-agent/issues"}, "homepage": "https://github.com/your-org/ai-coding-agent#readme", "publisher": "ai-coding-agent"}