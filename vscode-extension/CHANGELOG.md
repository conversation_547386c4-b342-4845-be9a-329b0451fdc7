# Change Log

All notable changes to the AI Coding Agent VS Code extension will be documented in this file.

## [0.1.0] - 2025-06-05

### Added
- Initial release of AI Coding Agent VS Code Extension
- Real-time bridge communication via HTTP and WebSocket
- Session management (create, start, stop, monitor)
- Interactive chat interface for AI assistance
- File and workspace context integration
- Status bar integration with real-time updates
- Comprehensive error handling and logging
- Configuration management with auto-reconnection

### Features
- **Bridge Integration**: Full integration with AI Coding Agent bridge API
- **Session Management**: Create and manage AI agent sessions
- **Chat Interface**: Interactive chat view for conversational AI assistance
- **Context Awareness**: Sends current file, selection, and workspace context
- **Real-time Updates**: WebSocket integration for live progress updates
- **Status Monitoring**: Visual status indicators in VS Code status bar
- **Configuration**: Comprehensive settings for bridge connection and behavior

### Commands
- `AI Coding Agent: Run Agent` - Run the AI agent with a task description
- `AI Coding Agent: Stop Agent` - Stop the currently running AI agent task
- `AI Coding Agent: Check Status` - Check the status of the AI agent bridge
- `AI Coding Agent: Open Chat` - Open the interactive chat interface

### Configuration
- `aiCodingAgent.bridgeHost` - Bridge host address (default: localhost)
- `aiCodingAgent.bridgePort` - Bridge port number (default: 8080)
- `aiCodingAgent.modelName` - AI model to use (default: claude-3-opus-20240229)
- `aiCodingAgent.autoConnect` - Auto-connect on activation (default: true)
- `aiCodingAgent.enableWebSocket` - Enable real-time updates (default: true)
- `aiCodingAgent.timeout` - Request timeout in milliseconds (default: 30000)

### Technical Details
- TypeScript implementation with full type safety
- Axios for HTTP communication
- Socket.IO for WebSocket real-time updates
- Comprehensive error handling and recovery
- Integration with VS Code workspace API
- Follows VS Code extension best practices
