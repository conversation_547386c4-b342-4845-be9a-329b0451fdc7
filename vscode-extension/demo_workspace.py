"""
Demo workspace file for testing VS Code extension with unified server.
This file simulates a real development scenario.
"""

import math
from typing import List, Optional

class Calculator:
    """A simple calculator class for demonstration."""
    
    def __init__(self):
        self.history: List[str] = []
    
    def add(self, a: float, b: float) -> float:
        """Add two numbers."""
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def subtract(self, a: float, b: float) -> float:
        """Subtract two numbers."""
        result = a - b
        self.history.append(f"{a} - {b} = {result}")
        return result
    
    def multiply(self, a: float, b: float) -> float:
        """Multiply two numbers."""
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result
    
    def divide(self, a: float, b: float) -> Optional[float]:
        """Divide two numbers."""
        if b == 0:
            self.history.append(f"Error: Division by zero ({a} / {b})")
            return None
        result = a / b
        self.history.append(f"{a} / {b} = {result}")
        return result
    
    def power(self, base: float, exponent: float) -> float:
        """Calculate power."""
        result = math.pow(base, exponent)
        self.history.append(f"{base} ^ {exponent} = {result}")
        return result
    
    def sqrt(self, number: float) -> Optional[float]:
        """Calculate square root."""
        if number < 0:
            self.history.append(f"Error: Square root of negative number ({number})")
            return None
        result = math.sqrt(number)
        self.history.append(f"√{number} = {result}")
        return result
    
    def get_history(self) -> List[str]:
        """Get calculation history."""
        return self.history.copy()
    
    def clear_history(self) -> None:
        """Clear calculation history."""
        self.history.clear()

def fibonacci(n: int) -> int:
    """
    Calculate the nth Fibonacci number.
    TODO: This could be optimized with memoization.
    """
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

def factorial(n: int) -> int:
    """
    Calculate factorial of n.
    TODO: Add input validation.
    """
    if n == 0 or n == 1:
        return 1
    return n * factorial(n - 1)

def is_prime(number: int) -> bool:
    """
    Check if a number is prime.
    TODO: Optimize for large numbers.
    """
    if number < 2:
        return False
    for i in range(2, int(math.sqrt(number)) + 1):
        if number % i == 0:
            return False
    return True

# Demo usage
if __name__ == "__main__":
    calc = Calculator()
    
    # Basic operations
    print("Basic Calculator Demo:")
    print(f"5 + 3 = {calc.add(5, 3)}")
    print(f"10 - 4 = {calc.subtract(10, 4)}")
    print(f"6 * 7 = {calc.multiply(6, 7)}")
    print(f"15 / 3 = {calc.divide(15, 3)}")
    print(f"2 ^ 8 = {calc.power(2, 8)}")
    print(f"√16 = {calc.sqrt(16)}")
    
    print("\nMath Functions Demo:")
    print(f"Fibonacci(10) = {fibonacci(10)}")
    print(f"Factorial(5) = {factorial(5)}")
    print(f"Is 17 prime? {is_prime(17)}")
    
    print("\nCalculation History:")
    for entry in calc.get_history():
        print(f"  {entry}")
