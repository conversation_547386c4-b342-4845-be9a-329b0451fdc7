#!/usr/bin/env python3
"""
Unified AI Coding Agent Bridge API Server.
This script starts the consolidated server with all features:
- Enhanced session management with SessionManager
- Full WebSocket support for real-time updates
- Complete blueprint architecture (chat, completion, workspace, auth)
- VS Code extension compatibility
- Vim integration support
"""

import logging
import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def main():
    """Start the unified API server."""
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )
    logger = logging.getLogger(__name__)

    logger.info("🚀 Starting Unified AI Coding Agent Bridge API Server")
    logger.info("Features: Enhanced Sessions + WebSocket + Blueprints + Auth")

    try:
        # Import and start the unified API server
        from bridge.api.api_server import run_server

        logger.info("✅ All modules loaded successfully")
        logger.info("🌐 Server will be available at http://localhost:8080")
        logger.info("📡 WebSocket support enabled for real-time updates")
        logger.info("🔧 VS Code extension compatibility: READY")

        # Run in main thread with debug disabled for stability
        run_server(debug=False)

    except ImportError as e:
        logger.error(f"❌ Failed to import bridge modules: {e}")
        logger.error("Make sure you're in the correct directory and dependencies are installed")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"💥 Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
