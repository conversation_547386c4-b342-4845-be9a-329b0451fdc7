#!/usr/bin/env python3
"""
Test script for Phase 3 code completion functionality.
Tests the new completion bridge, context analysis, and Vim integration.
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path

# Add the bridge to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from bridge.integrations.code_completion import completion_engine, CompletionContext
from bridge.core.context_manager import context_analyzer
from bridge.integrations.vim_client import send_command

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_context_analyzer():
    """Test the context analyzer functionality."""
    print("\n=== Testing Context Analyzer ===")
    
    # Test Python file analysis
    python_code = '''
import os
import sys
from typing import List, Dict

def fibonacci(n: int) -> int:
    """Calculate the nth Fibonacci number."""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

class Calculator:
    """A simple calculator class."""
    
    def __init__(self):
        self.history = []
    
    def add(self, a: float, b: float) -> float:
        """Add two numbers."""
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def multiply(self, a: float, b: float) -> float:
        """Multiply two numbers."""
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result

if __name__ == "__main__":
    calc = Calculator()
    print(calc.add(5, 3))
'''
    
    try:
        # Analyze the Python file
        context = context_analyzer.analyze_file("test.py", python_code, "python")
        
        print(f"✓ File analyzed: {context.file_path}")
        print(f"✓ Language detected: {context.language}")
        print(f"✓ Symbols found: {len(context.symbols)}")
        print(f"✓ Imports found: {len(context.imports)}")
        
        # Print symbols
        for symbol in context.symbols:
            print(f"  - {symbol.type.value}: {symbol.name} (line {symbol.line_start})")
        
        # Test context at position
        symbol = context_analyzer.get_context_at_position("test.py", 15, 10)
        if symbol:
            print(f"✓ Context at line 16: {symbol.name} ({symbol.type.value})")
        
        # Test symbols in scope
        symbols = context_analyzer.get_symbols_in_scope("test.py", 20)
        print(f"✓ Symbols in scope at line 21: {len(symbols)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Context analyzer test failed: {e}")
        return False


async def test_completion_engine():
    """Test the completion engine functionality."""
    print("\n=== Testing Completion Engine ===")
    
    # Test completion context
    test_code = '''
def calculate_area(radius):
    """Calculate the area of a circle."""
    import math
    return math.pi * radius ** 2

def main():
    r = 5
    area = calc
'''
    
    try:
        # Create completion context
        context = CompletionContext(
            file_path="test.py",
            content=test_code,
            cursor_line=8,
            cursor_column=15,
            language="python"
        )
        
        # Get completions
        response = await completion_engine.get_completions(context)
        
        print(f"✓ Completion request processed in {response.processing_time_ms}ms")
        print(f"✓ Found {len(response.items)} completion suggestions")
        
        # Print top suggestions
        for i, item in enumerate(response.items[:5]):
            print(f"  {i+1}. {item.text} ({item.kind}) - priority: {item.priority}")
        
        # Test cache
        cached_response = completion_engine.cache.get(context)
        if cached_response:
            print("✓ Completion cached successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Completion engine test failed: {e}")
        return False


def test_vim_integration():
    """Test the enhanced Vim integration."""
    print("\n=== Testing Vim Integration ===")
    
    test_code = '''
def hello_world():
    print("Hello, World!")
    return "success"

class TestClass:
    def __init__(self):
        self.value = 42
'''
    
    try:
        # Test ping command
        response = send_command("ping")
        if response.get("status") == "success":
            print("✓ Vim integration server is running")
        else:
            print("✗ Vim integration server not responding")
            return False
        
        # Test analyze command
        response = send_command(
            "analyze",
            file_path="test.py",
            content=test_code,
            language="python"
        )
        
        if response.get("status") == "success":
            analysis = response.get("analysis", {})
            symbols = analysis.get("symbols", [])
            print(f"✓ File analysis successful: {len(symbols)} symbols found")
        else:
            print(f"✗ File analysis failed: {response.get('message', 'Unknown error')}")
            return False
        
        # Test completion command
        response = send_command(
            "complete",
            file_path="test.py",
            content=test_code,
            cursor_line=2,
            cursor_column=10,
            language="python"
        )
        
        if response.get("status") == "success":
            completions = response.get("completions", {})
            items = completions.get("items", [])
            print(f"✓ Code completion successful: {len(items)} suggestions")
        else:
            print(f"✗ Code completion failed: {response.get('message', 'Unknown error')}")
            return False
        
        # Test context command
        response = send_command(
            "context",
            file_path="test.py",
            line=1,
            column=5
        )
        
        if response.get("status") == "success":
            context = response.get("context")
            if context:
                print(f"✓ Context retrieval successful: {context.get('name')} ({context.get('type')})")
            else:
                print("✓ Context retrieval successful: no symbol at position")
        else:
            print(f"✗ Context retrieval failed: {response.get('message', 'Unknown error')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Vim integration test failed: {e}")
        return False


def test_performance():
    """Test performance of completion system."""
    print("\n=== Testing Performance ===")
    
    large_code = '''
import os
import sys
import json
import time
import asyncio
from typing import List, Dict, Optional, Any

''' + '\n'.join([f"def function_{i}():\n    return {i}" for i in range(100)])
    
    try:
        start_time = time.time()
        
        # Test multiple completion requests
        for i in range(10):
            context = CompletionContext(
                file_path=f"test_{i}.py",
                content=large_code,
                cursor_line=50 + i,
                cursor_column=10,
                language="python"
            )
            
            # Run completion
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                response = loop.run_until_complete(completion_engine.get_completions(context))
            finally:
                loop.close()
        
        total_time = time.time() - start_time
        avg_time = total_time / 10
        
        print(f"✓ Performance test completed")
        print(f"✓ Total time for 10 requests: {total_time:.2f}s")
        print(f"✓ Average time per request: {avg_time:.2f}s")
        
        # Test cache performance
        cache_size = len(completion_engine.cache.cache)
        print(f"✓ Cache entries: {cache_size}")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance test failed: {e}")
        return False


def main():
    """Run all Phase 3 completion tests."""
    print("Starting Phase 3 Code Completion Tests")
    print("=" * 50)
    
    tests = [
        ("Context Analyzer", test_context_analyzer),
        ("Completion Engine", lambda: asyncio.run(test_completion_engine())),
        ("Vim Integration", test_vim_integration),
        ("Performance", test_performance)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} test PASSED")
            else:
                print(f"✗ {test_name} test FAILED")
        except Exception as e:
            print(f"✗ {test_name} test ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Phase 3 completion tests PASSED!")
        return 0
    else:
        print("❌ Some Phase 3 completion tests FAILED!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
