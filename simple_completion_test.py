#!/usr/bin/env python3
"""
Simple test for Phase 3.1 completion functionality.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

def test_context_analyzer():
    """Test context analyzer with simple code."""
    print("Testing Context Analyzer...")
    
    try:
        from bridge.core.context_manager import ContextAnalyzer
        
        analyzer = ContextAnalyzer()
        
        # Simple Python code
        code = '''
def hello_world():
    """Say hello to the world."""
    print("Hello, World!")
    return "success"

class Calculator:
    def add(self, a, b):
        return a + b
'''
        
        # Analyze the code
        context = analyzer.analyze_file("test.py", code, "python")
        
        print(f"✓ File: {context.file_path}")
        print(f"✓ Language: {context.language}")
        print(f"✓ Symbols found: {len(context.symbols)}")
        
        for symbol in context.symbols:
            print(f"  - {symbol.type.value}: {symbol.name} (line {symbol.line_start})")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_completion_basic():
    """Test basic completion functionality."""
    print("\nTesting Basic Completion...")
    
    try:
        from bridge.integrations.code_completion import CompletionContext, CompletionEngine
        
        engine = CompletionEngine()
        
        # Simple completion context
        context = CompletionContext(
            file_path="test.py",
            content="def hello():\n    pri",
            cursor_line=1,
            cursor_column=7,
            language="python"
        )
        
        print(f"✓ Context created for {context.file_path}")
        print(f"✓ Cursor at line {context.cursor_line}, column {context.cursor_column}")
        
        # Test language-specific completions (synchronous)
        completions = engine._get_language_completions(context)
        print(f"✓ Language completions: {len(completions)} found")
        
        for completion in completions[:3]:
            print(f"  - {completion.text} ({completion.kind})")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run simple tests."""
    print("Phase 3.1 Simple Completion Test")
    print("=" * 40)
    
    tests = [
        test_context_analyzer,
        test_completion_basic
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"Test failed with exception: {e}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
